document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const detailChestTitle = document.getElementById('detail-chest-title');
    const detailChestId = document.getElementById('detail-chest-id');
    const detailSeller = document.getElementById('detail-seller');
    const detailOncePrice = document.getElementById('detail-once-price');
    const detailMultiDraw = document.getElementById('detail-multi-draw');
    const detailMultiPrice = document.getElementById('detail-multi-price');
    const detailRemain = document.getElementById('detail-remain');
    const detailTotal = document.getElementById('detail-total');
    const detailLoading = document.getElementById('detail-loading');
    const detailError = document.getElementById('detail-error');
    const chestBasicInfo = document.getElementById('chest-basic-info');
    const chestGamesList = document.getElementById('chest-games-list');
    const refreshChestBtn = document.getElementById('refresh-chest-btn');
    const calculateEvBtn = document.getElementById('calculate-ev-btn');
    const chestEvResult = document.getElementById('chest-ev-result');
    const logsContent = document.getElementById('logs-content');
    const favoriteChestBtn = document.getElementById('favorite-chest-btn'); // 收藏按钮
    const backToListBtn = document.getElementById('back-to-list-btn'); // 返回列表按钮
    
    // 确保日志加载指示器状态正确
    const logsLoadingIndicator = document.querySelector('#chest-logs-container .loading-indicator');
    if (logsLoadingIndicator) {
        logsLoadingIndicator.style.display = 'none';
    }

    // 添加计算期望按钮的提示
    if (calculateEvBtn) {
        // 添加tooltip
        calculateEvBtn.setAttribute('data-tooltip', '计算期望值');
    }
    
    // 处理返回路径参数
    if (backToListBtn) {
        const urlParams = new URLSearchParams(window.location.search);
        const returnPath = urlParams.get('return_path');
        
        if (returnPath) {
            // 解码并设置返回链接
            const returnUrl = `/chests${decodeURIComponent(returnPath)}`;
            backToListBtn.href = returnUrl;
            console.log('返回链接设置为:', returnUrl);
            
            // 检查是否是从肥皂盒列表页来的
            const referrer = document.referrer;
            const isFromChestsList = referrer && referrer.includes('/chests');
            
            // 增加调试日志
            console.log('来源页面:', referrer);
            console.log('是否来自肥皂盒列表:', isFromChestsList);
            
            // 设置返回按钮点击事件
            backToListBtn.addEventListener('click', function(e) {
                if (isFromChestsList) {
                    // 如果来自列表页，使用浏览器回退
                    e.preventDefault();
                    console.log('使用浏览器回退');
                    window.history.back();
                } else {
                    // 否则使用返回链接
                    console.log('使用返回链接:', returnUrl);
                    // 不阻止默认行为，让链接正常工作
                }
            });
        }
    }
    
    // 游戏模板
    const poolTemplate = document.getElementById('pool-template');
    const gameCardTemplate = document.getElementById('game-card-template');
    
    // 添加旋转动画样式
    if (!document.getElementById('rotate-animation-style')) {
        const style = document.createElement('style');
        style.id = 'rotate-animation-style';
        style.innerHTML = `
            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .rotating {
                animation: rotate 1s linear infinite;
            }
        `;
        document.head.appendChild(style);
    }
    
    // 当前肥皂盒数据
    let currentChestDetail = null;
    let currentChestGames = null;
    
    // 下单锁定状态
    let orderLocked = true;
    
    // 下单配置预加载
    let orderConfig = {
        isUsingMainAccount: localStorage.getItem('chestUseMainAccount') !== 'false',
        payType: localStorage.getItem('chestPayType') || 'py',
        useBalance: localStorage.getItem('chestUseBalance'),
        isPreloaded: true
    };
    
    // 预加载下单API状态检查
    let orderApiStatus = { available: false, checked: false };
    
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const chestId = urlParams.get('id');
    
    // 防止重复请求标志
    let isLoadingData = false;
    let isLoadingLogs = false;
    
    // 收藏相关常量
    const FAVORITE_CHESTS_KEY = 'favorite_chests';
    
    // 添加全局变量来存储上次库存数据
    let previousStockData = {};

    // 添加遮罩层函数
    const addOverlay = () => {
        // 如果已存在遮罩层，先移除
        removeOverlay();
        
        // 创建遮罩层
        const overlay = document.createElement('div');
        overlay.id = 'chest-loading-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            backdrop-filter: blur(2px);
            transition: opacity 0.3s ease;
        `;
        
        // 添加加载动画
        const spinner = document.createElement('div');
        spinner.className = 'spin';
        spinner.style.width = '40px';
        spinner.style.height = '40px';
        
        overlay.appendChild(spinner);
        
        // 添加到页面主体部分
        const mainSection = document.querySelector('.detail-main-section .card');
        if (mainSection) {
            // 确保主体有相对定位
            mainSection.style.position = 'relative';
            mainSection.appendChild(overlay);
        }
    };

    // 移除遮罩层函数
    const removeOverlay = () => {
        const existingOverlay = document.getElementById('chest-loading-overlay');
        if (existingOverlay) {
            // 使用淡出效果
            existingOverlay.style.opacity = '0';
            setTimeout(() => {
                existingOverlay.remove();
            }, 300);
        }
    };

    // 页面加载时获取肥皂盒详情
    if (chestId) {
        // 预加载下单API状态
        preloadOrderApi();
        
        // 添加定期更新预加载配置的定时器
        const configUpdateInterval = setInterval(updateOrderConfig, 30000); // 每30秒更新一次
        
        // 页面卸载时清除定时器
        window.addEventListener('beforeunload', function() {
            clearInterval(configUpdateInterval);
        });
        
        loadChestDetail(chestId);
        
        // 初始化锁定按钮
        initLockButton();
        
        // 添加刷新按钮事件
        if (refreshChestBtn) {
            refreshChestBtn.addEventListener('click', function() {
                loadChestDetail(chestId);
                loadChestLogs(chestId);
                // 同时更新下单配置
                updateOrderConfig();
            });
        }
        
        // 添加收藏按钮事件
        if (favoriteChestBtn) {
            // 初始化收藏按钮状态
            updateFavoriteButtonState();
            
            favoriteChestBtn.addEventListener('click', function() {
                if (!currentChestDetail) {
                    showToast('请等待肥皂盒数据加载完成', 'error');
                    return;
                }
                
                // 切换收藏状态
                const newState = toggleFavoriteChest(currentChestDetail);
                
                // 更新按钮外观
                updateFavoriteButtonUI(newState);
            });
        }
        
        // 添加计算期望按钮事件
        if (calculateEvBtn) {
            calculateEvBtn.addEventListener('click', async function() {
                if (!currentChestDetail || !currentChestGames || currentChestGames.length === 0) {
                    showToast('请先等待肥皂盒数据和游戏列表加载完成', 'error');
                    return;
                }
                // 显示期望计算结果区域
                if (chestEvResult) chestEvResult.style.display = 'block';
                // 显示加载中
                const evResultContent = document.getElementById('ev-result-content');
                if (evResultContent) evResultContent.innerHTML = '<p>正在计算期望值，可能需要获取多个游戏价格...</p>';
                // 执行期望计算
                await calculateChestExpectation(currentChestDetail, currentChestGames);
            });
        }
        
        // 添加抽取按钮事件
        const onceDrawBtn = document.querySelector('.once-draw-btn');
        const multiDrawBtn = document.querySelector('.multi-draw-btn');
        
        // 单抽按钮
        if (onceDrawBtn) {
            onceDrawBtn.addEventListener('click', async function() {
                await placeChestOrder(chestId, 1);
            });
        }
        
        // 多抽按钮
        if (multiDrawBtn) {
            multiDrawBtn.addEventListener('click', async function() {
                // 获取多抽次数
                const multiDrawCount = parseInt(document.getElementById('detail-multi-draw')?.textContent || '3');
                await placeChestOrder(chestId, multiDrawCount);
            });
        }
        
        // 添加刷新日志按钮事件
        const refreshLogsBtn = document.getElementById('refresh-logs-btn');
        if (refreshLogsBtn) {
            refreshLogsBtn.addEventListener('click', async function() {
                // 添加旋转动画
                this.classList.add('rotating');
                try {
                    // 重新加载活动记录
                    await loadChestLogs(chestId);
                } finally {
                    // 无论成功或失败，1秒后移除旋转动画
                    setTimeout(() => {
                        this.classList.remove('rotating');
                    }, 1000);
                }
            });
        }
    } else {
        showDetailError('未提供有效的肥皂盒ID');
    }
    
    // 加载肥皂盒详情和游戏列表
    async function loadChestDetail(chestId) {
        if (isLoadingData) return;
        
        isLoadingData = true;
        
        try {
            // 添加遮罩层
            addOverlay();
            
            if (detailError) detailError.style.display = 'none';
            
            // 使用Promise.all并行请求数据
            const [detailData, gamesData] = await Promise.all([
                fetchChestDetail(chestId),
                fetchChestGames(chestId)
            ]);
            
            if (!detailData) {
                throw new Error('获取详情失败');
            }
            
            // 设置标题
            if (detailChestTitle) {
                detailChestTitle.textContent = detailData.name || '肥皂盒详情';
            }
            
            // 填充基本信息
            fillChestBasicInfo(detailData);
            
            // 提取概率信息
            const rateInfo = {
                rate1: detailData.rate1,
                rate2: detailData.rate2,
                rate3: detailData.rate3,
                rate4: detailData.rate4
            };
            
            // 记录当前库存数据，用于比较变化
            const currentStockData = {};
            gamesData.forEach(game => {
                if (game.gameId) {
                    currentStockData[game.gameId] = {
                        stock: Number(game.stock) || 0,
                        name: game.gameNameCn || '未知游戏'
                    };
                }
            });
            
            // 填充游戏列表，传入上次库存数据用于比较
            fillChestGames(gamesData, rateInfo, previousStockData);
            
            // 更新上次库存数据为当前数据
            previousStockData = currentStockData;
            
            // 显示内容
            if (detailLoading) detailLoading.style.display = 'none';
            if (chestBasicInfo && chestBasicInfo.style.display !== 'block') chestBasicInfo.style.display = 'block';
            if (chestGamesList && chestGamesList.style.display !== 'block') chestGamesList.style.display = 'block';
            
            // 保存当前数据用于期望计算
            currentChestDetail = detailData;
            currentChestGames = gamesData;
            
            // 加载当前肥皂盒的抽取记录
            loadChestLogs(chestId);
            
            // 移除遮罩层
            setTimeout(removeOverlay, 300);
            
        } catch (error) {
            console.error('获取肥皂盒详情失败:', error);
            showDetailError(error.message || '获取肥皂盒详情失败');
            removeOverlay();
        } finally {
            isLoadingData = false;
        }
    }
    
    /**
     * 获取肥皂盒详情
     * @param {string} chestId - 肥皂盒ID
     */
    async function fetchChestDetail(chestId) {
        const url = `/api/xboot/chest/showOne?chestId=${chestId}`;
        
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
            throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
        }
        
        const data = await response.json();
        if (data.code !== 200) {
            throw new Error(data.message || '获取详情失败');
        }
        
        return data.result;
    }
    
    /**
     * 获取肥皂盒游戏列表
     * @param {string} chestId - 肥皂盒ID
     */
    async function fetchChestGames(chestId) {
        const url = `/api/xboot/chest/showGame?chestId=${chestId}&pageNumber=1&pageSize=100&sort=lv&order=asc`;
        
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
            throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
        }
        
        const data = await response.json();
        if (data.code !== 200) {
            throw new Error(data.message || '获取游戏列表失败');
        }
        
        return data.result.content || [];
    }
    
    /**
     * 获取指定游戏ID的缓存价格
     * @param {Array} gameIds - 要获取价格的游戏ID数组
     * @returns {Promise<Object>} 游戏ID到价格的映射
     */
    async function fetchCachedPrices(gameIds) {
        if (!gameIds || !gameIds.length) {
            return {};
        }
        
        try {
            // 构建查询参数，只获取需要的游戏ID的缓存价格
            const queryParams = gameIds.map(id => `gameIds=${encodeURIComponent(id)}`).join('&');
            
            // 请求缓存价格，只获取指定ID的价格
            const response = await fetch(`/api/games/cached-prices?${queryParams}`);
            if (!response.ok) {
                throw new Error(`获取缓存价格失败: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 将数组转换为以游戏ID为键的对象
            const priceMap = {};
            if (Array.isArray(data)) {
                data.forEach(item => {
                    if (item && item.game_id && item.price !== undefined) {
                        priceMap[item.game_id] = Number(item.price);
                    }
                });
            }
            
            return priceMap;
        } catch (error) {
            console.error("获取缓存价格时出错:", error);
            return {}; // 返回空对象
        }
    }
    
    /**
     * 获取游戏价格
     * @param {string} gameId - 游戏ID
     * @param {string} gameName - 游戏名称，用于日志（可选）
     * @returns {Promise<number>} 游戏价格
     */
    async function fetchGamePrice(gameId, gameName = null) {
        if (!gameId) {
            console.error('fetchGamePrice: 游戏ID为空');
            return 0;
        }

        try {
            // 直接使用现有的sellers API，但只获取一个卖家信息
            const url = `/api/games/${gameId}/sellers?pageSize=1`;
            console.log(`正在获取游戏 ${gameName || gameId} 的价格...`);
            
            const response = await fetch(url);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }));
                console.error(`获取游戏价格失败: ${errorData.detail || '请求失败'}`);
                return 0;
            }
            
            const sellersData = await response.json();
            
            // 检查返回的卖家数据
            if (Array.isArray(sellersData) && sellersData.length > 0) {
                const firstSeller = sellersData[0];
                if (firstSeller && typeof firstSeller.keyPrice === 'number') {
                    console.log(`获取到游戏 ${gameName || gameId} 的价格: ${firstSeller.keyPrice}`);
                    return firstSeller.keyPrice;
                }
            }
            
            console.warn(`没有找到游戏 ${gameName || gameId} 的有效价格`);
            return 0;
        } catch (error) {
            console.error(`获取游戏价格时出错: ${error.message || error}`);
            return 0;
        }
    }
    
    /**
     * 更新后端价格缓存
     * @param {string} gameId - 游戏ID
     * @param {string} gameName - 游戏名称
     * @param {number} price - 游戏价格
     * @returns {Promise<boolean>} 是否更新成功
     */
    async function updatePriceInBackendCache(gameId, gameName, price) {
        if (!gameId || !gameName || price === undefined || price === null) {
            console.warn('updatePriceInBackendCache: 参数不完整', { gameId, gameName, price });
            return false;
        }
        
        try {
            const response = await fetch('/api/games/cache-price', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ game_id: gameId, name: gameName, price: price })
            });
            
            if (!response.ok) {
                console.error(`更新价格缓存失败: 状态码 ${response.status}`);
                return false;
            }
            
            const data = await response.json();
            return data.success === true;
        } catch (error) {
            console.error(`更新价格缓存时出错: ${error.message || error}`);
            return false;
        }
    }
    
    /**
     * 获取肥皂盒抽取记录
     * @param {string} chestId - 肥皂盒ID
     */
    async function loadChestLogs(chestId) {
        if (isLoadingLogs || !logsContent) return;
        
        isLoadingLogs = true;
        
        // 为日志区域添加遮罩层
        const addLogsOverlay = () => {
            // 如果已存在遮罩层，先移除
            removeLogsOverlay();
            
            // 创建遮罩层
            const overlay = document.createElement('div');
            overlay.id = 'logs-loading-overlay';
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(255, 255, 255, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 100;
                backdrop-filter: blur(1px);
                transition: opacity 0.3s ease;
                border-radius: 12px;
            `;
            
            // 添加加载动画
            const spinner = document.createElement('div');
            spinner.className = 'spin';
            spinner.style.width = '20px';
            spinner.style.height = '20px';
            
            overlay.appendChild(spinner);
            
            // 添加到日志区域
            const logsContainer = document.querySelector('#chest-logs-container');
            if (logsContainer) {
                // 确保容器有相对定位
                logsContainer.style.position = 'relative';
                logsContainer.appendChild(overlay);
            }
        };
        
        // 移除日志遮罩层
        const removeLogsOverlay = () => {
            const existingOverlay = document.getElementById('logs-loading-overlay');
            if (existingOverlay) {
                // 使用淡出效果
                existingOverlay.style.opacity = '0';
                setTimeout(() => {
                    existingOverlay.remove();
                }, 300);
            }
        };
        
        try {
            // 添加遮罩层
            addLogsOverlay();
            
            console.log('开始加载活动记录，当前肥皂盒ID:', chestId);
            
            // 构建API URL - 使用原有正确的API路径
            const url = '/api/chests/xboot/detLog/show';
            const params = new URLSearchParams();
            if (chestId) {
                params.append('chestId', chestId);
            }
            params.append('pageNumber', 1);
            params.append('pageSize', 20);
            params.append('sort', 'createTime');
            params.append('order', 'desc');

            const fullUrl = `${url}?${params.toString()}`;
            console.log('API请求URL:', fullUrl);
            
            // 发送API请求
            const response = await fetch(fullUrl);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
                throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 检查API返回数据格式
            let logsData = null;
            if (data && data.result && Array.isArray(data.result.content)) {
                logsData = data.result.content;
            } else if (data && data.result && Array.isArray(data.result.records)) {
                logsData = data.result.records;
            } else if (data && data.data && Array.isArray(data.data.records)) {
                logsData = data.data.records;
            } else {
                console.log('未找到有效的记录数据');
                logsContent.innerHTML = '<div style="padding: 12px; color: rgba(255,255,255,0.7); text-align: center;">暂无抽取记录</div>';
                return;
            }
            
            if (logsData && logsData.length > 0) {
                // 渲染记录
                renderChestLogs(logsData);
            } else {
                logsContent.innerHTML = '<div style="padding: 12px; color: rgba(255,255,255,0.7); text-align: center;">暂无抽取记录</div>';
            }
            
        } catch (error) {
            console.error('获取肥皂盒抽取记录失败:', error);
            logsContent.innerHTML = `<div style="padding: 12px; color: rgba(255,255,255,0.7); text-align: center;">获取记录失败: ${error.message || '未知错误'}</div>`;
        } finally {
            isLoadingLogs = false;
            // 移除遮罩层
            setTimeout(removeLogsOverlay, 300);
        }
    }
    
    /**
     * 渲染抽取记录
     * @param {Array} logs - 肥皂盒记录数据数组
     */
    function renderChestLogs(logs) {
        // 获取新的列表容器
        const logsContent = document.getElementById('logs-content');
        if (!logsContent) {
            console.error('找不到logs-content元素');
            return;
        }
        
        // 清空现有内容
        logsContent.innerHTML = '';
        
        // 检查是否有记录
        if (!logs || logs.length === 0) {
            logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
            return;
        }
        
        // 显示全部记录
        const recentLogsList = document.createElement('div');
        recentLogsList.className = 'activity-list';
        
        logs.forEach((log) => {
            // 创建记录项
            const logItem = document.createElement('div');
            logItem.style.display = 'flex';
            logItem.style.alignItems = 'center';
            logItem.style.padding = '8px 16px';
            logItem.style.marginBottom = '8px';
            
            // 根据等级选择图标内容和颜色
            let iconContent = '4';
            let iconBackground = '#2196f3';
            const lv = log.lv || 4;
            
            if (lv === 1) {
                iconContent = '1';
                iconBackground = '#ff8c00';
            } else if (lv === 2) {
                iconContent = '2';
                iconBackground = '#9370db';
            } else if (lv === 3) {
                iconContent = '3';
                iconBackground = '#4682b4';
            }
            
            // 创建图标容器
            const iconContainer = document.createElement('div');
            iconContainer.style.width = '32px';
            iconContainer.style.height = '32px';
            iconContainer.style.backgroundColor = iconBackground;
            iconContainer.style.borderRadius = '50%';
            iconContainer.style.display = 'flex';
            iconContainer.style.justifyContent = 'center';
            iconContainer.style.alignItems = 'center';
            iconContainer.style.color = '#fff';
            iconContainer.style.fontWeight = '600';
            iconContainer.style.fontSize = '14px';
            iconContainer.style.marginRight = '10px';
            iconContainer.style.flexShrink = '0';
            iconContainer.textContent = iconContent;
            
            // 创建内容容器
            const contentContainer = document.createElement('div');
            contentContainer.style.flex = '1';
            
            // 游戏名称
            const gameName = document.createElement('div');
            gameName.style.fontSize = '14px';
            gameName.style.fontWeight = '500';
            gameName.style.marginBottom = '2px';
            gameName.textContent = log.gameName || '未知游戏';
            
            // 等级标签
            const levelLabel = document.createElement('div');
            levelLabel.style.fontSize = '12px';
            levelLabel.style.color = 'rgba(255,255,255,0.7)';
            levelLabel.textContent = `—${getLevelName(log.lv)}`;
            
            // 添加到内容容器
            contentContainer.appendChild(gameName);
            contentContainer.appendChild(levelLabel);
            
            // 创建时间容器
            const timeContainer = document.createElement('div');
            timeContainer.style.fontSize = '12px';
            timeContainer.style.color = 'rgba(255,255,255,0.7)';
            timeContainer.style.textAlign = 'right';
            timeContainer.style.flexShrink = '0';
            
            // 格式化时间
            const date = new Date(log.createTime);
            const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
            timeContainer.textContent = formattedDate;
            
            // 组装记录项
            logItem.appendChild(iconContainer);
            logItem.appendChild(contentContainer);
            logItem.appendChild(timeContainer);
            
            // 添加到列表
            recentLogsList.appendChild(logItem);
        });
        
        logsContent.appendChild(recentLogsList);
    }
    
    /**
     * 根据等级获取等级名称
     */
    function getLevelName(level) {
        switch (level) {
            case 1:
                return '传说';
            case 2:
                return '史诗';
            case 3:
                return '稀有';
            default:
                return '普通';
        }
    }
    
    /**
     * 显示详情错误信息
     * @param {string} message - 错误信息
     */
    function showDetailError(message) {
        if (detailLoading) detailLoading.style.display = 'none';
        if (chestBasicInfo) chestBasicInfo.style.display = 'none';
        if (chestGamesList) chestGamesList.style.display = 'none';
        if (chestEvResult) chestEvResult.style.display = 'none';
        
        if (detailError) {
            const errorElement = detailError.querySelector('p');
            if (errorElement) {
                errorElement.textContent = message || '获取详情失败，请稍后再试。';
            }
            
            detailError.style.display = 'block';
        }
    }
    
    /**
     * 填充肥皂盒基本信息到UI
     * @param {Object} detail - 肥皂盒详情数据
     */
    function fillChestBasicInfo(detail) {
        if (!detail) {
            showDetailError('获取到的肥皂盒详情无效');
            return;
        }
        
        // 标题和ID
        if (detailChestTitle) detailChestTitle.textContent = detail.name || '未知肥皂盒';
        if (detailChestId) detailChestId.textContent = detail.id || '';
        
        // 卖家
        if (detailSeller) detailSeller.textContent = detail.nameSeller || '未知卖家';
        
        // 价格信息
        const oncePrice = detail.oncePrice || 0;
        const multiDraw = detail.multidraw || 3;
        const multiPrice = detail.multiPrice || (oncePrice * multiDraw);
        
        if (detailOncePrice) detailOncePrice.textContent = oncePrice.toFixed(2);
        if (detailMultiDraw) detailMultiDraw.textContent = multiDraw;
        if (detailMultiPrice) detailMultiPrice.textContent = multiPrice.toFixed(2);
        
        // 更新按钮上的价格显示
        const oncePriceDisplay = document.getElementById('once-price-display');
        const multiDrawDisplay = document.getElementById('multi-draw-display');
        const multiPriceDisplay = document.getElementById('multi-price-display');
        
        if (oncePriceDisplay) oncePriceDisplay.textContent = oncePrice.toFixed(2);
        if (multiDrawDisplay) multiDrawDisplay.textContent = multiDraw;
        if (multiPriceDisplay) multiPriceDisplay.textContent = multiPrice.toFixed(2);
        
        // 抽取情况
        const curDraw = detail.curDraw || 0;
        const totalDraw = detail.totalDraw || 0;
        const remainDraw = totalDraw - curDraw;
        
        if (detailRemain) detailRemain.textContent = remainDraw;
        if (detailTotal) detailTotal.textContent = totalDraw;
        
        // 保存当前肥皂盒详情
        currentChestDetail = detail;
        
        // 更新收藏按钮状态
        if (favoriteChestBtn) {
            updateFavoriteButtonState();
        }
        
        // 显示详情区域
        if (chestBasicInfo) chestBasicInfo.style.display = 'block';
    }
    
    /**
     * 填充肥皂盒游戏列表
     * @param {Array} games - 肥皂盒中的游戏数据
     * @param {Object} rateInfo - 概率信息对象
     */
    function fillChestGames(games, rateInfo, previousStockData = {}) {
        if (!games || !games.length || !chestGamesList) return;
        
        // 清空容器
        chestGamesList.innerHTML = '';
        
        // 转换概率函数 - 使用Intl.NumberFormat优化性能
        const percentFormatter = new Intl.NumberFormat('zh-CN', {
            style: 'percent',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        
        const smallPercentFormatter = new Intl.NumberFormat('zh-CN', {
            style: 'percent',
            minimumFractionDigits: 4,
            maximumFractionDigits: 4
        });
        
        const formatRate = (rateValue) => {
            if (rateValue === null || rateValue === undefined) return "未知";
            
            let rateNum = parseFloat(rateValue);
            if (isNaN(rateNum)) return "未知";
            
            if (rateNum > 1) {
                return `${rateNum.toFixed(2)}%`;
            } else if (rateNum < 0.0001 && rateNum > 0) {
                return rateNum.toExponential(2);
            } else if (rateNum === 0) {
                return "0"; // 概率为0时显示0
            } else if (rateNum < 0.001) {
                return smallPercentFormatter.format(rateNum);
            } else {
                return percentFormatter.format(rateNum);
            }
        };
        
        // 使用DocumentFragment优化DOM操作
        const fragment = document.createDocumentFragment();
        
        // 按等级分组游戏
        const gamesByLevel = {
            1: [], // 传说/传奇
            2: [], // 史诗
            3: [], // 稀有
            4: []  // 普通
        };
        
        // 处理游戏数据
        games.forEach(game => {
            const level = game.lv || 4;  // 默认为普通
            if (gamesByLevel[level]) {
                gamesByLevel[level].push(game);
            }
        });
        
        // 获取肥皂盒单抽价格
        let chestOncePrice = 0;
        try {
            chestOncePrice = parseFloat(detailOncePrice?.textContent?.replace('¥', '') || 90);
        } catch (e) {
            console.warn("无法正确解析肥皂盒价格，使用默认值90");
            chestOncePrice = 90;
        }
        
        // 批量获取缓存价格
        const gameIds = games.map(game => game.gameId).filter(Boolean);
        
        // 使用Promise.all优化异步操作
        fetchCachedPrices(gameIds)
            .then(cachedPrices => renderGamePools(cachedPrices, chestOncePrice))
            .catch(error => {
                console.error("获取缓存价格失败:", error);
                renderGamePools({}, chestOncePrice);
            });
        
        // 渲染游戏池函数
        function renderGamePools(cachedPrices, chestOncePrice) {
            // 按等级分组游戏 (将等级视为池子类型)
            const poolMap = {
                "1": { name: "传说池", class: "legendary-pool", probability: formatRate(rateInfo.rate1) },
                "2": { name: "史诗池", class: "epic-pool", probability: formatRate(rateInfo.rate2) },
                "3": { name: "稀有池", class: "rare-pool", probability: formatRate(rateInfo.rate3) },
                "4": { name: "普通池", class: "common-pool", probability: formatRate(rateInfo.rate4) }
            };
            
            // 创建一个文档片段，减少DOM重绘
            const fragment = document.createDocumentFragment();
            
            // 清空游戏列表容器
            chestGamesList.innerHTML = '';
            
            // 按等级顺序渲染游戏池
            Object.keys(poolMap).sort().forEach(level => {
                // 跳过没有游戏的池子
                if (!gamesByLevel[level] || gamesByLevel[level].length === 0) return;
                
                // 创建池子容器
                const poolContainer = document.createElement('div');
                poolContainer.className = `game-pool ${getLevelClassName(level)}`;
                
                // 创建池子标题
                const poolTitle = document.createElement('div');
                poolTitle.className = 'pool-title';
                
                // 池子名称
                const poolName = document.createElement('span');
                poolName.className = 'pool-name';
                poolName.textContent = getLevelPoolName(level);
                
                // 池子概率 - 确保概率显示清晰
                const poolProbability = document.createElement('span');
                poolProbability.className = 'pool-probability';
                
                // 获取该等级的概率
                const rateKey = `rate${level}`;
                const rateValue = rateInfo[rateKey];
                
                // 格式化概率显示
                if (rateValue !== undefined && rateValue !== null) {
                    let formattedRate;
                    const rateNum = parseFloat(rateValue);
                    
                    if (!isNaN(rateNum)) {
                        if (rateNum > 1) {
                            formattedRate = `概率: ${rateNum.toFixed(2)}%`;
                        } else if (rateNum === 0) {
                            formattedRate = "概率: 0%";
                        } else {
                            formattedRate = `概率: ${(rateNum * 100).toFixed(2)}%`;
                        }
                    } else {
                        formattedRate = "概率: 未知";
                    }
                    
                    poolProbability.textContent = formattedRate;
                } else {
                    poolProbability.textContent = "概率: 未知";
                }
                
                // 组装池子标题
                poolTitle.appendChild(poolName);
                poolTitle.appendChild(poolProbability);
                poolContainer.appendChild(poolTitle);
                
                // 创建游戏网格容器
                const gamesGrid = document.createElement('div');
                gamesGrid.className = 'pool-games';
                
                // 创建文档片段，优化DOM操作
                const gamesFragment = document.createDocumentFragment();
                
                // 对该等级的游戏进行排序：高价值 > 普通 > 低价值 > 库存为0
                const sortedGames = [...gamesByLevel[level]].sort((a, b) => {
                    // 首先按库存排序：库存为0的排最后
                    const aStock = Number(a.stock) || 0;
                    const bStock = Number(b.stock) || 0;
                    
                    if (aStock === 0 && bStock > 0) return 1;
                    if (aStock > 0 && bStock === 0) return -1;
                    
                    // 如果都有库存或都没有库存，按价值排序
                    const aPrice = cachedPrices[a.gameId] || 0;
                    const bPrice = cachedPrices[b.gameId] || 0;
                    
                    // 高价值判断：价格高于单抽价格+10
                    const aIsHighValue = aPrice > (chestOncePrice + 10);
                    const bIsHighValue = bPrice > (chestOncePrice + 10);
                    
                    // 低价值判断：价格低于单抽价格
                    const aIsLowValue = aPrice < chestOncePrice;
                    const bIsLowValue = bPrice < chestOncePrice;
                    
                    // 高价值排前面
                    if (aIsHighValue && !bIsHighValue) return -1;
                    if (!aIsHighValue && bIsHighValue) return 1;
                    
                    // 低价值排后面
                    if (aIsLowValue && !bIsLowValue) return 1;
                    if (!aIsLowValue && bIsLowValue) return -1;
                    
                    // 如果价值相同，按价格从高到低排序
                    return bPrice - aPrice;
                });
                
                // 渲染排序后的游戏
                sortedGames.forEach(game => {
                    // 创建游戏卡片
                    const gameCard = document.createElement('div');
                    gameCard.className = 'game-card';
                    
                    // 添加游戏图片
                    const gameImage = document.createElement('div');
                    gameImage.className = 'game-image';
                    const imgElement = document.createElement('img');
                    
                    // 确保图片URL有效，添加错误处理
                    imgElement.src = game.gameAva || '/static/images/default-game.png';
                    imgElement.alt = game.gameNameCn || '游戏图片';
                    imgElement.loading = 'lazy';
                    
                    // 添加图片加载错误处理
                    imgElement.onerror = function() {
                        this.src = '/static/images/default-game.png';
                        this.onerror = null; // 防止循环触发
                    };
                    
                    gameImage.appendChild(imgElement);
                    gameCard.appendChild(gameImage);
                    
                    // 添加历史价格标签（如果有）
                    const cachedPrice = cachedPrices[game.gameId];
                    if (cachedPrice) {
                        const priceTag = document.createElement('div');
                        priceTag.className = 'price-tag';
                        priceTag.textContent = `¥${cachedPrice.toFixed(2)}`;
                        gameImage.appendChild(priceTag);
                        
                        // 检查是否是高价值游戏 - 价格高于单抽价格+10
                        if (cachedPrice > (chestOncePrice + 10)) {
                            gameCard.classList.add('high-value-game');
                            gameCard.classList.remove('low-value-game');
                        } 
                        // 检查是否是低价值游戏 - 价格低于单抽价格
                        else if (cachedPrice < chestOncePrice) {
                            gameCard.classList.add('low-value-game');
                            gameCard.classList.remove('high-value-game');
                        }
                        else {
                            gameCard.classList.remove('high-value-game');
                            gameCard.classList.remove('low-value-game');
                        }
                    }
                    
                    // 设置游戏标题
                    const titleDiv = document.createElement('div');
                    titleDiv.className = 'game-title';
                    titleDiv.textContent = game.gameNameCn || '未知游戏';
                    gameCard.appendChild(titleDiv);
                    
                    // 创建库存和概率信息行
                    const infoRow = document.createElement('div');
                    infoRow.className = 'game-info-row';
                    
                    // 添加库存信息，包括变动指示器
                    const stockSpan = document.createElement('span');
                    stockSpan.className = 'stock-info';
                    const currentStock = Number(game.stock) || 0;
                    
                    // 检查是否有库存变动
                    if (previousStockData[game.gameId]) {
                        const prevStock = previousStockData[game.gameId].stock;
                        const stockChange = currentStock - prevStock;
                        
                        if (stockChange < 0) {
                            // 库存减少
                            stockSpan.innerHTML = `库存: <span class="stock-number">${currentStock}</span><span class="stock-decrease">↓${Math.abs(stockChange)}</span>`;
                            stockSpan.title = `库存减少了${Math.abs(stockChange)}个`;
                        } else if (stockChange > 0) {
                            // 库存增加
                            stockSpan.innerHTML = `库存: <span class="stock-number">${currentStock}</span><span class="stock-increase">↑${stockChange}</span>`;
                            stockSpan.title = `库存增加了${stockChange}个`;
                        } else {
                            // 库存无变化
                            stockSpan.innerHTML = `库存: <span class="stock-number">${currentStock}</span>`;
                        }
                    } else {
                        // 首次加载，无变化数据
                        stockSpan.innerHTML = `库存: <span class="stock-number">${currentStock}</span>`;
                    }
                    
                    infoRow.appendChild(stockSpan);
                    
                    // 计算该游戏在其等级池中的概率
                    const gamesInSameLevel = gamesByLevel[level] || [];
                    const totalStockInLevel = gamesInSameLevel.reduce((sum, g) => sum + (Number(g.stock) || 0), 0);
                    let gameProbability = 0;
                    
                    if (totalStockInLevel > 0 && currentStock > 0) {
                        // 该游戏在其等级池中的概率
                        const inLevelProb = currentStock / totalStockInLevel;
                        // 该等级池的总体概率
                        const levelProb = parseFloat(rateInfo[`rate${level}`] || 0);
                        // 该游戏的总体概率
                        gameProbability = inLevelProb * levelProb;
                    }
                    
                    // 添加概率信息
                    const probSpan = document.createElement('span');
                    probSpan.className = 'probability-info';
                    
                    if (gameProbability === 0) {
                        probSpan.textContent = `概率: 0%`; // 概率为0时显示0%
                    } else if (gameProbability < 0.0001) {
                        probSpan.textContent = `概率: ${gameProbability.toExponential(2)}`;
                    } else if (gameProbability < 0.001) {
                        probSpan.textContent = `概率: ${(gameProbability * 100).toFixed(4)}%`;
                    } else {
                        probSpan.textContent = `概率: ${(gameProbability * 100).toFixed(2)}%`;
                    }
                    
                    infoRow.appendChild(probSpan);
                    gameCard.appendChild(infoRow);
                    
                    // 将游戏ID和名称添加为数据属性
                    gameCard.dataset.gameId = game.gameId;
                    gameCard.dataset.gameName = game.gameNameCn;
                    
                    // 添加点击事件获取价格
                    gameCard.addEventListener('click', async function() {
                        try {
                            // 显示加载中
                            this.classList.add('loading-price');
                            
                            // 查找或创建价格元素
                            let priceEl = this.querySelector('.price-tag');
                            if (!priceEl) {
                                // 如果不存在价格标签，则在游戏图片上创建一个
                                const gameImage = this.querySelector('.game-image');
                                if (gameImage) {
                                    priceEl = document.createElement('div');
                                    priceEl.className = 'price-tag';
                                    gameImage.appendChild(priceEl);
                                }
                            }
                            
                            // 获取游戏价格
                            const price = await fetchGamePrice(game.gameId, game.gameNameCn);
                            
                            // 如果获取到有效价格，则保存到数据库
                            if (price > 0) {
                                await updatePriceInBackendCache(game.gameId, game.gameNameCn, price);
                                
                                // 更新显示价格
                                if (priceEl) {
                                    priceEl.textContent = `¥${price.toFixed(2)}`;
                                }
                                
                                // 检查是否是高价值游戏 - 价格高于单抽价格+10
                                if (price > (chestOncePrice + 10)) {
                                    this.classList.add('high-value-game');
                                    this.classList.remove('low-value-game');
                                } 
                                // 检查是否是低价值游戏 - 价格低于单抽价格
                                else if (price < chestOncePrice) {
                                    this.classList.add('low-value-game');
                                    this.classList.remove('high-value-game');
                                }
                                else {
                                    this.classList.remove('high-value-game');
                                    this.classList.remove('low-value-game');
                                }
                                
                                // 显示成功获取价格的通知
                                showToast(`已获取价格: ¥${price.toFixed(2)}`, 'success');
                            } else {
                                // 如果获取失败，恢复原来的价格或移除价格标签
                                showToast(`获取价格失败`, 'error');
                            }
                        } catch (error) {
                            console.error(`获取游戏价格时出错:`, error);
                            showToast(`获取价格失败: ${error.message}`, 'error');
                        } finally {
                            // 无论成功或失败，移除加载状态
                            this.classList.remove('loading-price');
                        }
                    });
                    
                    // 库存为0时，添加特定类名
                    if (currentStock === 0) {
                        gameCard.classList.add('out-of-stock');
                    }
                    
                    // 添加卡片到文档片段
                    gamesFragment.appendChild(gameCard);
                });
                
                // 将所有游戏卡片添加到网格容器
                gamesGrid.appendChild(gamesFragment);
                poolContainer.appendChild(gamesGrid);
                
                // 将整个池子添加到主容器
                chestGamesList.appendChild(poolContainer);
            });
        }
    }
    
    /**
     * 计算肥皂盒的期望值
     * @param {Object} chestDetail - 肥皂盒详情数据
     * @param {Array} games - 肥皂盒中的游戏数据
     */
    async function calculateChestExpectation(chestDetail, games) {
        const evResultContent = document.getElementById('ev-result-content');
        if (!chestDetail || !games || !games.length || !evResultContent) {
            showToast('无法计算期望：数据不完整', 'error');
            return;
        }
        let totalExpectedValue = 0;
        let anErrorOccurred = false;
        // 获取单抽成本
        const singleDrawCost = Number(chestDetail.oncePrice);
        if (isNaN(singleDrawCost)) {
            evResultContent.innerHTML = '<p style="color:red;">错误：未能获取到有效的单抽成本</p>';
            return;
        }
        // 收集所有有效游戏ID
        const allGameIdsInChest = [...new Set(games
            .filter(game => game.gameId && Number(game.stock ?? 0) > 0)
            .map(game => game.gameId))];
        // 如果没有有效游戏ID
        if (allGameIdsInChest.length === 0) {
            evResultContent.innerHTML = '<p>肥皂盒中没有库存大于0的游戏，无法计算期望</p>';
            return;
        }
        // 批量获取缓存价格
        const cachedPricesMap = await fetchCachedPrices(allGameIdsInChest);
        // 按等级分组游戏
        const gamesByLevel = games.reduce((acc, game) => {
            const level = game.lv ?? '未知';
            if (!acc[level]) acc[level] = [];
            acc[level].push(game);
            return acc;
        }, {});
        // 处理每个等级
        for (const level of Object.keys(gamesByLevel)) {
            if (level === '未知') continue;
            // 获取当前等级的概率
            const levelRate = Number(chestDetail[`rate${level}`]);
            if (isNaN(levelRate) || levelRate === 0) continue;
            const gamesInLevel = gamesByLevel[level];
            let weightedSumValueInLevel = 0;
            let totalStockInLevel = gamesInLevel.reduce(
                (sum, game) => sum + (Number(game.stock ?? 0) > 0 ? Number(game.stock) : 0),
                0
            );
            if (totalStockInLevel <= 0) continue;
            // 处理该等级中的每个游戏
            for (const game of gamesInLevel) {
                const gameId = game.gameId;
                const gameStock = Number(game.stock ?? 0);
                if (!gameId || gameStock <= 0) continue;
                let itemPrice = null;
                // 尝试从缓存获取价格
                if (cachedPricesMap[gameId]) {
                    itemPrice = Number(cachedPricesMap[gameId]);
                } else {
                    // 如果缓存中没有，尝试获取实时价格
                    try {
                        itemPrice = await fetchGamePrice(gameId);
                        // 如果获取到有效价格，保存到数据库
                        if (itemPrice !== null && !isNaN(itemPrice) && itemPrice > 0) {
                            const gameName = game.gameNameCn || '';
                            await updatePriceInBackendCache(gameId, gameName, itemPrice);
                        }
                    } catch (error) {
                        console.error(`获取游戏 ${gameId} 价格失败:`, error);
                        anErrorOccurred = true;
                    }
                }
                // 如果获取到有效价格，计算该游戏对期望的贡献
                if (itemPrice !== null && !isNaN(itemPrice)) {
                    const probabilityOfThisItemInLevel = gameStock / totalStockInLevel;
                    weightedSumValueInLevel += probabilityOfThisItemInLevel * itemPrice;
                }
            }
            // 计算该等级对总期望的贡献
            totalExpectedValue += levelRate * weightedSumValueInLevel;
        }
        // 显示计算结果
        if (anErrorOccurred) {
            evResultContent.innerHTML += '<p style="color:orange;">警告：部分游戏价格获取失败，结果可能不准确</p>';
        }
        // 计算净期望收益和期望倍率
        const netEV = totalExpectedValue - singleDrawCost;
        const multiplier = singleDrawCost > 0 ? (totalExpectedValue / singleDrawCost) : 0;
        // 确定期望倍率的解释文字和样式
        let explanation = "";
        let multiplierClass = "";
        if (multiplier > 1.1) {
            explanation = "期望 > 1.1，可能盈利";
            multiplierClass = "ev-good";
        } else if (multiplier > 0.9 && multiplier <= 1.1) {
            explanation = "期望接近1，不赚不亏";
            multiplierClass = "ev-neutral";
        } else {
            explanation = "期望 < 0.9，可能亏损";
            multiplierClass = "ev-bad";
        }
        // 构建结果HTML
        let resultHTML = `
            <div class="ev-multiplier ${multiplierClass}">
                期望倍率: ${multiplier.toFixed(4)} <span>(${explanation})</span>
            </div>
            <div class="ev-detail">
                <div class="ev-item">
                    <span class="ev-item-label">毛期望价值:</span>
                    <span class="ev-item-value">¥${totalExpectedValue.toFixed(2)}</span>
                </div>
                <div class="ev-item">
                    <span class="ev-item-label">单抽成本:</span>
                    <span class="ev-item-value">¥${singleDrawCost.toFixed(2)}</span>
                </div>
                <div class="ev-item">
                    <span class="ev-item-label">净期望收益:</span>
                    <span class="ev-item-value ${netEV > 0 ? 'ev-good' : 'ev-bad'}">¥${netEV.toFixed(2)}</span>
                </div>
                <div class="ev-item">
                    <span class="ev-item-label">ROI:</span>
                    <span class="ev-item-value ${netEV > 0 ? 'ev-good' : 'ev-bad'}">${(netEV / singleDrawCost * 100).toFixed(2)}%</span>
                </div>
            </div>
        `;
        evResultContent.innerHTML = resultHTML;
    }
    
    /**
     * 显示提示信息
     * @param {string} message - 提示消息
     * @param {string} type - 提示类型：success, error, info
     */
    function showToast(message, type = 'success') {
        // 检查是否已有toast元素
        let toast = document.querySelector('.toast');
        if (!toast) {
            // 创建toast元素
            toast = document.createElement('div');
            toast.className = 'toast';
            document.body.appendChild(toast);
        }
        
        // 设置消息内容和类型
        toast.textContent = message;
        toast.className = 'toast';
        
        // 添加类型样式
        if (type) {
            toast.classList.add(type);
        }
        
        // 显示toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }
    
    /**
     * 为肥皂盒下单
     * @param {string} chestId - 肥皂盒ID
     * @param {number} draws - 抽取次数
     */
    async function placeChestOrder(chestId, draws) {
        if (!chestId) {
            showToast('肥皂盒ID无效', 'error');
            return;
        }
        
        // 检查锁定状态
        if (orderLocked) {
            showToast('请先解锁下单功能', 'warning');
            return;
        }
        
        try {
            // 显示下单中提示
            showToast(`正在下单，请稍候...`, 'info');
            
            // 使用预加载的配置或从本地存储获取肥皂盒下单配置
            let isUsingMainAccount, payType, useBalance;
            
            if (orderConfig.isPreloaded) {
                // 使用预加载的配置
                isUsingMainAccount = orderConfig.isUsingMainAccount;
                payType = orderConfig.payType;
                useBalance = orderConfig.useBalance;
                console.log('使用预加载的下单配置');
            } else {
                // 从本地存储获取配置
                isUsingMainAccount = localStorage.getItem('chestUseMainAccount') !== 'false'; // 默认使用主账号
                payType = localStorage.getItem('chestPayType') || 'py';
                useBalance = localStorage.getItem('chestUseBalance');
            }
            
            // 构建API请求参数
            const data = {
                chestId: chestId,
                draws: draws,
                payType: payType,
                walletFlag: useBalance,
                useMainAccount: isUsingMainAccount,
                orderType: "chest" // 标识这是肥皂盒下单
            };
            
            // 设置请求超时
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
            
            try {
                // 使用新的API端点，将根据设置选择合适的账号下单
                const response = await fetch('/api/chest/place-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data),
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId); // 清除超时
                
                const result = await response.json();
                
                if (result.success) {
                    showToast(`下单成功！支付链接已发送到邮箱`, 'success');
                    
                    // 下单成功后，自动锁定
                    orderLocked = true;
                    initLockButton();
                    
                    // 下单成功后，立即更新界面状态，不等待刷新
                    showToast(`正在刷新数据...`, 'info');
                    
                    // 下单成功后，刷新页面数据
                    setTimeout(() => {
                        loadChestDetail(chestId);
                        loadChestLogs(chestId);
                    }, 1000);
                    
                    // 不再在界面上提示打开支付链接，而是依赖邮件通知
                } else {
                    // 处理各种错误情况
                    const errorMessage = result.message || '下单失败，请重试';
                    showToast(`下单失败: ${errorMessage}`, 'error');
                }
            } catch (fetchError) {
                clearTimeout(timeoutId);
                if (fetchError.name === 'AbortError') {
                    showToast(`下单请求超时，但可能已成功处理，请稍后检查邮件`, 'info');
                    // 尝试刷新数据
                    setTimeout(() => {
                        loadChestDetail(chestId);
                        loadChestLogs(chestId);
                    }, 2000);
                } else {
                    throw fetchError;
                }
            }
        } catch (error) {
            console.error('下单请求出错:', error);
            showToast(`下单请求出错: ${error.message || '未知错误'}`, 'error');
        }
    }
    
    /**
     * 获取收藏的肥皂盒列表
     * @returns {Array} 收藏的肥皂盒ID数组
     */
    function getFavoriteChests() {
        const favoritesStr = localStorage.getItem(FAVORITE_CHESTS_KEY);
        return favoritesStr ? JSON.parse(favoritesStr) : [];
    }

    /**
     * 添加肥皂盒到收藏
     * @param {Object} chest - 肥皂盒数据对象
     * @returns {boolean} - 是否添加成功
     */
    function addChestToFavorites(chest) {
        if (!chest || !chest.id) return false;
        
        const favorites = getFavoriteChests();
        
        // 检查是否已经在收藏列表中
        if (favorites.some(item => item.id === chest.id)) {
            return false; // 已存在，不重复添加
        }
        
        // 创建简化版的肥皂盒对象，只保存必要的信息
        const simpleChest = {
            id: chest.id,
            name: chest.name || chest.chestName || detailChestTitle.textContent || '未知肥皂盒', // 确保正确获取名称
            addTime: new Date().toISOString() // 添加时间
        };
        
        // 添加到收藏列表
        favorites.push(simpleChest);
        
        // 保存到本地存储
        localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(favorites));
        
        // 显示提示
        showToast('已添加到收藏', 'success');
        
        return true;
    }

    /**
     * 从收藏中移除肥皂盒
     * @param {string} chestId - 肥皂盒ID
     * @returns {boolean} - 是否移除成功
     */
    function removeChestFromFavorites(chestId) {
        if (!chestId) return false;
        
        const favorites = getFavoriteChests();
        const initialLength = favorites.length;
        
        // 过滤掉要移除的项目
        const newFavorites = favorites.filter(item => item.id !== chestId);
        
        // 如果长度没变，说明没找到要移除的项目
        if (newFavorites.length === initialLength) {
            return false;
        }
        
        // 保存新的收藏列表
        localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(newFavorites));
        
        // 显示提示
        showToast('已从收藏中移除', 'info');
        
        return true;
    }

    /**
     * 检查肥皂盒是否已收藏
     * @param {string} chestId - 肥皂盒ID
     * @returns {boolean} - 是否已收藏
     */
    function isChestFavorited(chestId) {
        if (!chestId) return false;
        
        const favorites = getFavoriteChests();
        return favorites.some(item => item.id === chestId);
    }

    /**
     * 切换肥皂盒收藏状态
     * @param {Object} chest - 肥皂盒数据对象
     * @returns {boolean} - 操作后的收藏状态（true为已收藏，false为未收藏）
     */
    function toggleFavoriteChest(chest) {
        if (!chest || !chest.id) return false;
        
        const isFavorited = isChestFavorited(chest.id);
        
        if (isFavorited) {
            removeChestFromFavorites(chest.id);
            return false;
        } else {
            addChestToFavorites(chest);
            return true;
        }
    }
    
    /**
     * 更新收藏按钮状态
     */
    function updateFavoriteButtonState() {
        if (!favoriteChestBtn || !currentChestDetail || !currentChestDetail.id) return;
        
        const isFavorited = isChestFavorited(currentChestDetail.id);
        updateFavoriteButtonUI(isFavorited);
    }
    
    /**
     * 更新收藏按钮UI
     * @param {boolean} isFavorited - 是否已收藏
     */
    function updateFavoriteButtonUI(isFavorited) {
        if (!favoriteChestBtn) return;
        
        const starIcon = favoriteChestBtn.querySelector('svg');
        if (!starIcon) return;
        
        if (isFavorited) {
            // 已收藏状态：填充星星
            starIcon.setAttribute('fill', 'currentColor');
            favoriteChestBtn.title = '取消收藏';
        } else {
            // 未收藏状态：空心星星
            starIcon.setAttribute('fill', 'none');
            favoriteChestBtn.title = '收藏';
        }
    }

    /**
     * 预加载下单API状态
     */
    async function preloadOrderApi() {
        if (orderApiStatus.checked) return;
        
        try {
            // 检查API可用性
            const response = await fetch('/api/system/status', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                // 设置较短的超时
                signal: AbortSignal.timeout(3000)
            });
            
            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    orderApiStatus.available = true;
                    console.log('下单API预加载成功，服务状态正常');
                }
            }
        } catch (error) {
            console.log('下单API预检查出错，将在下单时重试:', error);
        } finally {
            orderApiStatus.checked = true;
        }
    }

    /**
     * 更新预加载的下单配置
     */
    function updateOrderConfig() {
        // 更新下单配置
        orderConfig = {
            isUsingMainAccount: localStorage.getItem('chestUseMainAccount') !== 'false',
            payType: localStorage.getItem('chestPayType') || 'py',
            useBalance: localStorage.getItem('chestUseBalance'),
            isPreloaded: true,
            lastUpdated: new Date().toISOString()
        };
        console.log('下单配置已更新:', new Date().toLocaleTimeString());
    }

    // 初始化锁定按钮
    function initLockButton() {
        const lockButton = document.getElementById('action-lock-btn');
        const onceDrawBtn = document.querySelector('.once-draw-btn');
        const multiDrawBtn = document.querySelector('.multi-draw-btn');
        
        if (!lockButton || !onceDrawBtn || !multiDrawBtn) return;
        
        // 设置初始状态
        updateLockState();
        
        // 锁定按钮点击事件
        lockButton.addEventListener('click', function() {
            // 切换锁定状态
            orderLocked = !orderLocked;
            
            // 更新UI
            updateLockState();
            
            // 显示提示
            if (!orderLocked) {
                showToast('已解锁下单功能', 'warning');
            } else {
                showToast('已锁定下单功能', 'info');
            }
        });
        
        // 更新锁定状态UI
        function updateLockState() {
            // 更新锁定按钮
            if (orderLocked) {
                // 锁定状态 - 红色
                lockButton.style.color = '#dc3545';
                
                // 禁用下单按钮
                onceDrawBtn.disabled = true;
                multiDrawBtn.disabled = true;
                
                // 设置按钮样式为禁用
                onceDrawBtn.style.opacity = '0.65';
                multiDrawBtn.style.opacity = '0.65';
                
                // 添加锁定图标
                lockButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                    </svg>
                `;
                lockButton.title = "点击解锁";
            } else {
                // 解锁状态 - 绿色
                lockButton.style.color = '#28a745';
                
                // 启用下单按钮
                onceDrawBtn.disabled = false;
                multiDrawBtn.disabled = false;
                
                // 设置按钮样式为启用
                onceDrawBtn.style.opacity = '1';
                multiDrawBtn.style.opacity = '1';
                
                // 添加解锁图标
                lockButton.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                        <path d="M7 11V7a5 5 0 0 1 9.9 0"></path>
                        <path d="M8 11V8"></path>
                    </svg>
                `;
                lockButton.title = "点击锁定";
            }
        }
    }
});

// 添加页面卸载事件，清除库存记录
window.addEventListener('beforeunload', function() {
    // 清空库存记录
    previousStockData = {};
});

// 添加返回按钮点击事件，清除库存记录
document.addEventListener('DOMContentLoaded', function() {
    // 查找返回按钮
    const backButton = document.querySelector('.back-button') || document.querySelector('.nav-back');
    if (backButton) {
        backButton.addEventListener('click', function() {
            // 清空库存记录
            previousStockData = {};
        });
    }
});

// 添加刷新日志按钮事件
document.addEventListener('DOMContentLoaded', function() {
    const refreshLogsBtn = document.getElementById('refresh-logs-btn');
    if (refreshLogsBtn) {
        refreshLogsBtn.addEventListener('click', async function() {
            // 添加旋转动画
            this.classList.add('rotating');
            try {
                // 获取当前肥皂盒ID
                const urlParams = new URLSearchParams(window.location.search);
                const chestId = urlParams.get('id');
                
                if (chestId) {
                    // 重新加载活动记录
                    await loadChestLogs(chestId);
                }
            } finally {
                // 无论成功或失败，1秒后移除旋转动画
                setTimeout(() => {
                    this.classList.remove('rotating');
                }, 1000);
            }
        });
    }
    
    // 添加刷新详情按钮
    const refreshDetailBtn = document.getElementById('refresh-detail-btn');
    if (refreshDetailBtn) {
        refreshDetailBtn.addEventListener('click', async function() {
            // 添加旋转动画
            this.classList.add('rotating');
            try {
                // 获取当前肥皂盒ID
                const urlParams = new URLSearchParams(window.location.search);
                const chestId = urlParams.get('id');
                
                if (chestId) {
                    // 重新加载肥皂盒详情
                    await loadChestDetail(chestId);
                    showToast('已刷新肥皂盒数据', 'success');
                }
            } finally {
                // 无论成功或失败，1秒后移除旋转动画
                setTimeout(() => {
                    this.classList.remove('rotating');
                }, 1000);
            }
        });
    } else {
        // 如果没有刷新按钮，创建一个
        const detailHeader = document.querySelector('.detail-header');
        if (detailHeader) {
            const refreshBtn = document.createElement('button');
            refreshBtn.id = 'refresh-detail-btn';
            refreshBtn.className = 'refresh-btn';
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
            refreshBtn.title = '刷新肥皂盒数据';
            
            refreshBtn.addEventListener('click', async function() {
                // 添加旋转动画
                this.classList.add('rotating');
                try {
                    // 获取当前肥皂盒ID
                    const urlParams = new URLSearchParams(window.location.search);
                    const chestId = urlParams.get('id');
                    
                    if (chestId) {
                        // 重新加载肥皂盒详情
                        await loadChestDetail(chestId);
                        showToast('已刷新肥皂盒数据', 'success');
                    }
                } finally {
                    // 无论成功或失败，1秒后移除旋转动画
                    setTimeout(() => {
                        this.classList.remove('rotating');
                    }, 1000);
                }
            });
            
            // 添加到页面
            detailHeader.appendChild(refreshBtn);
        }
    }
});

/**
 * 获取等级对应的CSS类名
 */
function getLevelClassName(level) {
    switch (parseInt(level)) {
        case 1: return 'legendary-pool';
        case 2: return 'epic-pool';
        case 3: return 'rare-pool';
        default: return 'common-pool';
    }
}

/**
 * 获取等级对应的池子名称
 */
function getLevelPoolName(level) {
    switch (parseInt(level)) {
        case 1: return '传说池';
        case 2: return '史诗池';
        case 3: return '稀有池';
        default: return '普通池';
    }
}



















