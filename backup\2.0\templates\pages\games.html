{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}游戏列表 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <div class="header-content">
        <h1>
            {{ icon('search') }}
            游戏搜索
        </h1>
        <p class="page-description">
            在 Steam 商店中查找游戏。
        </p>
    </div>
</header>

<section>
    <div id="search-card" class="card">
        <form id="game-search-form" class="form-grid">
            <div class="form-group" style="grid-column: 1 / -1;">
                <label for="game-search-input">输入游戏名称</label>
                <input type="search" id="game-search-input" name="term" placeholder="例如：Counter-Strike 2" required>
            </div>
            <div class="form-actions" style="grid-column: 1 / -1;">
                <button type="submit" class="primary">
                    {{ icon('search') }} 搜索
                </button>
            </div>
        </form>
    </div>
</section>

<section>
    <header class="article-header">
        <h2>搜索结果</h2>
    </header>
    <div id="game-results-container">
        <p class="text-muted">输入关键词开始搜索...</p>
    </div>
</section>

<!-- 游戏卡片模板 (隐藏) -->
<template id="game-card-template">
    <div class="result-card">
        <img class="result-card-image" src="" alt="Game Image">
        <div class="result-card-info">
            <strong class="result-card-name"></strong>
            <span class="text-muted result-card-id"></span>
            <div class="result-card-prices">
                <span class="price-tag">Key 价格: <strong class="key-price">N/A</strong></span>
                <span class="price-tag">平均 Key 价: <strong class="key-ave-amt">N/A</strong></span>
            </div>
        </div>
        <!-- 卖家信息按钮 -->
        <button class="sellers-btn" role="button">查看卖家</button>
    </div>
</template>

<!-- 卖家信息模态框 -->
<div id="sellers-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-game-title">卖家信息</h3>
            <button class="close-btn" title="关闭">&times;</button>
        </div>
        <div class="modal-body">
            <div id="modal-loading" style="display: none;">正在加载卖家信息...</div>
            <div id="modal-sellers-list"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/app.js') }}" defer></script>
{% endblock %} 