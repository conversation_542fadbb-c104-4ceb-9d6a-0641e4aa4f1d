// 全局变量
window.logSortField = null;
window.logSortDirection = null;
window.logPageNumber = null;
window.chestLogsContainer = null;
window.currentLogsPage = 1;
window.totalLogsPages = 1;
window.LOGS_PAGE_SIZE = 40;
const PAGE_SIZE = 10;

// 肥皂盒收藏相关
const FAVORITE_CHESTS_KEY = 'favorite_chests';

// 防止重复请求的标记
let isLoadingChests = false;
let isLoadingLogs = false;

// 将肥皂盒详情函数暴露为全局函数
window.openChestDetail = null; // 预先声明全局函数变量

// 添加刷新按钮旋转动画样式
const style = document.createElement('style');
style.innerHTML = `
    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    .rotating {
        animation: rotate 1s linear infinite;
    }
`;
document.head.appendChild(style);

// 添加剩余数量排序相关变量
let remainingDrawModalOpen = false;
let remainingPageCount = 1; // 默认请求1页数据
const REMAINING_PAGE_SIZE = 50; // 固定每页50条
let remainingChestsData = []; // 存储剩余数量排序的数据

// 添加缓存相关常量和函数
const REMAINING_CACHE_KEY = 'remaining_chests_cache';
const CACHE_EXPIRY_TIME = 24 * 60 * 60 * 1000; // 缓存有效期：24小时

/**
 * 获取剩余数量缓存
 * @returns {Object} 缓存对象，格式为 {id: {remaining: number, timestamp: number}}
 */
function getRemainingCache() {
    const cacheStr = localStorage.getItem(REMAINING_CACHE_KEY);
    if (!cacheStr) return {};
    
    try {
        return JSON.parse(cacheStr);
    } catch (e) {
        console.error('解析剩余数量缓存失败:', e);
        return {};
    }
}

/**
 * 保存剩余数量缓存
 * @param {Object} cache - 缓存对象
 */
function saveRemainingCache(cache) {
    try {
        localStorage.setItem(REMAINING_CACHE_KEY, JSON.stringify(cache));
    } catch (e) {
        console.error('保存剩余数量缓存失败:', e);
    }
}

/**
 * 更新肥皂盒剩余数量缓存
 * @param {Array} chests - 肥皂盒数据数组
 * @returns {Object} 变化的肥皂盒对象，格式为 {id: {old: number, new: number}}
 */
function updateRemainingCache(chests) {
    if (!chests || !Array.isArray(chests)) return {};
    
    const cache = getRemainingCache();
    const changes = {};
    const now = Date.now();
    
    // 清理过期缓存
    Object.keys(cache).forEach(id => {
        if (now - cache[id].timestamp > CACHE_EXPIRY_TIME) {
            delete cache[id];
        }
    });
    
    // 更新缓存并检测变化
    chests.forEach(chest => {
        const id = chest.id;
        if (!id) return;
        
        const remainingDraw = chest.remainingDraw;
        
        // 检查是否有变化
        if (cache[id] && cache[id].remaining !== remainingDraw) {
            changes[id] = {
                old: cache[id].remaining,
                new: remainingDraw
            };
        }
        
        // 更新缓存
        cache[id] = {
            remaining: remainingDraw,
            timestamp: now
        };
    });
    
    // 保存更新后的缓存
    saveRemainingCache(cache);
    
    return changes;
}

/**
 * 全局函数：加载肥皂盒抽取记录
 * @param {string} chestId - 可选，特定肥皂盒ID
 * @returns {Promise} - 返回Promise以支持异步处理
 */
window.loadChestLogs = async function(chestId = null) {
    // 防止同时发起多个请求
    if (isLoadingLogs) {
        console.log('活动记录正在加载中，跳过重复请求');
        return Promise.resolve([]);
    }
    
    isLoadingLogs = true;
    
    try {
        // 获取容器元素
        const logsContainer = document.getElementById('chest-logs-container');
        if (!logsContainer) {
            console.error('找不到chest-logs-container元素');
            return Promise.reject(new Error('找不到日志容器'));
        }
        
        // 获取加载指示器
        const loadingIndicator = logsContainer.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'flex';
        }
        
        // 获取日志内容容器
        const logsContent = document.getElementById('logs-content');
        if (!logsContent) {
            console.error('找不到logs-content元素');
            if (loadingIndicator) loadingIndicator.style.display = 'none';
            return Promise.reject(new Error('找不到日志内容容器'));
        }
        
        console.log('开始加载活动记录...');
        
        // 构建API URL - 使用正确的API路径
        const url = '/api/chests/xboot/detLog/show';
        const params = new URLSearchParams();
        if (chestId) {
            params.append('chestId', chestId);
        }
        params.append('pageNumber', 1);
        params.append('pageSize', 20);
        params.append('sort', 'createTime');
        params.append('order', 'desc');

        const fullUrl = `${url}?${params.toString()}`;
        console.log('API请求URL:', fullUrl);
        
        // 发送API请求
        const response = await fetch(fullUrl);
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // 隐藏加载指示器
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
        
        // 检查API返回数据格式
        let logsData = null;
        if (data && data.result && Array.isArray(data.result.content)) {
            logsData = data.result.content;
        } else if (data && data.result && Array.isArray(data.result.records)) {
            logsData = data.result.records;
        } else if (data && data.data && Array.isArray(data.data.records)) {
            logsData = data.data.records;
        } else {
            console.log('未找到有效的记录数据');
            logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
            return Promise.resolve([]);
        }
        
        if (logsData && logsData.length > 0) {
            renderChestLogs(logsData);
            return Promise.resolve(logsData);
        } else {
            logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
            return Promise.resolve([]);
        }
        
    } catch (error) {
        console.error("加载活动记录时出错:", error);
        const logsContent = document.getElementById('logs-content');
        if (logsContent) {
            logsContent.innerHTML = `<div style="padding: 20px; text-align: center; color: rgba(255, 255, 255, 0.8);">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-bottom: 8px;">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <div>加载失败: ${error.message}</div>
            </div>`;
        }
        
        // 隐藏加载指示器
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
        
        return Promise.reject(error);
    } finally {
        isLoadingLogs = false; // 无论成功或失败，都重置加载状态
    }
};

/**
 * 加载肥皂盒数据
 */
window.loadChests = async function() {
    // 防止重复请求
    if (isLoadingChests) {
        console.log('肥皂盒数据正在加载中，跳过重复请求');
        return Promise.resolve([]);
    }
    
    isLoadingChests = true;
    
    const chestsContainer = document.getElementById('chests-container');
    if (!chestsContainer) {
        isLoadingChests = false;
        return Promise.resolve([]);
    }
    
    // 获取UI控件
    const sortField = document.getElementById('sort-field');
    const sortDirection = document.getElementById('sort-direction');
    const pageNumber = document.getElementById('page-number');
    
    // 直接从UI控件获取排序信息
    const currentSort = sortField ? sortField.value : 'sortOrder';
    const currentOrder = sortDirection ? sortDirection.value : 'asc';
    const currentPage = pageNumber ? parseInt(pageNumber.value) || 1 : 1;
    
    // 更新URL参数，不刷新页面
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('sort', currentSort);
    newUrl.searchParams.set('order', currentOrder);
    newUrl.searchParams.set('page', currentPage.toString());
    window.history.replaceState({}, '', newUrl.toString());
    
    // 更新当前页
    window.currentPage = currentPage;
    window.updatePageIndicator();
    
    // 显示加载中
    chestsContainer.innerHTML = `
        <div class="loading-indicator">
            <div class="spin"></div>
            <span>正在加载肥皂盒数据...</span>
        </div>
    `;
    
    try {
        // 检查是否是剩余数量排序
        const isRemainingSortMode = currentSort === 'remainingDraw';
        
        // 构建API请求URL
        // 如果是剩余数量排序，获取更多数据以便前端排序
        const pageSize = isRemainingSortMode ? 50 : PAGE_SIZE;
        // 如果是剩余数量排序，使用默认排序获取数据
        const apiSort = isRemainingSortMode ? 'oncePrice' : currentSort;
        
        // 对于剩余数量排序，始终从第1页获取所有数据
        // 对于其他排序方式，使用当前页码
        const apiPageNumber = isRemainingSortMode ? 1 : currentPage;
        
        const url = `/api/chests?page_number=${apiPageNumber}&page_size=${pageSize}&sort=${apiSort}&order=${currentOrder}`;
        
        // 发送请求
        const response = await fetch(url);
        
        // 检查响应状态
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
            throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
        }
        
        // 解析响应数据
        const data = await response.json();
        
        // 检查API返回状态
        if (data.code !== 200) {
            throw new Error(data.message || '获取数据失败');
        }
        
        // 获取内容
        const result = data.result || {};
        let content = result.content || [];
        
        // 如果是剩余数量排序，在前端进行排序
        if (isRemainingSortMode) {
            // 计算每个肥皂盒的剩余数量
            content.forEach(chest => {
                chest.remainingDraw = (chest.totalDraw || 0) - (chest.curDraw || 0);
            });
            
            // 根据剩余数量排序
            content.sort((a, b) => {
                // 剩余数量排序始终从小到大，忽略用户选择的排序方向
                return a.remainingDraw - b.remainingDraw;
            });
            
            // 计算总页数
            const totalItems = content.length;
            window.totalPages = Math.ceil(totalItems / PAGE_SIZE);
            
            // 只保留当前页需要的数据
            const startIndex = (currentPage - 1) * PAGE_SIZE;
            content = content.slice(startIndex, startIndex + PAGE_SIZE);
        } else {
            // 计算总页数
            if (result.totalElements && result.size) {
                window.totalPages = Math.ceil(result.totalElements / result.size);
            } else {
                window.totalPages = 1;
            }
        }
        
        // 更新分页控件
        window.updatePaginationControls();
        
        // 渲染肥皂盒列表
        window.renderChests(content);
        
        return content; // 返回数据，以便后续处理
    } catch (error) {
        console.error('获取肥皂盒数据失败:', error);
        chestsContainer.innerHTML = `
            <div class="error-message">
                <p>获取肥皂盒数据失败: ${error.message}</p>
                <p>请检查登录状态或稍后再试。</p>
            </div>
        `;
        throw error; // 重新抛出错误，以便调用者处理
    } finally {
        isLoadingChests = false; // 无论成功或失败，都重置加载状态
    }
};

/**
 * 更新页码指示器
 */
window.updatePageIndicator = function() {
    const pageIndicator = document.getElementById('page-indicator');
    if (pageIndicator) {
        pageIndicator.textContent = `第 ${window.currentPage || 1} 页`;
    }
};

/**
 * 更新分页控件状态
 */
window.updatePaginationControls = function() {
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    
    if (prevPageBtn) prevPageBtn.disabled = window.currentPage <= 1;
    if (nextPageBtn) nextPageBtn.disabled = window.currentPage >= window.totalPages;
    window.updatePageIndicator();
};

/**
 * 获取收藏的肥皂盒列表
 * @returns {Array} 收藏的肥皂盒ID数组
 */
function getFavoriteChests() {
    const favoritesStr = localStorage.getItem(FAVORITE_CHESTS_KEY);
    return favoritesStr ? JSON.parse(favoritesStr) : [];
}

/**
 * 添加肥皂盒到收藏
 * @param {Object} chest - 肥皂盒数据对象
 * @returns {boolean} - 是否添加成功
 */
function addChestToFavorites(chest) {
    if (!chest || !chest.id) return false;
    
    const favorites = getFavoriteChests();
    
    // 检查是否已经在收藏列表中
    if (favorites.some(item => item.id === chest.id)) {
        return false; // 已存在，不重复添加
    }
    
    // 创建简化版的肥皂盒对象，只保存必要的信息
    const simpleChest = {
        id: chest.id,
        name: chest.name || '未知肥皂盒', // 仅保存名称作为显示用
        addTime: new Date().toISOString() // 添加时间
    };
    
    // 添加到收藏列表
    favorites.push(simpleChest);
    
    // 保存到本地存储
    localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(favorites));
    
    // 显示提示
    showToast('已添加到收藏', 'success');
    
    return true;
}

/**
 * 从收藏中移除肥皂盒
 * @param {string} chestId - 肥皂盒ID
 * @returns {boolean} - 是否移除成功
 */
function removeChestFromFavorites(chestId) {
    if (!chestId) return false;
    
    const favorites = getFavoriteChests();
    const initialLength = favorites.length;
    
    // 过滤掉要移除的项目
    const newFavorites = favorites.filter(item => item.id !== chestId);
    
    // 如果长度没变，说明没找到要移除的项目
    if (newFavorites.length === initialLength) {
        return false;
    }
    
    // 保存新的收藏列表
    localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(newFavorites));
    
    // 显示提示
    showToast('已从收藏中移除', 'info');
    
    return true;
}

/**
 * 检查肥皂盒是否已收藏
 * @param {string} chestId - 肥皂盒ID
 * @returns {boolean} - 是否已收藏
 */
function isChestFavorited(chestId) {
    if (!chestId) return false;
    
    const favorites = getFavoriteChests();
    return favorites.some(item => item.id === chestId);
}

/**
 * 切换肥皂盒收藏状态
 * @param {Object} chest - 肥皂盒数据对象
 * @returns {boolean} - 操作后的收藏状态（true为已收藏，false为未收藏）
 */
function toggleFavoriteChest(chest) {
    if (!chest || !chest.id) return false;
    
    const isFavorited = isChestFavorited(chest.id);
    
    if (isFavorited) {
        removeChestFromFavorites(chest.id);
        return false;
    } else {
        addChestToFavorites(chest);
        return true;
    }
}

/**
 * 渲染肥皂盒列表
 * @param {Array} chests - 肥皂盒数据数组
 */
window.renderChests = function(chests) {
    const chestsContainer = document.getElementById('chests-container');
    const chestItemTemplate = document.getElementById('chest-item-template');
    if (!chestsContainer || !chestItemTemplate) return;
    
    // 如果没有数据
    if (!chests || chests.length === 0) {
        chestsContainer.innerHTML = '<p>没有找到肥皂盒数据。</p>';
        return;
    }
    
    // 清空容器
    chestsContainer.innerHTML = '';
    
    // 创建DocumentFragment提升渲染性能
    const fragment = document.createDocumentFragment();
    
    // 创建肥皂盒列表容器
    const chestsListElement = document.createElement('div');
    chestsListElement.className = 'chests-list';
    
    // 获取当前URL参数，用于构建返回链接
    const currentUrlParams = new URLSearchParams(window.location.search);
    const currentSort = currentUrlParams.get('sort') || 'sortOrder';
    const currentOrder = currentUrlParams.get('order') || 'asc';
    const currentPage = currentUrlParams.get('page') || '1';
    const returnPath = `?sort=${currentSort}&order=${currentOrder}&page=${currentPage}`;
    
    // 遍历肥皂盒数据
    chests.forEach(chest => {
        // 克隆模板
        const chestItem = chestItemTemplate.content.cloneNode(true).firstElementChild;
        
        // 设置肥皂盒ID属性
        const chestId = chest.id || '';
        chestItem.dataset.chestId = chestId;
        
        // 设置内容
        chestItem.querySelector('.chest-name').textContent = chest.name || '未知肥皂盒';
        
        // 设置ID - 新结构
        const chestIdSpan = chestItem.querySelector('.chest-id');
        if (chestIdSpan) {
            chestIdSpan.textContent = chestId;
        }
        
        chestItem.querySelector('.chest-price').textContent = chest.oncePrice ? `¥${chest.oncePrice.toFixed(2)}` : 'N/A';
        
        // 添加多抽价格
        if (chestItem.querySelector('.chest-multi-price')) {
            // 多抽价格直接使用multiPrice字段
            const multiPrice = chest.multiPrice || '未知';
            chestItem.querySelector('.chest-multi-price').textContent = multiPrice ? `¥${multiPrice.toFixed(2)}` : 'N/A';
            
            // 设置抽取次数
            const multiDrawCount = chest.multidraw || '未知'; // 默认为3次
            const multiDrawCountElem = chestItem.querySelector('.multi-draw-count');
            if (multiDrawCountElem) {
                multiDrawCountElem.textContent = multiDrawCount;
            }
        }
        
        // 添加卖家信息
        if (chestItem.querySelector('.chest-seller')) {
            chestItem.querySelector('.chest-seller').textContent = chest.nameSeller || '未知卖家';
        }
        
        // 添加肥皂盒编号
        if (chestItem.querySelector('.chest-number')) {
            // 尝试多个可能的字段名
            const chestNumber = chest.chestNo || '未知';
            chestItem.querySelector('.chest-number').textContent = chestNumber;
        }
        
        // 当前未抽 = 总数 - 已抽
        const totalDraw = chest.totalDraw || 0;
        const curDraw = chest.curDraw || 0;
        const remainDraws = totalDraw - curDraw;
        
        chestItem.querySelector('.chest-remain').textContent = remainDraws;
        chestItem.querySelector('.chest-total').textContent = totalDraw;
        
        // 使用rate1作为概率 - 将小数转换为百分比
        const rate1 = chest.rate1 || chest.probability1 || 0;
        // 如果rate1是小数(0-1之间)，则乘以100转为百分比
        const probabilityDisplay = rate1 <= 1 ? 
            `${(rate1 * 100).toFixed(2)}%` : 
            `${parseFloat(rate1).toFixed(2)}%`;
        
        chestItem.querySelector('.chest-probability').textContent = probabilityDisplay;
        
        // 设置收藏按钮
        const isFavorited = isChestFavorited(chestId);
        const favoriteBtn = document.createElement('button');
        favoriteBtn.className = 'favorite-btn';
        favoriteBtn.innerHTML = isFavorited ? 
            '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#FFB800" stroke="#FFB800" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>' : 
            '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>';
        favoriteBtn.style.background = 'transparent';
        favoriteBtn.style.border = 'none';
        favoriteBtn.style.cursor = 'pointer';
        favoriteBtn.style.padding = '4px';
        favoriteBtn.style.borderRadius = '50%';
        favoriteBtn.style.marginLeft = '8px';
        favoriteBtn.title = isFavorited ? '取消收藏' : '添加到收藏';
        
        // 添加收藏按钮点击事件
        favoriteBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            const newStatus = toggleFavoriteChest(chest);
            
            // 更新按钮状态
            this.innerHTML = newStatus ? 
                '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#FFB800" stroke="#FFB800" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>' : 
                '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>';
            this.title = newStatus ? '取消收藏' : '添加到收藏';
        });
        
        // 将收藏按钮添加到标题区域
        const chestTitle = chestItem.querySelector('.chest-title');
        chestTitle.style.display = 'flex';
        chestTitle.style.alignItems = 'center';
        chestTitle.appendChild(favoriteBtn);
        
        // 移除徽章显示
        const badge = chestItem.querySelector('.chest-badge');
        if (badge) {
            badge.style.display = 'none';
        }
        
        // 给名称点击事件 - 跳转到详情页面
        const nameElement = chestItem.querySelector('.chest-name');
        if (nameElement) {
            nameElement.style.cursor = 'pointer';
            nameElement.title = '点击查看详情页面';
            nameElement.addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡到整个卡片
                window.location.href = `/chest-detail?id=${chest.id}&return_path=${encodeURIComponent(returnPath)}`;
            });
        }
        
        // 添加点击事件，跳转到详情页面
        chestItem.addEventListener('click', function() {
            // 添加返回路径参数，以便从详情页返回时保持当前排序和分页状态
            window.location.href = `/chest-detail?id=${chest.id}&return_path=${encodeURIComponent(returnPath)}`;
        });
        
        // 添加到容器
        chestsListElement.appendChild(chestItem);
    });
    
    // 将列表添加到DocumentFragment
    fragment.appendChild(chestsListElement);
    
    // 一次性将所有内容添加到DOM
    chestsContainer.appendChild(fragment);
};

/**
 * 渲染肥皂盒记录
 * @param {Array} logs - 肥皂盒记录数据数组
 */
function renderChestLogs(logs) {
    // 获取新的列表容器
    const logsContent = document.getElementById('logs-content');
    if (!logsContent) {
        console.error('找不到logs-content元素');
        return;
    }
    
    // 清空现有内容
    logsContent.innerHTML = '';
    
    // 检查是否有记录
    if (!logs || logs.length === 0) {
        logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
        return;
    }
    
    // 创建DocumentFragment提升渲染性能
    const fragment = document.createDocumentFragment();
    
    // 创建记录列表
    const logsList = document.createElement('div');
    logsList.className = 'activity-list';
    
    // 构建返回路径参数
    const sortField = document.getElementById('sort-field');
    const sortDirection = document.getElementById('sort-direction');
    const pageNumber = document.getElementById('page-number');
    const returnParams = new URLSearchParams();
    if (sortField) returnParams.set('sort', sortField.value);
    if (sortDirection) returnParams.set('order', sortDirection.value);
    if (pageNumber) returnParams.set('page', pageNumber.value);
    const returnPath = returnParams.toString() ? `?${returnParams.toString()}` : '';
    
    // 遍历所有记录数据，而不是只取两条
    logs.forEach((log) => {
        const gameName = log.gameName || '未知游戏';
        const chestName = log.name || '未知肥皂盒';
        const createTime = log.createTime ?
            new Date(log.createTime).toLocaleString('zh-CN', {
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit', second: '2-digit'
            }).replace(/\//g, '-') : '未知时间';
        
        // 创建记录项
        const logItem = document.createElement('div');
        logItem.className = 'activity-item';
        logItem.style.display = 'flex';
        logItem.style.alignItems = 'center';
        logItem.style.padding = '12px 24px';
        logItem.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
        logItem.style.transition = 'background-color 0.2s ease';
        
        // 添加悬停效果
        logItem.addEventListener('mouseenter', () => {
            logItem.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        });
        logItem.addEventListener('mouseleave', () => {
            logItem.style.backgroundColor = 'transparent';
        });
        
        // 获取游戏等级信息，并设置对应的颜色
        const gameLv = parseInt(log.lv) || 4;  // 确保解析为数字
        let levelColor;
        switch (gameLv) {  // 直接使用已解析的数字
            case 1:
                levelColor = '#ec4899'; // 传说 - 粉色
                break;
            case 2:
                levelColor = '#7c3aed'; // 史诗 - 紫色
                break;
            case 3:
                levelColor = '#3b82f6'; // 稀有 - 蓝色
                break;
            default:
                levelColor = '#4b5563'; // 普通 - 灰色
        }
        
        // 为每个记录创建图标
        const gameIcon = document.createElement('div');
        gameIcon.className = 'game-icon';
        gameIcon.textContent = gameLv;  // 直接显示等级数字1、2、3、4
        gameIcon.style.width = '36px';
        gameIcon.style.height = '36px';
        gameIcon.style.borderRadius = '50%';
        gameIcon.style.backgroundColor = levelColor;
        gameIcon.style.color = 'white';
        gameIcon.style.display = 'flex';
        gameIcon.style.justifyContent = 'center';
        gameIcon.style.alignItems = 'center';
        gameIcon.style.marginRight = '12px';
        gameIcon.style.fontWeight = '600';
        gameIcon.style.fontSize = '16px';
        gameIcon.style.flexShrink = '0';
        
        // 创建内容区域
        const content = document.createElement('div');
        content.className = 'activity-content';
        content.style.flex = '1';
        content.style.minWidth = '0'; // 确保文本可以正确截断
        
        // 游戏名称显示
        const gameNameSpan = document.createElement('div');
        gameNameSpan.style.fontWeight = '500';
        gameNameSpan.style.whiteSpace = 'nowrap';
        gameNameSpan.style.textOverflow = 'ellipsis';
        gameNameSpan.style.overflow = 'hidden';
        gameNameSpan.textContent = gameName;
        
        // 抽取信息显示
        const activityInfo = document.createElement('div');
        activityInfo.className = 'activity-info';
        activityInfo.style.display = 'flex';
        activityInfo.style.justifyContent = 'space-between';
        activityInfo.style.fontSize = '0.85rem';
        activityInfo.style.marginTop = '4px';
        activityInfo.style.opacity = '0.8';
        
        // 肥皂盒名称部分
        const chestNameSpan = document.createElement('span');
        chestNameSpan.style.marginRight = '12px';
        chestNameSpan.style.overflow = 'hidden';
        chestNameSpan.style.textOverflow = 'ellipsis';
        chestNameSpan.style.whiteSpace = 'nowrap';
        chestNameSpan.textContent = chestName;
        
        // 时间显示部分
        const timeSpan = document.createElement('span');
        timeSpan.style.whiteSpace = 'nowrap';
        timeSpan.textContent = createTime;
        
        // 组装活动信息
        activityInfo.appendChild(chestNameSpan);
        activityInfo.appendChild(timeSpan);
        
        // 组装内容区域
        content.appendChild(gameNameSpan);
        content.appendChild(activityInfo);
        
        // 组装整个记录项
        logItem.appendChild(gameIcon);
        logItem.appendChild(content);
        
        // 单击记录项跳转到对应的肥皂盒详情
        if (log.chestId) {  // 直接使用chestId字段
            logItem.style.cursor = 'pointer';
            logItem.addEventListener('click', () => {
                window.location.href = `/chest-detail?id=${log.chestId}&return_path=${encodeURIComponent(returnPath)}`;
            });
        }
        
        // 添加到列表中
        logsList.appendChild(logItem);
    });
    
    // 将列表添加到DocumentFragment
    fragment.appendChild(logsList);
    
    // 一次性将所有内容添加到DOM
    logsContent.appendChild(fragment);
}

/**
 * 确保页面中有marquee动画的CSS定义
 */
function ensureMarqueeAnimation() {
    // 不再需要此功能，但保留函数以兼容现有代码
}

/**
 * 加载按剩余数量排序的肥皂盒数据
 */
async function loadRemainingDrawChests() {
    const remainingContainer = document.getElementById('remaining-chests-container');
    const loadingIndicator = document.getElementById('remaining-loading');
    
    if (!remainingContainer || !loadingIndicator) return;
    
    // 显示加载指示器
    loadingIndicator.style.display = 'flex';
    remainingContainer.innerHTML = '';
    
    try {
        // 使用当前的排序方式获取数据
        const sortField = document.getElementById('sort-field');
        const sortDirection = document.getElementById('sort-direction');
        const currentSort = sortField ? sortField.value : 'sortOrder';
        const currentOrder = sortDirection ? sortDirection.value : 'asc';
        
        // 创建并行请求数组
        const requests = [];
        for (let page = 1; page <= remainingPageCount; page++) {
            // 构建API请求URL
            const url = `/api/chests?page_number=${page}&page_size=${REMAINING_PAGE_SIZE}&sort=${currentSort}&order=${currentOrder}`;
            requests.push(fetch(url).then(response => {
                if (!response.ok) {
                    throw new Error(`页面 ${page} 请求失败: ${response.status}`);
                }
                return response.json();
            }));
        }
        
        // 并行发送请求
        const results = await Promise.all(requests);
        
        // 合并所有页面的数据
        let allChests = [];
        results.forEach((data, index) => {
            if (data.code === 200 && data.result && data.result.content) {
                console.log(`成功获取第 ${index + 1} 页数据，共 ${data.result.content.length} 条`);
                allChests = allChests.concat(data.result.content);
            } else {
                console.error(`第 ${index + 1} 页数据格式错误:`, data);
            }
        });
        
        // 计算每个肥皂盒的剩余数量
        allChests.forEach(chest => {
            chest.remainingDraw = (chest.totalDraw || 0) - (chest.curDraw || 0);
        });
        
        // 根据剩余数量从小到大排序
        allChests.sort((a, b) => a.remainingDraw - b.remainingDraw);
        
        // 更新缓存并获取变化
        const changes = updateRemainingCache(allChests);
        
        // 标记变化的肥皂盒
        allChests.forEach(chest => {
            if (changes[chest.id]) {
                const change = changes[chest.id];
                // 添加变化标记和变化方向
                chest.hasChanged = true;
                chest.changeDirection = change.new < change.old ? 'decrease' : 'increase';
                chest.oldRemaining = change.old;
            } else {
                chest.hasChanged = false;
            }
        });
        
        // 保存排序后的数据
        remainingChestsData = allChests;
        
        // 渲染肥皂盒列表
        renderRemainingChests(allChests);
    } catch (error) {
        console.error('获取剩余数量排序数据失败:', error);
        remainingContainer.innerHTML = `
            <div class="error-message" style="padding: 20px; text-align: center; color: #f44336;">
                <p>获取数据失败: ${error.message}</p>
                <p>请检查登录状态或稍后再试。</p>
            </div>
        `;
    } finally {
        // 隐藏加载指示器
        loadingIndicator.style.display = 'none';
    }
}

/**
 * 渲染剩余数量排序的肥皂盒列表
 * @param {Array} chests - 肥皂盒数据数组
 */
function renderRemainingChests(chests) {
    const remainingContainer = document.getElementById('remaining-chests-container');
    if (!remainingContainer) return;
    
    // 如果没有数据
    if (!chests || chests.length === 0) {
        remainingContainer.innerHTML = '<p style="padding: 20px; text-align: center;">没有找到肥皂盒数据。</p>';
        return;
    }
    
    // 清空容器
    remainingContainer.innerHTML = '';
    
    // 创建表格
    const table = document.createElement('table');
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';
    table.style.marginBottom = '20px';
    
    // 创建表头
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr style="background-color: #f5f5f5; font-weight: bold;">
            <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">肥皂盒名称</th>
            <th style="padding: 10px; text-align: center; border-bottom: 1px solid #ddd;">ID</th>
            <th style="padding: 10px; text-align: center; border-bottom: 1px solid #ddd;">剩余数量</th>
            <th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">单抽价格</th>
            <th style="padding: 10px; text-align: center; border-bottom: 1px solid #ddd;">操作</th>
        </tr>
    `;
    table.appendChild(thead);
    
    // 创建表格主体
    const tbody = document.createElement('tbody');
    
    // 遍历肥皂盒数据
    chests.forEach((chest, index) => {
        const row = document.createElement('tr');
        
        // 如果有变化，设置特殊背景色
        if (chest.hasChanged) {
            // 减少(剩余变少)用浅红色，增加(剩余变多)用浅绿色
            row.style.backgroundColor = chest.changeDirection === 'decrease' ? '#ffebee' : '#e8f5e9';
        } else {
            row.style.backgroundColor = index % 2 === 0 ? '#ffffff' : '#f9f9f9';
        }
        
        row.style.transition = 'background-color 0.2s';
        
        // 鼠标悬停效果
        row.addEventListener('mouseenter', () => {
            row.style.backgroundColor = '#e9f5fe';
        });
        row.addEventListener('mouseleave', () => {
            if (chest.hasChanged) {
                row.style.backgroundColor = chest.changeDirection === 'decrease' ? '#ffebee' : '#e8f5e9';
            } else {
                row.style.backgroundColor = index % 2 === 0 ? '#ffffff' : '#f9f9f9';
            }
        });
        
        // 肥皂盒名称
        const nameCell = document.createElement('td');
        nameCell.style.padding = '10px';
        nameCell.style.textAlign = 'left';
        nameCell.style.borderBottom = '1px solid #ddd';
        nameCell.textContent = chest.name || '未知肥皂盒';
        
        // ID
        const idCell = document.createElement('td');
        idCell.style.padding = '10px';
        idCell.style.textAlign = 'center';
        idCell.style.borderBottom = '1px solid #ddd';
        idCell.textContent = chest.id || '';
        
        // 剩余数量
        const remainingCell = document.createElement('td');
        remainingCell.style.padding = '10px';
        remainingCell.style.textAlign = 'center';
        remainingCell.style.borderBottom = '1px solid #ddd';
        remainingCell.style.fontWeight = 'bold';
        
        // 设置剩余数量颜色
        if (chest.remainingDraw < 10) {
            remainingCell.style.color = '#f44336'; // 红色
        } else {
            remainingCell.style.color = '#4CAF50'; // 绿色
        }
        
        // 显示剩余数量，如果有变化则显示变化前的数量
        if (chest.hasChanged) {
            const changeIcon = chest.changeDirection === 'decrease' ? '↓' : '↑';
            const changeText = document.createElement('span');
            changeText.style.fontSize = '0.85em';
            changeText.style.color = chest.changeDirection === 'decrease' ? '#f44336' : '#4CAF50';
            changeText.style.marginLeft = '5px';
            changeText.textContent = `(${changeIcon} 之前: ${chest.oldRemaining})`;
            
            remainingCell.textContent = `${chest.remainingDraw} / ${chest.totalDraw || 0} `;
            remainingCell.appendChild(changeText);
        } else {
            remainingCell.textContent = `${chest.remainingDraw} / ${chest.totalDraw || 0}`;
        }
        
        // 单抽价格
        const priceCell = document.createElement('td');
        priceCell.style.padding = '10px';
        priceCell.style.textAlign = 'right';
        priceCell.style.borderBottom = '1px solid #ddd';
        priceCell.textContent = chest.oncePrice ? `¥${chest.oncePrice.toFixed(2)}` : 'N/A';
        
        // 操作按钮
        const actionCell = document.createElement('td');
        actionCell.style.padding = '10px';
        actionCell.style.textAlign = 'center';
        actionCell.style.borderBottom = '1px solid #ddd';
        
        // 查看详情按钮
        const viewBtn = document.createElement('button');
        viewBtn.textContent = '查看详情';
        viewBtn.style.padding = '5px 10px';
        viewBtn.style.backgroundColor = '#3b82f6';
        viewBtn.style.color = 'white';
        viewBtn.style.border = 'none';
        viewBtn.style.borderRadius = '4px';
        viewBtn.style.cursor = 'pointer';
        
        // 点击查看详情 - 在新标签页中打开
        viewBtn.addEventListener('click', () => {
            // 获取当前URL参数，用于构建返回链接
            const currentUrlParams = new URLSearchParams(window.location.search);
            const currentSort = currentUrlParams.get('sort') || 'sortOrder';
            const currentOrder = currentUrlParams.get('order') || 'asc';
            const currentPage = currentUrlParams.get('page') || '1';
            const returnPath = `?sort=${currentSort}&order=${currentOrder}&page=${currentPage}`;
            
            // 在新标签页中打开详情页
            const detailUrl = `/chest-detail?id=${chest.id}&return_path=${encodeURIComponent(returnPath)}`;
            window.open(detailUrl, '_blank');
        });
        
        actionCell.appendChild(viewBtn);
        
        // 添加单元格到行
        row.appendChild(nameCell);
        row.appendChild(idCell);
        row.appendChild(remainingCell);
        row.appendChild(priceCell);
        row.appendChild(actionCell);
        
        // 添加行到表格
        tbody.appendChild(row);
    });
    
    table.appendChild(tbody);
    remainingContainer.appendChild(table);
}

/**
 * 切换剩余数量排序模态框的显示状态
 * @param {boolean} show - 是否显示模态框
 */
function toggleRemainingDrawModal(show) {
    const modal = document.getElementById('remaining-draw-modal');
    if (!modal) return;
    
    if (show) {
        modal.style.display = 'block';
        remainingDrawModalOpen = true;
        
        // 设置初始页码值
        const pageCountSelect = document.getElementById('remaining-page-count');
        if (pageCountSelect) {
            pageCountSelect.value = remainingPageCount.toString();
        }
        
        // 加载数据
        loadRemainingDrawChests();
    } else {
        modal.style.display = 'none';
        remainingDrawModalOpen = false;
    }
}

/**
 * 清除剩余数量缓存
 */
function clearRemainingCache() {
    localStorage.removeItem(REMAINING_CACHE_KEY);
    showToast('剩余数量缓存已清除', 'info');
}

// 仅使用一个统一的初始化函数
document.addEventListener('DOMContentLoaded', async function initPage() {
    try {
        // 移除监听器，确保只执行一次
        document.removeEventListener('DOMContentLoaded', initPage);
        console.log('DOM已加载完成，开始初始化页面...');
        
        // 初始化DOM元素引用
        const sortField = document.getElementById('sort-field');
        const sortDirection = document.getElementById('sort-direction');
        const pageNumber = document.getElementById('page-number');
        const prevPageBtn = document.getElementById('prev-page');
        const nextPageBtn = document.getElementById('next-page');
        const pageIndicator = document.getElementById('page-indicator');
        const modalCloseBtn = document.querySelector('#chest-detail-modal .close-btn');
        const refreshLogsBtn = document.getElementById('refresh-logs-btn');
        const sortBtns = document.querySelectorAll('.sort-btn');
        const chestSearchInput = document.getElementById('chest-id-search');
        const chestSearchBtn = document.getElementById('chest-search-btn');
        
        // 剩余数量排序相关元素
        const remainingDrawBtn = document.getElementById('remaining-draw-btn');
        const remainingCloseBtn = document.getElementById('remaining-close-btn');
        const refreshRemainingBtn = document.getElementById('refresh-remaining-btn');
        const remainingPageCountSelect = document.getElementById('remaining-page-count');
        const applyRemainingSettingsBtn = document.getElementById('apply-remaining-settings');
        const clearRemainingCacheBtn = document.getElementById('clear-remaining-cache');
        
        // 从URL参数中读取排序和分页设置
        const urlParams = new URLSearchParams(window.location.search);
        const urlSort = urlParams.get('sort');
        const urlOrder = urlParams.get('order');
        const urlPage = urlParams.get('page');
        
        // 如果URL中有排序参数，先应用到UI
        if (urlSort && sortField) {
            sortField.value = urlSort;
            document.querySelectorAll('.sort-btn:not(.sort-dir-btn)').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.sort === urlSort);
            });
        }
        
        if (urlOrder && sortDirection) {
            sortDirection.value = urlOrder;
            document.querySelectorAll('.sort-dir-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.direction === urlOrder);
            });
        }
        
        if (urlPage && pageNumber) {
            pageNumber.value = parseInt(urlPage) || 1;
        }
        
        // 并行加载肥皂盒数据和抽取记录
        console.log('开始并行加载数据...');
        try {
            // 使用Promise.all同时发起两个请求
            await Promise.all([
                window.loadChests(),
                window.loadChestLogs()
            ]);
            console.log('完成并行数据加载');
        } catch (error) {
            console.error('数据加载失败:', error);
            // 即使一个请求失败也继续执行
        }
        
        // 设置页码指示器更新函数
        window.updatePageIndicator = function() {
            if (pageIndicator) {
                pageIndicator.textContent = `第 ${window.currentPage || 1} 页`;
            }
        };
        
        // 添加肥皂盒ID搜索功能
        if (chestSearchBtn && chestSearchInput) {
            // 点击搜索按钮时跳转
            chestSearchBtn.addEventListener('click', function() {
                handleChestSearch();
            });
            
            // 按下回车键时也触发搜索
            chestSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleChestSearch();
                }
            });
        }
        
        // 添加剩余数量排序按钮事件
        if (remainingDrawBtn) {
            remainingDrawBtn.addEventListener('click', function() {
                toggleRemainingDrawModal(true);
            });
        }
        
        // 添加剩余数量排序模态框关闭按钮事件
        if (remainingCloseBtn) {
            remainingCloseBtn.addEventListener('click', function() {
                toggleRemainingDrawModal(false);
            });
        }
        
        // 添加剩余数量排序刷新按钮事件
        if (refreshRemainingBtn) {
            refreshRemainingBtn.addEventListener('click', function() {
                this.classList.add('rotating');
                loadRemainingDrawChests().finally(() => {
                    setTimeout(() => {
                        this.classList.remove('rotating');
                    }, 1000);
                });
            });
        }
        
        // 添加应用设置按钮事件
        if (applyRemainingSettingsBtn && remainingPageCountSelect) {
            applyRemainingSettingsBtn.addEventListener('click', function() {
                remainingPageCount = parseInt(remainingPageCountSelect.value) || 1;
                loadRemainingDrawChests();
            });
        }
        
        // 添加清除缓存按钮事件
        if (clearRemainingCacheBtn) {
            clearRemainingCacheBtn.addEventListener('click', function() {
                clearRemainingCache();
                // 清除缓存后重新加载数据
                loadRemainingDrawChests();
            });
        }
        
        // 点击模态框外部关闭模态框
        window.addEventListener('click', function(event) {
            const remainingModal = document.getElementById('remaining-draw-modal');
            if (event.target === remainingModal) {
                toggleRemainingDrawModal(false);
            }
        });
        
        // 肥皂盒ID搜索处理函数
        function handleChestSearch() {
            const chestId = chestSearchInput.value.trim();
            if (!chestId) {
                showToast('请输入肥皂盒ID', 'error');
                return;
            }
            
            // 如果全局函数已定义，则直接打开详情
            if (typeof window.openChestDetail === 'function') {
                window.openChestDetail(chestId);
            } else {
                // 根据当前系统的URL格式跳转到详情页面
                const returnParams = new URLSearchParams();
                if (sortField) returnParams.set('sort', sortField.value);
                if (sortDirection) returnParams.set('order', sortDirection.value);
                if (pageNumber) returnParams.set('page', pageNumber.value);
                const returnPath = returnParams.toString() ? `?${returnParams.toString()}` : '';
                
                window.location.href = `/chest-detail?id=${chestId}&return_path=${encodeURIComponent(returnPath)}`;
            }
        }

        // 添加刷新按钮事件
        if (refreshLogsBtn) {
            refreshLogsBtn.addEventListener('click', async function() {
                // 添加旋转动画
                this.classList.add('rotating');
                try {
                    // 重新加载活动记录
                    await window.loadChestLogs();
                } finally {
                    // 无论成功或失败，1秒后移除旋转动画
                    setTimeout(() => {
                        this.classList.remove('rotating');
                    }, 1000);
                }
            });
        }
        
        // 检查URL参数，是否需要打开特定肥皂盒详情
        const openChestId = urlParams.get('open_chest');
        if (openChestId) {
            console.log('检测到open_chest参数，跳转到详情页面:', openChestId);
            // 直接跳转到肥皂盒详情页面
            const returnParams = new URLSearchParams();
            if (sortField) returnParams.set('sort', sortField.value);
            if (sortDirection) returnParams.set('order', sortDirection.value);
            if (pageNumber) returnParams.set('page', pageNumber.value);
            const returnPath = returnParams.toString() ? `?${returnParams.toString()}` : '';
            
            window.location.href = `/chest-detail?id=${openChestId}&return_path=${encodeURIComponent(returnPath)}`;
        }
        
        // 绑定排序按钮事件
        if (sortBtns) {
            sortBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 判断是排序字段按钮还是排序方向按钮
                    if (btn.classList.contains('sort-dir-btn')) {
                        // 如果已经激活，不做操作
                        if (btn.classList.contains('active')) {
                            return;
                        }
                        
                        // 只更新排序方向按钮状态
                        document.querySelectorAll('.sort-dir-btn').forEach(b => {
                            b.classList.remove('active');
                        });
                        btn.classList.add('active');
                        
                        // 更新排序方向select
                        if (sortDirection) {
                            sortDirection.value = btn.dataset.direction;
                        }
                    } else {
                        // 如果已经激活，不做操作
                        if (btn.classList.contains('active')) {
                            return;
                        }
                        
                        // 只更新排序字段按钮状态
                        document.querySelectorAll('.sort-btn:not(.sort-dir-btn)').forEach(b => {
                            b.classList.remove('active');
                        });
                        btn.classList.add('active');
                        
                        // 更新排序字段select
                        if (sortField) {
                            sortField.value = btn.dataset.sort;
                        }
                    }
                    
                    // 重新加载肥皂盒数据
                    window.loadChests();
                });
            });
        }
        
        // 排序和分页控制事件监听
        if (sortField) sortField.addEventListener('change', window.loadChests);
        if (sortDirection) sortDirection.addEventListener('change', window.loadChests);
        if (pageNumber) pageNumber.addEventListener('change', handlePageChange);
        if (prevPageBtn) prevPageBtn.addEventListener('click', goToPrevPage);
        if (nextPageBtn) nextPageBtn.addEventListener('click', goToNextPage);
        
        // 详情模态框关闭按钮事件
        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', function() {
                const detailModal = document.getElementById('chest-detail-modal');
                if (detailModal) {
                    detailModal.style.display = 'none';
                }
            });
        }
    } catch (error) {
        console.error('页面初始化失败:', error);
    }
});

// 分页处理函数
function handlePageChange() {
    const pageNumber = document.getElementById('page-number');
    if (!pageNumber) return;
    
    let page = parseInt(pageNumber.value) || 1;
    if (page < 1) page = 1;
    if (window.totalPages && page > window.totalPages) page = window.totalPages;
    
    // 只有当页码真的改变时才重新加载
    if (page !== window.currentPage) {
        pageNumber.value = page;
        window.loadChests();
    }
}

function goToPrevPage() {
    const pageNumber = document.getElementById('page-number');
    if (!pageNumber) return;
    
    let page = parseInt(pageNumber.value) || 1;
    if (page > 1) {
        page--;
        pageNumber.value = page;
        window.loadChests();
    }
}

function goToNextPage() {
    const pageNumber = document.getElementById('page-number');
    if (!pageNumber) return;
    
    let page = parseInt(pageNumber.value) || 1;
    if (window.totalPages && page < window.totalPages) {
        page++;
        pageNumber.value = page;
        window.loadChests();
    }
}

/**
 * 显示提示消息
 * @param {string} message - 提示消息
 * @param {string} type - 消息类型 (success, error, warning)
 */
function showToast(message, type = 'success') {
    // 检查是否已存在toast容器，如果不存在则创建
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        document.body.appendChild(toastContainer);
    }
    
    // 创建新的toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加到容器
    toastContainer.appendChild(toast);
    
    // 设置定时器移除
    setTimeout(() => {
        toast.classList.add('toast-hide');
        setTimeout(() => {
            if (toastContainer.contains(toast)) {
                toastContainer.removeChild(toast);
            }
        }, 300); // 淡出动画后移除
    }, 3000); // 3秒后开始淡出
} 