#!/usr/bin/env python3
"""
CDK监控info API优化测试脚本
专门测试游戏信息获取（updateTime）的串行vs并行性能差异
"""

import asyncio
import aiohttp
import time
import json
from typing import List, Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_GAME_IDS = [
    "1234567",  # 示例游戏ID，需要替换为实际的游戏ID
    "2345678",
    "3456789",
    "4567890",
    "5678901",
    "6789012",
    "7890123",
    "8901234"
]

async def test_single_info_requests(session: aiohttp.ClientSession, game_ids: List[str]) -> Dict[str, Any]:
    """测试单个info请求的性能（串行）"""
    print("🔄 测试串行info请求模式...")
    start_time = time.time()
    
    results = []
    for game_id in game_ids:
        try:
            async with session.get(f"{BASE_URL}/api/games/{game_id}/info") as response:
                if response.status == 200:
                    data = await response.json()
                    update_time = data.get("updateTime") or (data.get("result", {}).get("updateTime"))
                    results.append({
                        "game_id": game_id, 
                        "success": True, 
                        "update_time": update_time
                    })
                else:
                    results.append({"game_id": game_id, "success": False, "error": f"HTTP {response.status}"})
        except Exception as e:
            results.append({"game_id": game_id, "success": False, "error": str(e)})
    
    end_time = time.time()
    duration = end_time - start_time
    
    return {
        "mode": "single_info",
        "duration": duration,
        "total_requests": len(game_ids),
        "successful_requests": sum(1 for r in results if r["success"]),
        "results": results
    }

async def test_batch_info_requests(session: aiohttp.ClientSession, game_ids: List[str]) -> Dict[str, Any]:
    """测试批量info请求的性能（并行）"""
    print("🚀 测试并行info请求模式...")
    start_time = time.time()
    
    batch_size = 8  # 每批并行处理8个游戏
    results = []
    
    # 分批处理，避免同时发送太多请求
    for i in range(0, len(game_ids), batch_size):
        batch = game_ids[i:i + batch_size]
        batch_tasks = []
        
        for game_id in batch:
            task = session.get(f"{BASE_URL}/api/games/{game_id}/info")
            batch_tasks.append(task)
        
        # 并发执行当前批次
        try:
            responses = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            for j, response in enumerate(responses):
                game_id = batch[j]
                if isinstance(response, Exception):
                    results.append({"game_id": game_id, "success": False, "error": str(response)})
                else:
                    try:
                        if response.status == 200:
                            data = await response.json()
                            update_time = data.get("updateTime") or (data.get("result", {}).get("updateTime"))
                            results.append({
                                "game_id": game_id, 
                                "success": True, 
                                "update_time": update_time
                            })
                        else:
                            results.append({"game_id": game_id, "success": False, "error": f"HTTP {response.status}"})
                    except Exception as e:
                        results.append({"game_id": game_id, "success": False, "error": str(e)})
                    finally:
                        response.close()
        except Exception as e:
            for game_id in batch:
                results.append({"game_id": game_id, "success": False, "error": str(e)})
        
        # 批次间短暂延迟
        if i + batch_size < len(game_ids):
            await asyncio.sleep(0.05)  # 50ms延迟
    
    end_time = time.time()
    duration = end_time - start_time
    
    return {
        "mode": "batch_info",
        "duration": duration,
        "total_requests": len(game_ids),
        "successful_requests": sum(1 for r in results if r["success"]),
        "results": results
    }

async def test_concurrent_info_requests(session: aiohttp.ClientSession, game_ids: List[str]) -> Dict[str, Any]:
    """测试完全并发info请求的性能（所有请求同时发送）"""
    print("⚡ 测试完全并发info请求模式...")
    start_time = time.time()
    
    # 创建所有请求任务
    tasks = []
    for game_id in game_ids:
        task = asyncio.create_task(fetch_single_info(session, game_id))
        tasks.append(task)
    
    # 等待所有任务完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果
    processed_results = []
    for i, result in enumerate(results):
        game_id = game_ids[i]
        if isinstance(result, Exception):
            processed_results.append({"game_id": game_id, "success": False, "error": str(result)})
        else:
            processed_results.append(result)
    
    end_time = time.time()
    duration = end_time - start_time
    
    return {
        "mode": "concurrent_info",
        "duration": duration,
        "total_requests": len(game_ids),
        "successful_requests": sum(1 for r in processed_results if r["success"]),
        "results": processed_results
    }

async def fetch_single_info(session: aiohttp.ClientSession, game_id: str) -> Dict[str, Any]:
    """获取单个游戏的信息"""
    try:
        async with session.get(f"{BASE_URL}/api/games/{game_id}/info") as response:
            if response.status == 200:
                data = await response.json()
                update_time = data.get("updateTime") or (data.get("result", {}).get("updateTime"))
                return {
                    "game_id": game_id, 
                    "success": True, 
                    "update_time": update_time
                }
            else:
                return {"game_id": game_id, "success": False, "error": f"HTTP {response.status}"}
    except Exception as e:
        return {"game_id": game_id, "success": False, "error": str(e)}

async def main():
    """主测试函数"""
    print("🧪 CDK监控info API优化性能测试")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        # 测试串行请求
        single_result = await test_single_info_requests(session, TEST_GAME_IDS)
        
        # 等待一秒
        await asyncio.sleep(1)
        
        # 测试批量并行请求
        batch_result = await test_batch_info_requests(session, TEST_GAME_IDS)
        
        # 等待一秒
        await asyncio.sleep(1)
        
        # 测试完全并发请求
        concurrent_result = await test_concurrent_info_requests(session, TEST_GAME_IDS)
        
        # 输出结果
        print("\n📈 测试结果:")
        print("=" * 50)
        
        print(f"串行请求模式:")
        print(f"  ⏱️  耗时: {single_result['duration']:.2f}秒")
        print(f"  ✅ 成功: {single_result['successful_requests']}/{single_result['total_requests']}")
        
        print(f"\n批量并行请求模式:")
        print(f"  ⏱️  耗时: {batch_result['duration']:.2f}秒")
        print(f"  ✅ 成功: {batch_result['successful_requests']}/{batch_result['total_requests']}")
        
        print(f"\n完全并发请求模式:")
        print(f"  ⏱️  耗时: {concurrent_result['duration']:.2f}秒")
        print(f"  ✅ 成功: {concurrent_result['successful_requests']}/{concurrent_result['total_requests']}")
        
        # 计算性能提升
        if single_result['duration'] > 0:
            batch_improvement = ((single_result['duration'] - batch_result['duration']) / single_result['duration']) * 100
            concurrent_improvement = ((single_result['duration'] - concurrent_result['duration']) / single_result['duration']) * 100
            
            print(f"\n🚀 性能提升:")
            print(f"  批量并行模式: {batch_improvement:.1f}%")
            print(f"  完全并发模式: {concurrent_improvement:.1f}%")
        
        # 保存详细结果到文件
        results = {
            "timestamp": time.time(),
            "test_game_count": len(TEST_GAME_IDS),
            "single_mode": single_result,
            "batch_mode": batch_result,
            "concurrent_mode": concurrent_result
        }
        
        with open("info_optimization_test_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细结果已保存到: info_optimization_test_results.json")

if __name__ == "__main__":
    asyncio.run(main())
