{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}监控中心 - PyWatcher{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', path='css/monitor.css') }}">
{% endblock %}

{% block content %}
<div class="container main-container">
    <h1 class="page-title">监控中心</h1>
    
    <!-- CDK低价监控部分 -->
    <div class="card mb-4">
        <div class="card-header">
            <h2 class="card-title">CDK低价监控</h2>
        </div>
        <div class="card-body">
            <p class="text-muted mb-4">CDK监控功能即将推出，敬请期待...</p>
        </div>
    </div>
    
    <!-- 肥皂盒监控部分 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="card-title mb-0">肥皂盒监控</h2>
            <a href="#" id="favorites-link" class="btn btn-sm btn-outline-primary">
                {{ icon('star', 'mr-1', 14) }}收藏列表
            </a>
        </div>
        <div class="card-body">
            <!-- 控制面板 -->
            <div class="monitor-control-panel mb-4">
                <div class="row align-items-center">
                    <div class="col-md-4 mb-3 mb-md-0">
                        <!-- 开始/停止按钮 -->
                        <button id="toggleMonitor" class="btn btn-primary btn-lg w-100">
                            {{ icon('play', 'mr-2', 16) }}开始监控
                        </button>
                    </div>
                    <div class="col-md-5 mb-3 mb-md-0">
                        <!-- 监控间隔设置 -->
                        <div class="interval-controls d-flex align-items-center">
                            <label class="mr-2 font-weight-bold">监控间隔:</label>
                            <div class="input-group">
                                <input type="number" id="minInterval" value="5000" min="1000" step="500" class="form-control">
                                <div class="input-group-prepend input-group-append">
                                    <span class="input-group-text">-</span>
                                </div>
                                <input type="number" id="maxInterval" value="18000" min="1000" step="500" class="form-control">
                                <div class="input-group-append">
                                    <span class="input-group-text">ms</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <!-- 自动下单开关 -->
                        <div class="auto-order-switch d-flex align-items-center justify-content-center justify-content-md-end">
                            <label class="mr-2 font-weight-bold">自动下单:</label>
                            <label class="switch-control">
                                <input id="autoOrder" type="checkbox">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 监控模式选择 -->
            <div class="monitor-modes-panel mb-4">
                <h3 class="section-title">监控模式:</h3>
                <div class="monitor-mode-options">
                    <label class="mode-option">
                        <input type="checkbox" value="expected" name="monitorMode" checked>
                        <span class="mode-check"></span>
                        <span class="mode-label">期望</span>
                        <span class="mode-description">监控肥皂盒期望价值</span>
                    </label>
                    
                    <label class="mode-option">
                        <input type="checkbox" value="probability" name="monitorMode" checked>
                        <span class="mode-check"></span>
                        <span class="mode-label">概率</span>
                        <span class="mode-description">监控肥皂盒抽取概率</span>
                    </label>
                    
                    <label class="mode-option">
                        <input type="checkbox" value="wrongPrice" name="monitorMode" checked>
                        <span class="mode-check"></span>
                        <span class="mode-label">错价</span>
                        <span class="mode-description">监控肥皂盒错误定价</span>
                    </label>
                    
                    <label class="mode-option">
                        <input type="checkbox" value="specific" name="monitorMode">
                        <span class="mode-check"></span>
                        <span class="mode-label">特定</span>
                        <span class="mode-description">监控特定肥皂盒</span>
                    </label>
                </div>
            </div>
            
            <!-- 特定监控配置面板 -->
            <div id="specific-monitor-panel" class="monitor-specific-panel mb-4" style="display: none;">
                <div class="specific-header d-flex justify-content-between align-items-center mb-3">
                    <h3 class="section-title">收藏肥皂盒监控:</h3>
                    <div>
                        <button id="refresh-favorites" class="btn btn-sm btn-outline-secondary mr-2">
                            {{ icon('refresh-cw', 'mr-1', 14) }}刷新列表
                        </button>
                        <button id="manage-favorites" class="btn btn-sm btn-outline-primary">
                            {{ icon('edit', 'mr-1', 14) }}管理收藏
                        </button>
                    </div>
                </div>
                <div class="specific-chests-container">
                    <div id="favorite-chests-list" class="favorite-chests-list">
                        <div class="text-muted text-center py-4">暂无收藏的肥皂盒...</div>
                    </div>
                </div>
            </div>
            
            <!-- 监控记录 -->
            <div class="monitor-logs-panel">
                <div class="logs-header d-flex justify-content-between align-items-center">
                    <h3 class="section-title mb-0">监控记录:</h3>
                    <button id="clearLogs" class="btn btn-sm btn-outline-secondary">
                        {{ icon('trash', 'mr-1', 14) }}清空记录
                    </button>
                </div>
                <div id="monitorLogs" class="monitor-logs-container">
                    <div class="text-muted text-center py-4">监控记录将显示在这里...</div>
                </div>
            </div>
            
            <!-- 命中记录表格 -->
            <div class="monitor-hits-panel mt-4">
                <div class="hits-header d-flex justify-content-between align-items-center mb-3">
                    <h3 class="section-title mb-0">命中记录:</h3>
                    <button id="clearHits" class="btn btn-sm btn-outline-secondary">
                        {{ icon('trash', 'mr-1', 14) }}清空记录
                    </button>
                </div>
                <div class="monitor-hits-container">
                    <table class="table table-hover table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>肥皂盒ID</th>
                                <th>监控类型</th>
                                <th>单抽价格</th>
                                <th>多抽价格</th>
                                <th>命中时间</th>
                            </tr>
                        </thead>
                        <tbody id="monitorHits">
                            <tr class="text-center text-muted">
                                <td colspan="5">暂无命中记录...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 收藏管理模态框 -->
<div id="favorites-modal" class="favorites-modal">
    <div class="favorites-modal-content">
        <div class="favorites-modal-header">
            <h3 class="favorites-modal-title">管理收藏肥皂盒</h3>
            <button class="favorites-modal-close">&times;</button>
        </div>
        <div class="favorites-modal-body">
            <div class="form-group mb-3">
                <label for="add-chest-id">添加肥皂盒ID:</label>
                <div class="input-group">
                    <input type="text" id="add-chest-id" class="form-control" placeholder="输入肥皂盒ID">
                    <div class="input-group-append">
                        <button id="add-chest-btn" class="btn btn-primary">添加</button>
                    </div>
                </div>
                <small class="form-text text-muted">输入肥皂盒ID添加到收藏列表</small>
            </div>
            <div class="mb-3">
                <h4 class="mb-3">当前收藏:</h4>
                <div id="favorites-list" class="favorites-list">
                    <div class="text-muted text-center py-4">暂无收藏的肥皂盒...</div>
                </div>
            </div>
        </div>
        <div class="favorites-modal-footer">
            <button id="close-favorites-modal" class="btn btn-secondary">关闭</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='js/monitor.js') }}"></script>
{% endblock %} 