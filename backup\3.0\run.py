import uvicorn
import os
import sys

if __name__ == "__main__":
    # 项目的根目录是'pywatcher'包所在的目录。
    # __file__ -> /path/to/project/pywatcher/run.py
    # os.path.dirname(__file__) -> /path/to/project/pywatcher
    # os.path.dirname(os.path.dirname(__file__)) -> /path/to/project (项目根目录)
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # 将项目根目录添加到 sys.path
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # 将当前工作目录更改为项目根目录, 确保所有相对路径(如 config.json)正确
    os.chdir(project_root)

    # 现在, uvicorn可以从正确的根目录启动, 并将'pywatcher'识别为一个包
    # 临时禁用 reloader 以排查问题
    uvicorn.run("pywatcher.api.main:app", host="0.0.0.0", port=8000, reload=False) 