import aiohttp
import json
import asyncio
import logging
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)

class SteamPyClient:
    """
    与 steampy.com 后端 API 交互的异步客户端。
    该实现基于 Web/steampy_client.py 以确保兼容性。
    """
    BASE_URL = "https://steampy.com/xboot"

    def __init__(self, session: aiohttp.ClientSession, access_token: Optional[str] = None):
        self.session = session
        self.access_token = access_token
        if self.access_token:
            logger.info(f"SteamPyClient is using an access token.")

    async def _send_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """统一的请求发送方法"""
        url = f"{self.BASE_URL}{endpoint}"
        
        headers = kwargs.get("headers", {})
        if self.access_token:
            headers["accesstoken"] = self.access_token
        kwargs["headers"] = headers

        try:
            async with self.session.request(method, url, **kwargs) as response:
                status = response.status
                content_type = response.headers.get('Content-Type', '')
                
                # 尝试解析 JSON
                if 'application/json' in content_type:
                    json_response = await response.json()
                    # 即使是JSON，也检查HTTP状态码
                    if status >= 400:
                         logger.error(f"HTTP Error {status} for {url}. Response: {json_response}")
                         # 将错误信息构建得更像 aiohttp 的异常，以便上层统一处理
                         return {"error": f"HTTP {status}", "status_code": status, "details": json_response}
                    return json_response
                else:
                    text_content = await response.text()
                    logger.warning(f"Response from {url} (status {status}) has Content-Type '{content_type}', not JSON. Body: {text_content[:200]}...")
                    if status >= 400:
                         return {"error": f"HTTP {status}", "status_code": status, "details": text_content}
                    # 对于非JSON的成功响应，也返回一个结构化的字典
                    return {"raw_text": text_content, "status_code": status}

        except aiohttp.ClientResponseError as e:
            logger.error(f"ClientResponseError for {url}: {e.status}, message='{e.message}'")
            return {"error": str(e), "status_code": e.status, "message": e.message}
        except Exception as e:
            logger.error(f"Unexpected error during request to {url}: {e}", exc_info=True)
            return {"error": f"Unexpected error: {e}"}

    async def search_game(self, game_name: str, page_number: int = 1, page_size: int = 100) -> Dict[str, Any]:
        """根据名称在 steampy.com 搜索游戏。"""
        logger.info(f"Searching for game: {game_name}...")
        endpoint = "/steamGame/keyByName"
        params = {
            "pageNumber": page_number, 
            "pageSize": page_size,
            "sort": "keyTx", 
            "order": "asc", 
            "gameName": game_name,
        }
        return await self._send_request("GET", endpoint, params=params)

    async def get_game_sellers(self, game_id: str, page_size: int = 5) -> Dict[str, Any]:
        """
        获取指定游戏的卖家列表.
        API Doc: /xboot/steamKeySale/listSale
        """
        if not self.access_token:
            logger.error("Cannot get game sellers without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}

        logger.info(f"Fetching sellers for game ID: {game_id}")
        endpoint = "/steamKeySale/listSale"
        params = {
            "pageNumber": 1,
            "pageSize": page_size,
            "sort": "keyPrice",
            "order": "asc",
            "gameId": game_id,
        }
        
        # 使用通用发送请求方法
        return await self._send_request("GET", endpoint, params=params)

    async def get_chests(self, page_number: int = 1, page_size: int = 10, 
                         sort: str = "sortOrder", order: str = "asc") -> Dict[str, Any]:
        """
        获取肥皂盒记录列表
        API: /xboot/chest/showPageV2
        """
        if not self.access_token:
            logger.error("Cannot get chests without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}
            
        logger.info(f"Fetching chests (page {page_number}, sort by {sort}, order {order})")
        endpoint = "/chest/showPageV2"
        params = {
            "pageNumber": page_number,
            "pageSize": page_size,
            "sort": sort,
            "order": order,
        }
        
        return await self._send_request("GET", endpoint, params=params)

    # --- 保留其他方法的桩代码，以防其他部分调用 ---

    async def get_login_sms(self, phone: str) -> Dict[str, Any]:
        logger.info(f"Requesting SMS code for phone: {phone}...")
        endpoint = f"/common/captcha/sendLoginSms/{phone}"
        return await self._send_request("GET", endpoint)

    async def verify_login_code(self, phone: str, code: str) -> Dict[str, Any]:
        logger.info(f"Attempting login with phone: {phone}...")
        endpoint = "/user/smsLogin"
        data = {"mobile": phone, "code": code}
        return await self._send_request("POST", endpoint, data=data)

    async def fetch_game_price(self, game_name: str) -> float:
        logger.warning("fetch_game_price is a placeholder and not fully implemented.")
        return 0.0

    async def get_market_item_price(self, market_hash_name: str) -> Optional[float]:
        logger.warning("get_market_item_price is a placeholder and not fully implemented.")
        return 0.0
        
    async def get_chest_details(self, chest_id: str) -> Dict[str, Any]:
        """
        获取肥皂盒详情
        API: /xboot/chest/showOne
        """
        if not self.access_token:
            logger.error(f"Cannot get chest details for {chest_id} without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}
            
        logger.info(f"Fetching details for chest: {chest_id}")
        endpoint = "/chest/showOne"
        params = {
            "chestId": chest_id
        }
        
        response = await self._send_request("GET", endpoint, params=params)
        
        # 为响应添加success字段，方便上层统一处理
        if isinstance(response, dict) and "error" not in response and "code" in response:
            if response.get("code") == 200 and "success" not in response:
                response["success"] = True
            elif response.get("code") != 200 and "success" not in response:
                response["success"] = False
        elif isinstance(response, dict) and "error" in response and "success" not in response:
            response["success"] = False
            
        return response
        
    async def get_chest_games(self, chest_id: str, page_number: int = 1, page_size: int = 100,
                           sort_order: str = "lv", order_direction: str = "asc") -> Dict[str, Any]:
        """
        获取肥皂盒游戏列表
        API: /xboot/chest/showGame
        """
        if not self.access_token:
            logger.error(f"Cannot get chest games for {chest_id} without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}
            
        logger.info(f"Fetching games for chest: {chest_id}")
        endpoint = "/chest/showGame"
        params = {
            "chestId": chest_id,
            "pageNumber": page_number,
            "pageSize": page_size,
            "sort": sort_order,
            "order": order_direction
        }
        
        return await self._send_request("GET", endpoint, params=params) 