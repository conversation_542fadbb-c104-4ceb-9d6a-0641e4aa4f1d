import requests

# 配置信息
BASE_URL = "https://steampy.com/xboot"
PHONE = "16256997814"  # 替换为你的手机号
HEADERS = {"User-Agent": "APPAPK"}

# Step 1: 获取验证码ID
init_url = f"{BASE_URL}/common/captcha/init"
init_res = requests.get(init_url, headers=HEADERS)
captcha_id = init_res.json()["result"]  # 提取验证码ID
print(f"验证码ID: {captcha_id}")

# Step 2: 下载图形验证码图片
draw_url = f"{BASE_URL}/common/captcha/draw/{captcha_id}"
# 注意：此处需使用安卓设备的User-Agent（参考截图2）
draw_headers = {"User-Agent": "Dalvik/2.1.0 (Linux; U; Android 12; PFGM00 Build/f89a2a9.0)"}
draw_res = requests.get(draw_url, headers=draw_headers)

# 保存验证码图片到本地
with open("captcha.png", "wb") as f:
    f.write(draw_res.content)
print("验证码图片已保存至 captcha.png，请查看并输入验证码")

# Step 3: 用户输入图形验证码
captcha_code = input("请输入图形验证码: ").strip()

# Step 4: 发送短信验证码
sms_url = f"{BASE_URL}/common/captcha/sendRegistAppSms/{PHONE}?captchald={captcha_id}&code={captcha_code}"
sms_res = requests.get(sms_url, headers=HEADERS)
result = sms_res.json()

# 检查结果
if result.get("success"):
    print("短信验证码发送成功！")
else:
    print(f"失败原因: {result.get('message')}")