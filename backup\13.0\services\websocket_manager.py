from fastapi import WebSocket
import logging
from typing import List, Dict, Any, Set

# 配置日志
logger = logging.getLogger(__name__)

class ConnectionManager:
    """管理 WebSocket 连接"""
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()  # 使用集合避免重复连接

    async def connect(self, websocket: WebSocket):
        """
        接受WebSocket连接并将其添加到活动连接集合
        """
        try:
            await websocket.accept()
            self.active_connections.add(websocket)
            logger.info(f"WebSocket连接已接受，当前活动连接数: {len(self.active_connections)}")
        except Exception as e:
            logger.error(f"接受WebSocket连接失败: {e}")
            raise

    def disconnect(self, websocket: WebSocket):
        """
        移除WebSocket连接
        """
        try:
            self.active_connections.discard(websocket)  # 使用discard而不是remove，避免键不存在的错误
            logger.info(f"WebSocket连接已断开，剩余活动连接数: {len(self.active_connections)}")
        except Exception as e:
            logger.error(f"断开WebSocket连接时出错: {e}")

    async def broadcast(self, message: str):
        """
        向所有连接的客户端广播文本消息
        """
        if not self.active_connections:
            logger.warning("尝试广播消息，但没有活动连接")
            return

        logger.info(f"开始广播文本消息给 {len(self.active_connections)} 个客户端")
        failed_connections = []

        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"向客户端发送文本消息失败: {e}")
                failed_connections.append(connection)

        # 移除失败的连接
        for conn in failed_connections:
            self.active_connections.discard(conn)

        if failed_connections:
            logger.warning(f"移除了 {len(failed_connections)} 个失败的连接")
            
    async def broadcast_json(self, message: Dict[str, Any]):
        """
        向所有连接的客户端广播JSON消息
        """
        if not self.active_connections:
            logger.warning("尝试广播JSON消息，但没有活动连接")
            return

        logger.info(f"开始广播JSON消息给 {len(self.active_connections)} 个客户端: {message}")
        failed_connections = []

        for connection in self.active_connections:
            try:
                await connection.send_json(message)
                logger.debug(f"成功发送JSON消息到一个客户端")
            except Exception as e:
                logger.error(f"向客户端发送JSON消息失败: {e}")
                failed_connections.append(connection)

        # 移除失败的连接
        for conn in failed_connections:
            self.active_connections.discard(conn)

        if failed_connections:
            logger.warning(f"移除了 {len(failed_connections)} 个失败的连接")

# 创建一个全局的连接管理器实例
websocket_manager = ConnectionManager() 