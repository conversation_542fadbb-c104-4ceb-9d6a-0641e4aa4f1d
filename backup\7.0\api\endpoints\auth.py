from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.api.dependencies import get_steam_client_for_login

router = APIRouter(prefix="/login", tags=["Authentication"])

class SmsRequest(BaseModel):
    phone: str = Field(..., description="手机号码")

@router.post("/sms")
async def request_sms_code(
    payload: SmsRequest,
    client: SteamPyClient = Depends(get_steam_client_for_login)
):
    """请求发送短信登录验证码。"""
    # 注意：登录和添加账户的逻辑已经移动到 /api/accounts/login
    result = await client.get_login_sms(payload.phone)
    if result and result.get("code") == 200:
        return {"message": "验证码发送请求已成功处理。"}
    else:
        raise HTTPException(status_code=400, detail=result.get("message", "发送验证码失败。")) 