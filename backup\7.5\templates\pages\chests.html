{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}肥皂盒 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <div class="header-content">
        <h1>
            {{ icon('box') }}
            肥皂盒记录
        </h1>
        <p class="page-description">
            查看和管理您的肥皂盒记录。
        </p>
    </div>
</header>

<section>
    <div class="card sort-card">
        <!-- 排序控制区域 -->
        <div class="sort-controls">
            <div class="sort-btn-group">
                <button class="sort-btn active" data-sort="sortOrder">默认排序</button>
                <button class="sort-btn" data-sort="oncePrice">单抽价格</button>
                <button class="sort-btn sort-dir-btn active" data-direction="asc">升序</button>
                <button class="sort-btn sort-dir-btn" data-direction="desc">降序</button>
            </div>
            
            <!-- 隐藏的下拉选择器，用于兼容现有代码 -->
            <select id="sort-field" class="sort-select" style="display: none;">
                <option value="sortOrder" selected>默认</option>
                <option value="oncePrice">单抽价格</option>
            </select>
            <select id="sort-direction" class="sort-select" style="display: none;">
                <option value="asc" selected>升序</option>
                <option value="desc">降序</option>
            </select>
            
            <!-- 页码控制 -->
            <div class="page-control">
                <span>页码：</span>
                <input type="number" id="page-number" min="1" value="1" class="page-input">
            </div>
        </div>
    </div>
</section>

<section>
    <div class="card">
        <div id="chests-container">
            <div class="loading-indicator">
                <div class="spin"></div>
                <span>正在加载肥皂盒数据...</span>
            </div>
        </div>
    </div>
</section>

<!-- 肥皂盒列表项模板 -->
<template id="chest-item-template">
    <div class="chest-item" data-chest-id="">
        <div class="chest-header">
            <div class="chest-title">
                <h3 class="chest-name"></h3>
                <span class="chest-id" style="display: none;"></span>
            </div>
            <div class="chest-probability-badge">
                <span class="chest-probability"></span>
            </div>
            <div class="chest-badge" style="display: none;"></div>
        </div>
        <div class="chest-details">
            <div class="chest-detail">
                <span class="detail-label">卖家:</span>
                <span class="chest-seller"></span>
            </div>
            <div class="chest-detail">
                <span class="detail-label">单抽价格:</span>
                <span class="chest-price"></span>
            </div>
            <div class="chest-detail">
                <span class="detail-label">抽取进度:</span>
                <span class="chest-progress">
                    <span class="chest-remain"></span>/<span class="chest-total"></span>
                </span>
            </div>
        </div>
    </div>
</template>

<!-- 肥皂盒详情模态框 -->
<div id="chest-detail-modal" class="modal">
    <div class="modal-content chest-detail-content">
        <div class="modal-header">
            <div class="chest-header-info">
                <h3 id="detail-chest-title">肥皂盒详情</h3>
                <div class="chest-subtitle">
                    <span class="chest-id-label">ID: <span id="detail-chest-id"></span></span>
                    <span class="chest-seller-label">卖家: <span id="detail-seller"></span></span>
                </div>
            </div>
            <div class="modal-actions">
                <button class="detail-refresh-btn" id="refresh-chest-btn" title="刷新">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M23 4v6h-6"></path>
                        <path d="M1 20v-6h6"></path>
                        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                        <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                    </svg>
                </button>
                <button class="close-btn" title="关闭">&times;</button>
            </div>
        </div>
        <div class="modal-body">
            <!-- 加载状态 -->
            <div id="detail-loading" class="loading-indicator">
                <div class="spin"></div>
                <span>正在加载详情...</span>
            </div>
            
            <!-- 基本信息 -->
            <div id="chest-basic-info" class="detail-section" style="display: none;">
                <!-- 顶部概况 -->
                <div class="chest-summary">
                    <div class="chest-progress-info">
                        <div class="chest-draw-progress">
                            <span id="detail-remain" class="remain-value"></span>/<span id="detail-total"></span>
                        </div>
                    </div>
                    <div class="chest-actions">
                        <button class="action-btn chest-draw-btn once-draw-btn">
                            抽 1 次
                            <span class="price-tag" id="detail-once-price">¥90.00</span>
                        </button>
                        <button class="action-btn chest-draw-btn multi-draw-btn">
                            抽 <span id="detail-multi-draw">3</span> 次
                            <span class="price-tag" id="detail-multi-price">¥270.00</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 游戏列表 -->
            <div id="chest-games-list" class="games-section" style="display: none;">
                <!-- 游戏池子将在这里动态生成 -->
            </div>
            
            <!-- 错误信息 -->
            <div id="detail-error" class="error-message" style="display: none;">
                <p>获取详情失败，请稍后再试。</p>
            </div>
        </div>
    </div>
</div>

<!-- 池子模板 -->
<template id="pool-template">
    <div class="game-pool">
        <div class="pool-header">
            <h4 class="pool-name"></h4>
            <span class="pool-probability"></span>
        </div>
        <div class="pool-games"></div>
    </div>
</template>

<!-- 游戏卡片模板 -->
<template id="game-card-template">
    <div class="game-card">
        <div class="game-info">
            <h5 class="game-title"></h5>
            <div class="game-status">
                <span class="game-stock"></span>
            </div>
        </div>
    </div>
</template>

<!-- 分页控制 -->
<div class="pagination-controls">
    <button id="prev-page" class="pagination-btn" disabled>上一页</button>
    <span id="page-indicator">第 1 页</span>
    <button id="next-page" class="pagination-btn">下一页</button>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/chests.js') }}" defer></script>
<script>
    // 排序按钮点击事件
    document.addEventListener('DOMContentLoaded', function() {
        const sortBtns = document.querySelectorAll('.sort-btn:not(.sort-dir-btn)');
        const sortDirBtns = document.querySelectorAll('.sort-dir-btn');
        const sortFieldSelect = document.getElementById('sort-field');
        const sortDirectionSelect = document.getElementById('sort-direction');
        
        // 排序字段点击
        sortBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 跳过排序方向按钮
                if (this.classList.contains('sort-dir-btn')) return;
                
                // 移除所有活动状态
                sortBtns.forEach(b => {
                    if (!b.classList.contains('sort-dir-btn')) {
                        b.classList.remove('active');
                    }
                });
                
                // 添加活动状态
                this.classList.add('active');
                
                // 更新隐藏的选择器
                if (sortFieldSelect) {
                    sortFieldSelect.value = this.dataset.sort;
                    
                    // 触发变更事件
                    const event = new Event('change');
                    sortFieldSelect.dispatchEvent(event);
                }
            });
        });
        
        // 排序方向点击
        sortDirBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有活动状态
                sortDirBtns.forEach(b => b.classList.remove('active'));
                
                // 添加活动状态
                this.classList.add('active');
                
                // 更新隐藏的选择器
                if (sortDirectionSelect) {
                    sortDirectionSelect.value = this.dataset.direction;
                    
                    // 触发变更事件
                    const event = new Event('change');
                    sortDirectionSelect.dispatchEvent(event);
                }
            });
        });
    });
</script>
{% endblock %} 