from pydantic import BaseModel, Field
from typing import Optional, Literal
from datetime import datetime

class MobileAccount(BaseModel):
    """用于短信登录和存储的账户模型"""
    id: str = Field(..., description="通常是手机号")
    name: str = Field("", description="用户备注或昵称")
    access_token: str = Field(..., description="从API获取的访问令牌")
    account_type: str = "SMS" # 保持和旧版兼容，可选值: SMS, primary, order_primary, order_secondary, cdk_query
    is_cdk_query: bool = Field(False, description="是否为CDK专用查询账号")

class WebAccount(BaseModel):
    """Web端登录账户模型"""
    id: str = Field(..., description="账户唯一标识")
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码(加密存储)")
    access_token: str = Field(..., description="从API获取的访问令牌")
    account_type: str = Field(default="web_cdk_query", description="账户类型，固定为web_cdk_query")
    identifier: str = Field(..., description="账户标识符，通常为用户名")
    is_active: bool = Field(default=True, description="账户是否活跃")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    last_used: Optional[datetime] = Field(None, description="最后使用时间") 