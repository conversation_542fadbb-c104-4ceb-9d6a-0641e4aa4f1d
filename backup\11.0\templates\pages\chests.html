{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}肥皂盒 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <div class="header-content">
        <h1>
            {{ icon('box') }}
            肥皂盒记录
        </h1>
        <p class="page-description">
            查看和管理您的肥皂盒记录。
        </p>
    </div>
</header>

<!-- 将抽取记录移到这里（图3红框位置） -->
<div>
    <!-- 删除原标题，直接使用高级卡片设计 -->
    <div id="chest-logs-container">
        <div class="loading-indicator">
            <div class="spin"></div>
            <span>正在加载记录...</span>
        </div>
        <div class="activity-feed" style="border-radius: 12px; background: linear-gradient(135deg, #1a73e8 0%, #3490dc 100%); box-shadow: 0 8px 16px rgba(33,150,243,0.15); margin: 10px 0; padding: 0; overflow: hidden; position: relative; z-index: 10;">
            <div class="activity-header" style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-bottom: 1px solid rgba(255,255,255,0.1);">
                <div style="display: flex; align-items: center; color: white;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 10px;">
                        <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"></path>
                    </svg>
                    <span style="font-weight: 600; font-size: 18px;">最近活动</span>
                </div>
                <button id="refresh-logs-btn" style="background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; cursor: pointer; transition: background 0.3s ease;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M23 4v6h-6"></path>
                        <path d="M1 20v-6h6"></path>
                        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                        <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                    </svg>
                </button>
            </div>
            <div class="activity-content" style="height: 138px; overflow-y: auto; padding: 0; scrollbar-width: none; -ms-overflow-style: none;">
                <style>
                    /* 针对WebKit浏览器（Chrome、Safari等）隐藏滚动条 */
                    .activity-content::-webkit-scrollbar {
                        display: none;
                    }
                </style>
                <div id="logs-content" style="color: #fff;">
                    <!-- 内容将通过JavaScript生成 -->
                </div>
            </div>
            <div style="text-align: center; padding: 6px 0; font-size: 12px; color: rgba(255,255,255,0.6); background: rgba(0,0,0,0.1); border-top: 1px solid rgba(255,255,255,0.1);">
                <span>向下滚动查看更多</span>
            </div>
        </div>
    </div>
</div>

<section>
    <div class="card sort-card">
        <!-- 排序控制区域 -->
        <div class="sort-controls">
            <div class="sort-btn-group">
                <button class="sort-btn active" data-sort="sortOrder">默认排序</button>
                <button class="sort-btn" data-sort="oncePrice">单抽价格</button>
                <button class="sort-btn" data-sort="chestNo">肥皂盒编号</button>
                <button class="sort-btn sort-dir-btn active" data-direction="asc">升序</button>
                <button class="sort-btn sort-dir-btn" data-direction="desc">降序</button>
            </div>
            
            <!-- 搜索肥皂盒ID -->
            <div class="chest-search">
                <input type="text" id="chest-id-search" placeholder="输入肥皂盒ID" class="chest-search-input">
                <button id="chest-search-btn" class="chest-search-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                </button>
            </div>
            
            <!-- 隐藏的下拉选择器，用于兼容现有代码 -->
            <select id="sort-field" class="sort-select" style="display: none;">
                <option value="sortOrder" selected>默认</option>
                <option value="oncePrice">单抽价格</option>
                <option value="chestNo">肥皂盒编号</option>
            </select>
            <select id="sort-direction" class="sort-select" style="display: none;">
                <option value="asc" selected>升序</option>
                <option value="desc">降序</option>
            </select>
            
            <!-- 页码控制 -->
            <div class="page-control">
                <span>页码：</span>
                <input type="number" id="page-number" min="1" value="1" class="page-input">
            </div>
        </div>
    </div>
</section>

<section>
    <div class="card">
        <div id="chests-container">
            <div class="loading-indicator">
                <div class="spin"></div>
                <span>正在加载肥皂盒数据...</span>
            </div>
        </div>
    </div>
</section>

<!-- 删除原抽取记录部分 -->

<!-- 肥皂盒列表项模板 -->
<template id="chest-item-template">
    <div class="chest-item" data-chest-id="">
        <div class="chest-header">
            <div class="chest-title">
                <h3 class="chest-name"></h3>
                <span class="chest-id" style="display: none;"></span>
            </div>
            <div class="chest-probability-badge">
                <span class="chest-probability"></span>
            </div>
            <div class="chest-badge" style="display: none;"></div>
        </div>
        <div class="chest-details">
            <div class="chest-detail">
                <span class="detail-label">卖家:</span>
                <span class="chest-seller"></span>
            </div>
            <div class="chest-detail">
                <span class="detail-label">单抽价格:</span>
                <span class="chest-price"></span>
            </div>
            <div class="chest-detail">
                <span class="detail-label"><span class="multi-draw-count">3</span>抽价格:</span>
                <span class="chest-multi-price"></span>
            </div>
            <div class="chest-detail">
                <span class="detail-label">编号:</span>
                <span class="chest-number"></span>
            </div>
            <div class="chest-detail">
                <span class="detail-label">抽取进度:</span>
                <span class="chest-progress">
                    <span class="chest-remain"></span>/<span class="chest-total"></span>
                </span>
            </div>
        </div>
    </div>
</template>

<!-- 肥皂盒详情模态框 -->
<div id="chest-detail-modal" class="modal">
    <div class="modal-content chest-detail-content">
        <div class="modal-header">
            <div class="chest-header-info">
                <h3 id="detail-chest-title">肥皂盒详情</h3>
                <div class="chest-subtitle">
                    <span class="chest-id-label">ID: <span id="detail-chest-id"></span></span>
                    <span class="chest-seller-label">卖家: <span id="detail-seller"></span></span>
                </div>
            </div>
            <div class="modal-actions">
                <button class="calculate-ev-btn" id="calculate-ev-btn" title="计算期望">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="4" y="2" width="16" height="20" rx="2"></rect>
                        <line x1="9" y1="8" x2="15" y2="8"></line>
                        <line x1="12" y1="16" x2="12" y2="16"></line>
                        <line x1="9" y1="12" x2="15" y2="12"></line>
                    </svg>
                </button>
                <button class="detail-refresh-btn" id="refresh-chest-btn" title="刷新">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M23 4v6h-6"></path>
                        <path d="M1 20v-6h6"></path>
                        <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                        <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                    </svg>
                </button>
                <button class="close-btn" title="关闭">&times;</button>
            </div>
        </div>
        <div class="modal-body">
            <!-- 加载状态 -->
            <div id="detail-loading" class="loading-indicator">
                <div class="spin"></div>
                <span>正在加载详情...</span>
            </div>
            
            <!-- 基本信息 -->
            <div id="chest-basic-info" class="detail-section" style="display: none;">
                <!-- 顶部概况 -->
                <div class="chest-summary">
                    <div class="chest-progress-info">
                        <div class="chest-draw-progress">
                            <span id="detail-remain" class="remain-value"></span>/<span id="detail-total"></span>
                        </div>
                    </div>
                    <div class="chest-actions">
                        <button class="action-btn chest-draw-btn once-draw-btn">
                            抽 1 次
                            <span class="price-tag" id="detail-once-price">¥90.00</span>
                        </button>
                        <button class="action-btn chest-draw-btn multi-draw-btn">
                            抽 <span id="detail-multi-draw">3</span> 次
                            <span class="price-tag" id="detail-multi-price">¥270.00</span>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 期望计算结果区域 -->
            <div id="chest-ev-result" class="detail-section ev-result" style="display: none;">
                <div class="ev-result-container">
                    <div class="ev-result-header">
                        <h4>期望计算结果</h4>
                    </div>
                    <div id="ev-result-content" class="ev-result-content"></div>
                </div>
            </div>
            
            <!-- 游戏列表 -->
            <div id="chest-games-list" class="games-section" style="display: none;">
                <!-- 游戏池子将在这里动态生成 -->
            </div>
            
            <!-- 错误信息 -->
            <div id="detail-error" class="error-message" style="display: none;">
                <p>获取详情失败，请稍后再试。</p>
            </div>
        </div>
    </div>
</div>

<!-- 池子模板 -->
<template id="pool-template">
    <div class="game-pool">
        <div class="pool-header">
            <h4 class="pool-name"></h4>
            <span class="pool-probability"></span>
        </div>
        <div class="pool-games"></div>
    </div>
</template>

<!-- 游戏卡片模板 -->
<template id="game-card-template">
    <div class="game-card">
        <div class="game-info">
            <h5 class="game-title"></h5>
            <div class="game-status">
                <span class="game-stock"></span>
            </div>
        </div>
    </div>
</template>

<!-- 分页控制 -->
<div class="pagination-controls">
    <button id="prev-page" class="pagination-btn" disabled>上一页</button>
    <span id="page-indicator">第 1 页</span>
    <button id="next-page" class="pagination-btn">下一页</button>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/chests.js') }}" defer></script>
<script>
    // 排序按钮点击事件
    document.addEventListener('DOMContentLoaded', function() {
        const sortBtns = document.querySelectorAll('.sort-btn:not(.sort-dir-btn)');
        const sortDirBtns = document.querySelectorAll('.sort-dir-btn');
        const sortFieldSelect = document.getElementById('sort-field');
        const sortDirectionSelect = document.getElementById('sort-direction');
        
        // 排序字段点击
        sortBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 跳过排序方向按钮
                if (this.classList.contains('sort-dir-btn')) return;
                
                // 移除所有活动状态
                sortBtns.forEach(b => {
                    if (!b.classList.contains('sort-dir-btn')) {
                        b.classList.remove('active');
                    }
                });
                
                // 添加活动状态
                this.classList.add('active');
                
                // 更新隐藏的选择器
                if (sortFieldSelect) {
                    sortFieldSelect.value = this.dataset.sort;
                    
                    // 触发变更事件
                    const event = new Event('change');
                    sortFieldSelect.dispatchEvent(event);
                }
            });
        });
        
        // 排序方向点击
        sortDirBtns.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有活动状态
                sortDirBtns.forEach(b => b.classList.remove('active'));
                
                // 添加活动状态
                this.classList.add('active');
                
                // 更新隐藏的选择器
                if (sortDirectionSelect) {
                    sortDirectionSelect.value = this.dataset.direction;
                    
                    // 触发变更事件
                    const event = new Event('change');
                    sortDirectionSelect.dispatchEvent(event);
                }
            });
        });
    });
</script>
{% endblock %} 