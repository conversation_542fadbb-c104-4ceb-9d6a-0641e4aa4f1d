from pydantic import BaseModel, Field
from typing import List, Optional
from .account import MobileAccount

class GameFavorite(BaseModel):
    id: str
    name: str
    gameAva: Optional[str] = None

class GameMonitor(BaseModel):
    id: str
    name: str
    gameAva: Optional[str] = None
    min_interval: int = 5000  # 默认最小监控间隔5000ms
    max_interval: int = 15000 # 默认最大监控间隔15000ms
    last_update_time: Optional[str] = None  # 上次检查到的updateTime
    auto_order: bool = False  # 是否自动下单
    target_price: Optional[float] = None  # 目标价格
    price_notification_type: Optional[str] = None  # 价格通知类型：highlight或browser
    current_price: Optional[float] = None  # 当前价格
    game_group: Optional[str] = None  # 游戏分组ID，同一分组的游戏只要有一个下单成功，其他都会停止监控

class Config(BaseModel):
    accounts: List[MobileAccount] = Field(default_factory=list)
    favorite_games: List[GameFavorite] = Field(default_factory=list)
    monitored_games: List[GameMonitor] = Field(default_factory=list)  # CDK监控游戏列表