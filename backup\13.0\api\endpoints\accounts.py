from fastapi import APIRouter, HTTPException, Body, Depends
from typing import List, Dict, Any
from pydantic import BaseModel, Field

from pywatcher.models.cookie_pool import MobileAccount, AccountType
from pywatcher.crud.cookie_pool_manager import CookiePoolManager
from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.api.dependencies import get_steam_client_for_login, get_cookie_pool_manager

# 账号类型的中文名称映射
ACCOUNT_TYPE_NAMES = {
    AccountType.QUERY: "查询账户",
    AccountType.ORDER_PRIMARY: "下单主账号",
    AccountType.ORDER_SECONDARY: "下单副账号",
    AccountType.CDK_QUERY: "CDK专用查询",
    AccountType.BACKUP: "备用账号"
}

router = APIRouter(
    prefix="/accounts",
    tags=["Accounts"]
)

class SmsLoginRequest(BaseModel):
    phone: str
    code: str
    remark: str = "" # Add an optional remark field

class AccountTypeRequest(BaseModel):
    account_type: AccountType

class CaptchaRequest(BaseModel):
    pass

class RegisterSmsRequest(BaseModel):
    phone: str
    captcha_id: str
    captcha_code: str

class RegisterLoginRequest(BaseModel):
    phone: str
    sms_code: str
    remark: str = ""

@router.get("", response_model=List[MobileAccount])
async def get_accounts(
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """获取所有存储在 Mobile Cookie Pool 中的账户列表。"""
    return cookie_manager.get_all_mobile_accounts()

@router.post("/login", response_model=MobileAccount)
async def login_and_add_account(
    login_data: SmsLoginRequest,
    client: SteamPyClient = Depends(get_steam_client_for_login),
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """通过短信验证码登录，如果成功，则将账户添加到 Mobile Cookie Pool 中。"""
    token, response = await client.verify_login_code(login_data.phone, login_data.code)
    
    if not token:
        # 登录失败，提取错误信息
        detail = "登录失败或验证码错误"
        if response and isinstance(response, dict):
            detail = response.get("message", detail)
        raise HTTPException(status_code=401, detail=detail)
    
    # 使用直接返回的token
    access_token = token
    
    if not access_token:
        raise HTTPException(status_code=500, detail="登录成功，但未能获取到Token")

    # 准备账户名称和备注
    account_name = login_data.remark or f"用户-{login_data.phone[-4:]}"
    
    # 创建新账户，确保提供必要的id和name字段
    new_account = MobileAccount(
        id=login_data.phone,  # 使用手机号作为ID
        name=account_name,    # 使用备注或默认名称
        account=login_data.phone,  # 兼容旧字段
        password="",  # 短信登录无密码
        access_token=access_token,
        token_expire_time=response.get("tokenExpireTime") if isinstance(response, dict) else None,
        remark=account_name,
        status=1,  # 假设登录成功状态为活跃
        account_type=AccountType.BACKUP  # 默认为备用账号
    )
    
    try:
        # The manager handles adding and saving to the JSON file.
        added_account = cookie_manager.add_mobile_account(new_account)
        cookie_manager.save_pool()  # 持久化更改
        return added_account
    except ValueError as e:
        # This will catch duplicates
        raise HTTPException(status_code=409, detail=str(e))


@router.delete("/{account_name}", status_code=204)
async def delete_account(
    account_name: str,
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """从 Mobile Cookie Pool 中删除一个账户。"""
    success = cookie_manager.remove_mobile_account(account_name)
    if not success:
        raise HTTPException(status_code=404, detail=f"未在移动账户池中找到账户: {account_name}")
    
    cookie_manager.save_pool()  # 持久化更改
    return None

@router.put("/{account_id}/set-type")
async def set_account_type(
    account_id: str,
    request: AccountTypeRequest,
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """设置指定账户的类型"""
    # 先找到指定账户
    account = cookie_manager.get_mobile_account(account_id)
    if not account:
        raise HTTPException(status_code=404, detail=f"未找到账户: {account_id}")
    
    # 如果不是设置为备用账号或CDK专用查询账号，需要检查是否已经存在同类型账号
    if request.account_type != AccountType.BACKUP and request.account_type != AccountType.CDK_QUERY:
        # 获取所有账号
        all_accounts = cookie_manager.get_all_mobile_accounts()
        # 检查是否存在相同类型的账号(除了当前账号)
        for acc in all_accounts:
            if acc.id != account.id and acc.account_type == request.account_type:
                raise HTTPException(
                    status_code=400, 
                    detail=f"已存在一个{ACCOUNT_TYPE_NAMES.get(request.account_type, request.account_type)}，请先将其修改为其他类型"
                )
    
    # 设置账户类型
    account.account_type = request.account_type
    cookie_manager.save_pool()  # 持久化更改
    
    return {"message": f"已将账户 '{account.name}' 设置为 {request.account_type} 类型", "account_id": account_id, "account_type": request.account_type}

@router.get("/register/captcha")
async def get_register_captcha(
    client: SteamPyClient = Depends(get_steam_client_for_login)
):
    """获取注册用的图形验证码"""
    captcha_id = await client.get_captcha_id()
    if not captcha_id:
        raise HTTPException(status_code=500, detail="获取验证码ID失败")
    
    captcha_image = await client.get_captcha_image(captcha_id)
    if not captcha_image:
        raise HTTPException(status_code=500, detail="获取验证码图片失败")
    
    import base64
    image_base64 = base64.b64encode(captcha_image).decode()
    
    return {
        "captcha_id": captcha_id,
        "captcha_image": f"data:image/png;base64,{image_base64}"
    }

@router.post("/register/sms")
async def send_register_sms(
    request: RegisterSmsRequest,
    client: SteamPyClient = Depends(get_steam_client_for_login)
):
    """发送注册短信验证码"""
    result = await client.send_register_sms(request.phone, request.captcha_id, request.captcha_code)
    
    if result.get("success"):
        return {"message": "注册短信验证码发送成功"}
    else:
        raise HTTPException(status_code=400, detail=result.get("message", "发送注册短信失败"))

@router.post("/register", response_model=MobileAccount)
async def register_and_add_account(
    register_data: RegisterLoginRequest,
    client: SteamPyClient = Depends(get_steam_client_for_login),
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """通过注册短信验证码登录并添加账户"""
    result = await client.register_with_sms(register_data.phone, register_data.sms_code)
    
    if not result.get("success"):
        raise HTTPException(status_code=401, detail=result.get("message", "注册登录失败"))
    
    token = result.get("result")
    if not token:
        raise HTTPException(status_code=500, detail="注册成功但未获取到Token")
    
    account_name = register_data.remark or f"用户-{register_data.phone[-4:]}"
    
    new_account = MobileAccount(
        id=register_data.phone,
        name=account_name,
        account=register_data.phone,
        password="",
        access_token=token,
        token_expire_time=None,
        remark=account_name,
        status=1,
        account_type=AccountType.BACKUP
    )
    
    try:
        added_account = cookie_manager.add_mobile_account(new_account)
        cookie_manager.save_pool()
        return added_account
    except ValueError as e:
        raise HTTPException(status_code=409, detail=str(e))


