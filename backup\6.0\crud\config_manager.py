import json
import logging
from pathlib import Path
from typing import Optional, List
import aiofiles

from pywatcher.models.config import Config, GameFavorite
from pywatcher.models.account import MobileAccount

logger = logging.getLogger(__name__)

# 将 CONFIG_PATH 定义为字符串,以避免在模块加载时进行同步IO
CONFIG_PATH_STR = "pywatcher/config.json"

# This will be populated at application startup
app_config: Optional[Config] = None

async def initialize_config():
    """
    Asynchronously loads the application configuration from config.json
    and populates the global app_config variable.
    """
    global app_config
    config_path = Path(CONFIG_PATH_STR)
    try:
        # Asynchronously try to open and read the file.
        async with aiofiles.open(config_path, "r", encoding="utf-8") as f:
            content = await f.read()
        app_config = Config.model_validate_json(content)
        logger.info("Successfully loaded application config from file.")
    except FileNotFoundError:
        # If the file doesn't exist, create a default one.
        logger.warning(f"{config_path} not found. Creating a default config file.")
        app_config = Config()
        await save_config_async(app_config)
    except Exception as e:
        # Handle other errors like JSON validation issues.
        logger.error(f"Failed to load or validate config from {config_path}: {e}", exc_info=True)
        # Fallback to a default config if loading or validation fails.
        app_config = Config()

async def save_config_async(config: Config):
    """Asynchronously saves the provided configuration to config.json."""
    config_path = Path(CONFIG_PATH_STR)
    try:
        # Ensure directory exists, asynchronously
        await aiofiles.os.makedirs(config_path.parent, exist_ok=True)
        
        async with aiofiles.open(config_path, "w", encoding="utf-8") as f:
            await f.write(config.model_dump_json(indent=2))
    except Exception as e:
        logger.error(f"Failed to save config to {config_path}: {e}", exc_info=True)


# --- Favorite Games Management ---

def get_favorite_games() -> List[GameFavorite]:
    """Returns the list of favorite games from the config."""
    if not app_config:
        return []
    return app_config.favorite_games


async def add_favorite_game(game: GameFavorite):
    """Adds a game to the favorites list and saves the config."""
    if not app_config:
        logger.warning("Config not initialized, cannot add favorite game.")
        return
    # Avoid duplicates
    if any(g.id == game.id for g in app_config.favorite_games):
        logger.warning(f"Game {game.name} ({game.id}) is already in favorites.")
        return
    app_config.favorite_games.append(game)
    await save_config_async(app_config)


async def remove_favorite_game(game_id: str) -> bool:
    """Removes a game from the favorites list by its ID and saves the config."""
    if not app_config:
        logger.warning("Config not initialized, cannot remove favorite game.")
        return False
    
    initial_len = len(app_config.favorite_games)
    app_config.favorite_games = [g for g in app_config.favorite_games if g.id != game_id]
    
    if len(app_config.favorite_games) < initial_len:
        await save_config_async(app_config)
        return True
    return False


async def update_favorite_games_order(game_ids: List[str]):
    """Updates the order of favorite games based on a list of IDs."""
    if not app_config:
        logger.warning("Config not initialized, cannot reorder favorite games.")
        return
        
    id_to_game_map = {game.id: game for game in app_config.favorite_games}
    
    new_ordered_list = []
    for game_id in game_ids:
        if game_id in id_to_game_map:
            new_ordered_list.append(id_to_game_map[game_id])
    
    # Verify we didn't lose any games, and all old games are in the new list
    if len(new_ordered_list) == len(id_to_game_map):
        app_config.favorite_games = new_ordered_list
        await save_config_async(app_config)
        logger.info("Successfully updated favorite games order.")
    else:
        logger.error("Failed to reorder favorite games due to mismatch in game list.")