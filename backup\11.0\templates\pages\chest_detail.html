{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}肥皂盒详情 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <div class="header-content">
        <h1>
            {{ icon('box') }}
            <span id="detail-chest-title">肥皂盒详情</span>
        </h1>
        <p class="page-description">
            查看肥皂盒详细信息和游戏列表。
            <a href="/chests" id="back-to-list-btn" class="btn btn-sm btn-outline" style="margin-left: 10px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="19" y1="12" x2="5" y2="12"></line>
                    <polyline points="12 19 5 12 12 5"></polyline>
                </svg>
                返回列表
            </a>
        </p>
    </div>
</header>

<div class="detail-page-container">
    <!-- 肥皂盒抽取记录部分 -->
    <section class="detail-sidebar-section">
        <div>
            <div id="chest-logs-container">
                <div class="loading-indicator" style="display: none;">
                    <div class="spin"></div>
                    <span>正在加载记录...</span>
                </div>
                <div class="activity-feed" style="border-radius: 12px; background: linear-gradient(135deg, #1a73e8 0%, #3490dc 100%); box-shadow: 0 8px 16px rgba(33,150,243,0.15); margin: 10px 0; padding: 0; overflow: hidden; position: relative; z-index: 10;">
                    <div class="activity-header" style="display: flex; justify-content: space-between; align-items: center; padding: 16px 24px; border-bottom: 1px solid rgba(255,255,255,0.1);">
                        <div style="display: flex; align-items: center; color: white;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin-right: 10px;">
                                <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"></path>
                            </svg>
                            <span style="font-weight: 600; font-size: 18px;">最近活动</span>
                        </div>
                        <button id="refresh-logs-btn" style="background: rgba(255,255,255,0.2); border: none; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; justify-content: center; align-items: center; cursor: pointer; transition: background 0.3s ease;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M23 4v6h-6"></path>
                                <path d="M1 20v-6h6"></path>
                                <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                                <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="activity-content" style="height: 138px; overflow-y: auto; padding: 0; scrollbar-width: none; -ms-overflow-style: none;">
                        <style>
                            /* 针对WebKit浏览器（Chrome、Safari等）隐藏滚动条 */
                            .activity-content::-webkit-scrollbar {
                                display: none;
                            }
                        </style>
                        <div id="logs-content" style="color: #fff;">
                            <!-- 内容将通过JavaScript生成 -->
                            <div style="padding: 12px; color: rgba(255,255,255,0.7); text-align: center;">正在加载记录...</div>
                        </div>
                    </div>
                    <div style="text-align: center; padding: 6px 0; font-size: 12px; color: rgba(255,255,255,0.6); background: rgba(0,0,0,0.1); border-top: 1px solid rgba(255,255,255,0.1);">
                        <span>向下滚动查看更多</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="detail-main-section">
        <div class="card">
            <!-- 加载状态 -->
            <div id="detail-loading" class="loading-indicator">
                <div class="spin"></div>
                <span>正在加载肥皂盒详情...</span>
            </div>
            
            <!-- 错误信息 -->
            <div id="detail-error" class="error-message" style="display: none;">
                <p>加载肥皂盒详情失败，请稍后再试。</p>
            </div>
            
            <!-- 基本信息 -->
            <div id="chest-basic-info" class="detail-section" style="display: none;">
                <!-- 顶部概况 -->
                <div class="chest-summary">
                    <div class="chest-progress-info">
                        <div class="chest-draw-progress">
                            <span id="detail-remain" class="remain-value"></span>/<span id="detail-total"></span>
                        </div>
                    </div>
                    <div class="chest-actions">
                        <!-- 添加收藏按钮 -->
                        <button class="action-btn icon-action-btn" id="favorite-chest-btn" title="收藏" style="background: rgba(255,255,255,0.2); color: #ff9800; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; padding: 0; flex-shrink: 0; flex-grow: 0; border: none; box-shadow: none; margin-right: 10px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                            </svg>
                        </button>
                        <button class="action-btn chest-draw-btn once-draw-btn">
                            抽 1 次 <span class="price-tag" id="detail-once-price">¥90.00</span>
                        </button>
                        <button class="action-btn chest-draw-btn multi-draw-btn">
                            抽 <span id="detail-multi-draw">3</span> 次 <span class="price-tag" id="detail-multi-price">¥270.00</span>
                        </button>
                        <button class="action-btn icon-action-btn" id="refresh-chest-btn" title="刷新" style="background: rgba(255,255,255,0.2); color: #3490dc; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; padding: 0; flex-shrink: 0; flex-grow: 0; border: none; box-shadow: none;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M23 4v6h-6"></path>
                                <path d="M1 20v-6h6"></path>
                                <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10"></path>
                                <path d="M20.49 15a9 9 0 0 1-14.85 3.36L1 14"></path>
                            </svg>
                        </button>
                        <button class="action-btn icon-action-btn" id="calculate-ev-btn" title="计算期望" style="background: rgba(52, 144, 220, 0.9); color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; padding: 0; flex-shrink: 0; flex-grow: 0; border: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polygon points="12 2 2 7 12 12 22 7 12 2"></polygon>
                                <polyline points="2 17 12 22 22 17"></polyline>
                                <polyline points="2 12 12 17 22 12"></polyline>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 期望结果 -->
            <div id="chest-ev-result" class="detail-section" style="display: none;">
                <div class="ev-result-container">
                    <div class="ev-result-header">
                        <h4>期望计算结果</h4>
                    </div>
                    <div id="ev-result-content" class="ev-result-content"></div>
                </div>
            </div>
            
            <!-- 游戏列表 -->
            <div id="chest-games-list" class="games-section" style="display: none;">
                <!-- 游戏列表将通过JavaScript动态加载 -->
            </div>
        </div>
    </section>
</div>

<!-- 游戏池模板 -->
<template id="pool-template">
    <div class="game-pool">
        <div class="pool-header">
            <h3 class="pool-name">未知池子</h3>
            <span class="pool-probability">未知概率</span>
        </div>
        <div class="pool-games">
            <!-- 游戏卡片将被动态添加到这里 -->
        </div>
    </div>
</template>

<!-- 游戏卡片模板 -->
<template id="game-card-template">
    <div class="game-card">
        <div class="game-title">游戏名称</div>
    </div>
</template>
{% endblock %}

{% block styles %}
<style>
/* 页面布局 */
.detail-page-container {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 20px;
    margin-bottom: 30px;
}

@media (max-width: 1024px) {
    .detail-page-container {
        grid-template-columns: 1fr;
    }
}

/* 肥皂盒基本信息样式 */
.chest-summary {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 24px;
}

.chest-progress-info {
    display: flex;
    align-items: center;
}

.chest-draw-progress {
    font-size: 1.5rem;
    font-weight: 600;
}

.remain-value {
    color: #3490dc;
}

.chest-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.chest-actions .action-btn {
    margin-left: 0 !important;
}

.action-btn {
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chest-draw-btn {
    background-color: #3490dc;
    color: white;
}

.chest-draw-btn:hover {
    background-color: #2779bd;
}

.price-tag {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-top: 5px;
}

/* 游戏池样式 */
.games-section {
    margin-top: 20px;
}

.game-pool {
    margin-bottom: 30px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.05);
}

.pool-name {
    margin: 0;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.pool-level {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.2);
}

.pool-probability {
    font-size: 0.9rem;
    font-weight: 600;
    color: #e6a23c;
}

.pool-games {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
    padding: 16px;
}

/* 游戏卡片样式 */
.game-card {
    background-color: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.game-card:hover {
    background-color: rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.game-title {
    font-weight: 600;
    margin: 0 0 5px 0;
    font-size: 0.95rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.game-status {
    font-size: 0.85rem;
    color: rgba(0, 0, 0, 0.6);
}

.game-info-row {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: rgba(0, 0, 0, 0.7);
}

.cached-price {
    color: #e6a23c !important;
}

/* 高价值游戏突出显示 */
.high-value-game {
    background-color: rgba(103, 194, 58, 0.15);
    box-shadow: 0 0 5px rgba(103, 194, 58, 0.5);
}

.high-value-game:hover {
    background-color: rgba(103, 194, 58, 0.25);
}

/* 池子样式 */
.legendary-pool .pool-header {
    background-color: rgba(236, 72, 153, 0.3);
}

.epic-pool .pool-header {
    background-color: rgba(124, 58, 237, 0.3);
}

.rare-pool .pool-header {
    background-color: rgba(59, 130, 246, 0.3);
}

.common-pool .pool-header {
    background-color: rgba(75, 85, 99, 0.3);
}

/* 等级标签 */
.lv1 {
    background-color: #ec4899 !important;
    color: white;
}

.lv2 {
    background-color: #7c3aed !important;
    color: white;
}

.lv3 {
    background-color: #3b82f6 !important;
    color: white;
}

.lv4 {
    background-color: #4b5563 !important;
    color: white;
}

/* 期望值结果 */
#chest-ev-result {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    margin-bottom: 20px;
}

.ev-result-container {
    padding: 16px;
}

.ev-result-header {
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding-bottom: 10px;
}

.ev-result-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #333;
}

.ev-multiplier {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
}

.ev-multiplier span {
    font-size: 1rem;
    opacity: 0.8;
}

.ev-detail {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.ev-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px dashed rgba(0,0,0,0.1);
}

.ev-good {
    color: #67c23a !important;
}

.ev-bad {
    color: #f56c6c !important;
}

.ev-neutral {
    color: #e6a23c !important;
}

/* 抽取记录表格样式 */
.logs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 0.9rem;
}

.logs-table th {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 10px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.logs-table td {
    padding: 8px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.logs-table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* 稀有度行样式 */
.logs-table .legendary-item {
    background-color: rgba(236, 72, 153, 0.1);
}

.logs-table .epic-item {
    background-color: rgba(124, 58, 237, 0.1);
}

.logs-table .rare-item {
    background-color: rgba(59, 130, 246, 0.1);
}

/* 旋转动画 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.rotating {
    animation: rotate 1s linear infinite;
}

/* Activity records styles */
.activity-list {
    margin-top: 10px;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    transition: background-color 0.2s ease;
}

.activity-item:hover {
    background-color: rgba(0,0,0,0.05);
}

.game-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #3490dc;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    font-weight: bold;
}

.activity-content {
    flex: 1;
}

.activity-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
    margin-top: 4px;
}

/* Toast通知样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    background-color: #67c23a;
    color: white;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast.error {
    background-color: #f56c6c;
}

.toast.info {
    background-color: #909399;
}

/* 空状态样式 */
.empty-message {
    padding: 30px;
    text-align: center;
    color: #909399;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .info-group {
        grid-template-columns: 1fr;
    }
    
    .pool-games {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
    
    .actions-group {
        flex-direction: column;
    }
    
    .actions-group button {
        width: 100%;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/chest_detail.js') }}" defer></script>
{% endblock %} 