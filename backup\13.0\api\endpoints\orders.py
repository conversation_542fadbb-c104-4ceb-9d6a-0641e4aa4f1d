from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel
from typing import List
import logging
import aiohttp

from pywatcher.models.order import OrderRecord
from pywatcher.crud.order_manager import get_order_manager, OrderManager
from pywatcher.api.dependencies import get_client_session

router = APIRouter(
    prefix="/orders",
    tags=["Orders"]
)

logger = logging.getLogger(__name__)

@router.get("", response_model=List[OrderRecord])
async def list_orders(
    order_manager: OrderManager = Depends(get_order_manager)
):
    """获取所有订单记录"""
    return order_manager.get_orders()

@router.get("/{order_id}", response_model=OrderRecord)
async def get_order(
    order_id: str,
    order_manager: OrderManager = Depends(get_order_manager)
):
    """获取指定订单详情"""
    order = order_manager.get_order_by_id(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    return order

@router.delete("/{order_id}")
async def delete_order(
    order_id: str,
    order_manager: OrderManager = Depends(get_order_manager)
):
    """删除指定订单"""
    order = order_manager.get_order_by_id(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 删除订单
    await order_manager.delete_order(order_id)
    
    return {"success": True, "message": "订单已删除"}

@router.get("/{order_id}/redeem")
async def redeem_order(
    order_id: str,
    order_manager: OrderManager = Depends(get_order_manager),
    session: aiohttp.ClientSession = Depends(get_client_session)
):
    """兑换订单中的游戏激活码"""
    # 获取订单信息
    order = order_manager.get_order_by_id(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 检查订单状态
    if order.status != "已支付":
        raise HTTPException(status_code=400, detail="只有已支付的订单才能兑换激活码")
    
    try:
        # 调用激活码API
        url = "https://steampy.com/xboot/accountBook/userList"
        params = {
            "type": "cdk",
            "area": "CNY",
            "pageNumber": 1,
            "pageSize": 5,
            "sort": "loginTime",
            "order": "desc"
        }
        
        headers = {"accesstoken": order.access_token}
        async with session.get(url, params=params, headers=headers) as response:
            data = await response.json()
            
            if data.get("code") != 200:
                error_msg = data.get("message", "获取激活码失败") 
                logger.error(f"兑换订单 {order_id} 激活码失败: {error_msg}")
                raise HTTPException(status_code=data.get("code", 500), detail=error_msg)
                
            # 如果成功获取激活码，从响应中获取CDK信息
            content = data.get("result", {}).get("content", [])
            if not content:
                raise HTTPException(status_code=404, detail="未找到对应的激活码记录")
                
            # 查找匹配的销售记录
            cdk_records = []
            for record in content:
                record_data = {
                    "id": record.get("id"),
                    "game_name": record.get("gameName", "未知游戏"),
                    "cdk": record.get("cdk", "N/A"),
                    "create_time": record.get("createTime"),
                    "is_used": record.get("isUsed", False)
                }
                cdk_records.append(record_data)
            
            return {
                "success": True,
                "message": "获取激活码成功",
                "records": cdk_records
            }
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"兑换订单 {order_id} 激活码时出错: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"兑换激活码时出错: {str(e)}") 