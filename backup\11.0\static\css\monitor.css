/* 监控页面特定样式 */

/* 监控控制面板 */
.monitor-control-panel {
    background-color: rgba(var(--primary-rgb), 0.03);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* 按钮样式 */
#toggleMonitor {
    transition: all 0.3s ease;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    min-height: 48px;
}

#toggleMonitor.monitoring {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

#toggleMonitor.monitoring:hover {
    background-color: rgba(var(--danger-rgb), 0.9);
}

/* SVG图标样式 */
.icon-play, .icon-stop, .icon-trash {
    display: inline-block;
    vertical-align: middle;
    margin-right: 6px;
}

.mr-1 {
    margin-right: 4px;
}

.mr-2 {
    margin-right: 8px;
}

.feather {
    display: inline-block;
    vertical-align: middle;
}

.feather.mr-1 {
    margin-right: 4px;
}

.feather.mr-2 {
    margin-right: 8px;
}

#toggleMonitor .feather {
    stroke-width: 2.5px;
}

#toggleMonitor.monitoring .feather {
    stroke: white;
}

#clearLogs .feather {
    stroke-width: 2px;
}

/* 监控间隔输入框 */
.interval-controls {
    display: flex;
    align-items: center;
}

.interval-controls label {
    min-width: 85px;
    color: var(--text-color);
}

.interval-controls .input-group {
    flex: 1;
}

.interval-controls .input-group-text {
    background-color: var(--primary-color);
    color: #fff;
    border: none;
}

/* 开关样式 */
.switch-control {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin: 0;
}

.switch-control input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--success-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--success-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* 监控模式面板 */
.monitor-modes-panel {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
    margin-top: 25px;
}

.section-title {
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.section-title:before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: var(--primary-color);
    margin-right: 10px;
    border-radius: 2px;
}

.monitor-mode-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
}

.mode-option {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 12px 15px;
    background-color: rgba(var(--primary-rgb), 0.03);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.mode-option:hover {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.06);
}

.mode-option input {
    position: absolute;
    opacity: 0;
}

.mode-check {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(var(--primary-rgb), 0.5);
    border-radius: 4px;
    margin-right: 10px;
}

.mode-option input:checked + .mode-check:before {
    content: "\2713";
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.mode-label {
    font-weight: 600;
    color: var(--text-color);
    margin-right: 10px;
}

.mode-description {
    flex-basis: 100%;
    margin-top: 8px;
    margin-left: 30px;
    font-size: 0.85rem;
    color: var(--text-muted-color);
}

/* 日志面板 */
.monitor-logs-panel {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
    margin-top: 25px;
}

.logs-header {
    margin-bottom: 15px;
}

.monitor-logs-container {
    height: 300px;
    overflow-y: auto;
    padding: 15px;
    background-color: var(--log-bg-color);
    border-radius: 8px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

.monitor-logs-container::-webkit-scrollbar {
    width: 8px;
}

.monitor-logs-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.monitor-logs-container::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.monitor-logs-container div {
    padding: 6px 0;
    border-bottom: 1px dashed rgba(var(--text-muted-rgb), 0.1);
}

.monitor-logs-container div:last-child {
    border-bottom: none;
}

/* 日志样式 */
.log-time {
    color: var(--text-muted-color);
    margin-right: 8px;
    font-weight: 500;
}

.log-normal {
    color: var(--text-color);
}

.log-error {
    color: var(--danger-color) !important;
}

.log-success {
    color: var(--success-color) !important;
}

.log-warning {
    color: var(--warning-color) !important;
}

/* 响应式调整 */
@media (max-width: 767px) {
    .monitor-control-panel {
        padding: 15px;
    }
    
    .monitor-mode-options {
        grid-template-columns: 1fr;
    }
    
    .mode-option {
        padding: 10px;
    }
    
    .monitor-logs-container {
        height: 250px;
    }
}

/* 命中记录面板 */
.monitor-hits-panel {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
    margin-top: 25px;
}

.hits-header {
    margin-bottom: 15px;
}

.monitor-hits-container {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.monitor-hits-container table {
    margin-bottom: 0;
}

.monitor-hits-container th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    border-top: none;
}

.monitor-hits-container td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.chest-id-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.chest-id-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.monitor-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

.monitor-type-expected {
    background-color: rgba(0, 123, 255, 0.1);
    color: #0062cc;
}

.monitor-type-probability {
    background-color: rgba(40, 167, 69, 0.1);
    color: #1e7e34;
}

.monitor-type-wrongPrice {
    background-color: rgba(220, 53, 69, 0.1);
    color: #bd2130;
}

.monitor-type-specific {
    background-color: rgba(108, 117, 125, 0.1);
    color: #5a6268;
}

/* 特定监控面板 */
.monitor-specific-panel {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
    margin-top: 25px;
}

.specific-header {
    margin-bottom: 15px;
}

.specific-chests-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.favorite-chests-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.favorite-chest-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    position: relative;
}

.favorite-chest-item:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.favorite-chest-name {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 8px;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.favorite-chest-id {
    font-size: 0.8rem;
    color: var(--text-muted-color);
    margin-bottom: 10px;
}

.favorite-chest-price {
    font-weight: 500;
    color: var(--primary-color);
}

.favorite-chest-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    gap: 5px;
}

.favorite-action-btn {
    background: none;
    border: none;
    color: var(--text-muted-color);
    padding: 2px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.favorite-action-btn:hover {
    color: var(--primary-color);
}

.favorite-action-btn.remove:hover {
    color: var(--danger-color);
}

/* 收藏管理模态框 */
.favorites-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.favorites-modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.favorites-modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.favorites-modal-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.favorites-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted-color);
}

.favorites-modal-body {
    padding: 20px;
}

.favorites-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

@media (max-width: 767px) {
    .favorite-chests-list {
        grid-template-columns: 1fr;
    }
} 