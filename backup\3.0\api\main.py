import aiohttp
import logging
import os
from fastapi import FastAPI, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from contextlib import asynccontextmanager
from pathlib import Path
import aiofiles

# 配置日志 - 改为更详细的调试级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入模块
from pywatcher.database.session import db_init
from pywatcher.api import dependencies
from pywatcher.api.endpoints import auth, accounts, games
from pywatcher.crud.config_manager import initialize_config

async def load_env_async(path: str = ".env"):
    """Asynchronously reads a .env file and loads variables into os.environ."""
    try:
        async with aiofiles.open(path, "r", encoding="utf-8") as f:
            async for line in f:
                line = line.strip()
                if not line or line.startswith("#") or "=" not in line:
                    continue
                key, value = line.split("=", 1)
                key = key.strip()
                # 移除可能存在的引号
                value = value.strip().strip("\"'")
                if key not in os.environ:
                    os.environ[key] = value
        logger.info(".env file loaded asynchronously.")
    except FileNotFoundError:
        logger.warning(f".env file not found at '{path}', skipping.")
    except Exception as e:
        logger.error(f"Error loading .env file: {e}")

# --- 应用生命周期管理 ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时
    # 将所有可能涉及IO的初始化操作都放入lifespan
    
    # 1. 挂载静态文件和模板
    # 这会执行同步的 os.path.isdir 检查, 放在lifespan中更安全
    base_dir = Path(__file__).resolve().parent.parent
    app.state.templates = Jinja2Templates(directory=base_dir / "templates")
    app.mount("/static", StaticFiles(directory=base_dir / "static"), name="static")
    
    # 2. 异步加载环境变量
    await load_env_async(base_dir.parent / ".env")
    
    logger.info("应用启动中...")
    # 3. 初始化数据库
    await db_init()
    logger.info("数据库初始化完成。")
    # 4. 异步加载应用配置
    await initialize_config()
    logger.info("应用配置加载完成。")
    # 5. 创建全局 aiohttp.ClientSession
    dependencies.client_session = aiohttp.ClientSession()
    logger.info("全局 ClientSession 已创建。")
    
    yield
    
    # 应用关闭时
    logger.info("应用关闭中...")
    # 1. 关闭全局 aiohttp.ClientSession
    if dependencies.client_session and not dependencies.client_session.closed:
        await dependencies.client_session.close()
        logger.info("全局 ClientSession 已关闭。")

# --- FastAPI 应用实例 ---
app = FastAPI(
    title="PyWatcher Web App",
    description="一个用于监控和自动购买 Steam 市场物品的应用。",
    lifespan=lifespan,
    debug=True  # 启用调试模式
)

# --- API 路由器 ---
app.include_router(auth.router, prefix="/api")
app.include_router(accounts.router, prefix="/api")
app.include_router(games.router, prefix="/api")

# --- 页面路由 ---
@app.get("/", include_in_schema=False)
async def page_dashboard(request: Request):
    logger.debug("访问仪表盘页面")
    return request.app.state.templates.TemplateResponse("pages/dashboard.html", {"request": request})

@app.get("/accounts", include_in_schema=False)
async def page_accounts(request: Request):
    logger.debug("访问账户管理页面")
    return request.app.state.templates.TemplateResponse("pages/accounts.html", {"request": request})

@app.get("/games", include_in_schema=False)
async def page_games(request: Request):
    logger.debug("访问游戏列表页面")
    return request.app.state.templates.TemplateResponse("pages/games.html", {"request": request}) 