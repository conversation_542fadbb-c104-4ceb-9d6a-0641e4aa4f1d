/**
 * CDK游戏监控系统 - 优化版本
 * 重构为模块化、可维护的代码结构
 */

class CDKMonitorSystem {
    constructor() {
        this.isCdkMonitoring = false;
        this.cdkMonitorTimers = new Map();
        this.monitoredGames = [];
        this.currentGameId = null;
        
        // DOM元素缓存
        this.elements = {};
        
        // 配置项
        this.config = {
            defaultMinInterval: 5000,
            defaultMaxInterval: 15000,
            maxLogEntries: 50,
            maxPriceChangeEntries: 50
        };
        
        this.init();
    }
    
    /**
     * 初始化系统
     */
    init() {
        this.cacheElements();
        this.initializeSettings();
        this.bindEvents();
        this.loadInitialData();
    }
    
    /**
     * 缓存DOM元素
     */
    cacheElements() {
        const elementIds = [
            'toggle-cdk-monitor', 'add-monitor-game', 'export-cdk-games',
            'cdk-min-interval', 'cdk-max-interval', 'cdk-notify-changes',
            'cdk-auto-order', 'cdk-monitored-games-container', 'cdk-monitor-logs',
            'clear-cdk-logs', 'cdk-price-changes', 'clear-cdk-changes',
            'save-cdk-settings', 'export-modal', 'close-export-modal-x',
            'close-export-modal', 'export-content', 'copy-export-btn',
            'add-game-modal', 'close-add-game-modal-x', 'close-add-game-modal',
            'game-search-input', 'search-game-btn', 'game-search-results',
            'set-target-price-modal', 'close-target-price-modal-x',
            'close-target-price-modal', 'save-target-price-btn',
            'clear-target-price-btn', 'target-game-img', 'target-game-name',
            'target-price-input', 'price-notification-type'
        ];
        
        elementIds.forEach(id => {
            this.elements[id] = document.getElementById(id);
        });
    }
    
    /**
     * 初始化设置
     */
    initializeSettings() {
        // 初始化自动下单设置
        if (localStorage.getItem('cdkAutoOrder') === null) {
            localStorage.setItem('cdkAutoOrder', 'true');
        }
        
        // 同步自动下单复选框状态
        if (this.elements['cdk-auto-order']) {
            this.elements['cdk-auto-order'].checked = localStorage.getItem('cdkAutoOrder') !== 'false';
        }
        
        this.loadCdkSettings();
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 监控控制按钮
        this.bindEvent('toggle-cdk-monitor', 'click', this.toggleMonitoring.bind(this));
        
        // 游戏管理按钮
        this.bindEvent('add-monitor-game', 'click', this.openAddGameModal.bind(this));
        this.bindEvent('export-cdk-games', 'click', this.openExportModal.bind(this));
        
        // 设置按钮
        this.bindEvent('save-cdk-settings', 'click', this.saveCdkSettings.bind(this));
        this.bindEvent('clear-cdk-logs', 'click', this.clearLogs.bind(this));
        this.bindEvent('clear-cdk-changes', 'click', this.clearPriceChanges.bind(this));
        
        // 自动下单复选框
        this.bindEvent('cdk-auto-order', 'change', this.handleAutoOrderChange.bind(this));
        
        // 模态框事件
        this.bindModalEvents();
        
        // 搜索事件
        this.bindEvent('search-game-btn', 'click', this.searchGames.bind(this));
        this.bindEvent('game-search-input', 'keypress', this.handleSearchKeyPress.bind(this));
        
        // 目标价格模态框事件
        this.bindEvent('save-target-price-btn', 'click', this.saveTargetPrice.bind(this));
        this.bindEvent('clear-target-price-btn', 'click', this.clearTargetPrice.bind(this));
        
        // 全局事件
        this.bindGlobalEvents();
    }
    
    /**
     * 绑定单个事件
     */
    bindEvent(elementId, eventType, handler) {
        const element = this.elements[elementId];
        if (element) {
            element.addEventListener(eventType, handler);
        }
    }
    
    /**
     * 绑定模态框事件
     */
    bindModalEvents() {
        // 导出模态框
        this.bindEvent('close-export-modal-x', 'click', this.closeExportModal.bind(this));
        this.bindEvent('close-export-modal', 'click', this.closeExportModal.bind(this));
        this.bindEvent('copy-export-btn', 'click', this.copyExportContent.bind(this));
        
        // 添加游戏模态框
        this.bindEvent('close-add-game-modal-x', 'click', this.closeAddGameModal.bind(this));
        this.bindEvent('close-add-game-modal', 'click', this.closeAddGameModal.bind(this));
        
        // 目标价格模态框
        this.bindEvent('close-target-price-modal-x', 'click', this.closeTargetPriceModal.bind(this));
        this.bindEvent('close-target-price-modal', 'click', this.closeTargetPriceModal.bind(this));
    }
    
    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 点击模态框外部关闭
        window.addEventListener('click', (event) => {
            if (event.target === this.elements['export-modal']) {
                this.closeExportModal();
            }
            if (event.target === this.elements['add-game-modal']) {
                this.closeAddGameModal();
            }
            if (event.target === this.elements['set-target-price-modal']) {
                this.closeTargetPriceModal();
            }
        });
        
        // 右键菜单
        document.addEventListener('contextmenu', this.handleContextMenu.bind(this));
    }
    
    /**
     * 加载初始数据
     */
    async loadInitialData() {
        try {
            await this.loadMonitoredGames();
            this.loadCdkPriceChanges();
        } catch (error) {
            this.addCdkLog('初始化数据加载失败: ' + error.message, true);
        }
    }
    
    /**
     * 切换监控状态
     */
    toggleMonitoring() {
        if (this.isCdkMonitoring) {
            this.stopCdkMonitoring();
        } else {
            this.startCdkMonitoring();
        }
    }
    
    /**
     * 开始CDK监控
     */
    startCdkMonitoring() {
        if (this.isCdkMonitoring) return;
        
        if (this.monitoredGames.length === 0) {
            this.addCdkLog('没有可监控的游戏，请先添加游戏', true);
            return;
        }
        
        this.isCdkMonitoring = true;
        this.updateMonitoringButton(true);
        this.addCdkLog('开始CDK监控...', false, true);
        
        // 开始监控所有游戏
        this.monitoredGames.forEach(game => {
            this.startGameMonitoring(game);
        });
    }
    
    /**
     * 停止CDK监控
     */
    stopCdkMonitoring() {
        this.isCdkMonitoring = false;
        this.updateMonitoringButton(false);
        
        // 清除所有定时器
        this.cdkMonitorTimers.forEach(timerId => {
            clearTimeout(timerId);
        });
        this.cdkMonitorTimers.clear();
        
        this.addCdkLog('CDK监控已停止', false);
    }
    
    /**
     * 更新监控按钮状态
     */
    updateMonitoringButton(isMonitoring) {
        const button = this.elements['toggle-cdk-monitor'];
        if (!button) return;
        
        if (isMonitoring) {
            button.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-stop-circle mr-1"><circle cx="12" cy="12" r="10"></circle><rect x="9" y="9" width="6" height="6"></rect></svg>停止监控`;
            button.classList.replace('btn-outline-success', 'btn-outline-danger');
        } else {
            button.innerHTML = '<i class="icon-play mr-1"></i>开始监控';
            button.classList.replace('btn-outline-danger', 'btn-outline-success');
        }
    }
    
    /**
     * 开始监控单个游戏
     */
    startGameMonitoring(game) {
        // 清除现有定时器
        if (this.cdkMonitorTimers.has(game.id)) {
            clearTimeout(this.cdkMonitorTimers.get(game.id));
        }
        
        const minInterval = game.min_interval || this.getStoredSetting('cdkMinInterval', this.config.defaultMinInterval);
        const maxInterval = game.max_interval || this.getStoredSetting('cdkMaxInterval', this.config.defaultMaxInterval);
        
        const monitorTask = async () => {
            if (!this.isCdkMonitoring) return;
            
            try {
                await this.monitorGameInfo(game);
            } catch (error) {
                this.addCdkLog(`监控游戏 ${game.name} 时出错: ${error.message}`, true);
            }
            
            // 设置下次监控
            if (this.isCdkMonitoring) {
                const nextInterval = this.getRandomInterval(minInterval, maxInterval);
                const timerId = setTimeout(monitorTask, nextInterval);
                this.cdkMonitorTimers.set(game.id, timerId);
            }
        };
        
        monitorTask();
    }
    
    /**
     * 监控单个游戏信息
     */
    async monitorGameInfo(game) {
        try {
            const [gameInfo, sellersData] = await Promise.all([
                this.fetchGameInfo(game.id),
                !game.last_update_time ? this.fetchGameSellers(game.id) : null
            ]);
            
            const currentUpdateTime = gameInfo.updateTime;
            
            if (game.last_update_time && currentUpdateTime === game.last_update_time) {
                return; // 没有更新
            }
            
            // 处理价格更新
            await this.handlePriceUpdate(game, currentUpdateTime, sellersData);
            
        } catch (error) {
            this.addCdkLog(`监控游戏 ${game.name} 出错: ${error.message}`, true);
            throw error;
        }
    }
    
    /**
     * 处理价格更新
     */
    async handlePriceUpdate(game, currentUpdateTime, sellersData) {
        let sellersResponse = sellersData;
        
        if (!sellersResponse) {
            this.addCdkLog(`检测到游戏 ${game.name} 信息更新，正在获取最新价格...`, false, true);
            sellersResponse = await this.fetchGameSellers(game.id);
        }
        
        const lowestPrice = this.getLowestPrice(sellersResponse);
        
        if (lowestPrice > 0) {
            this.addCdkLog(`游戏 ${game.name} 价格${game.last_update_time ? '已更新' : '初始化'}: ¥${lowestPrice}`, false, true);
            
            // 更新游戏信息
            await this.updateGameInfo(game.id, currentUpdateTime, lowestPrice);
            
            // 更新本地缓存
            this.updateLocalGameCache(game.id, currentUpdateTime, lowestPrice);
            
            // 检查目标价格
            await this.checkTargetPrice(game, lowestPrice, sellersResponse);
        }
    }
    
    /**
     * 检查目标价格
     */
    async checkTargetPrice(game, currentPrice, sellersData) {
        const updatedGame = this.monitoredGames.find(g => g.id === game.id);
        
        if (updatedGame?.target_price && currentPrice <= updatedGame.target_price) {
            this.addCdkLog(`游戏 ${game.name} 达到目标价格！当前价格: ${currentPrice}, 目标价格: ${updatedGame.target_price}`, false, true);
            
            // 发送通知
            if (updatedGame.price_notification_type === 'browser') {
                this.sendPriceNotification(updatedGame, currentPrice);
            }
            
            // 自动下单
            if (this.shouldAutoOrder()) {
                await this.handleAutoOrder(game, sellersData);
            }
            
            // 重新渲染游戏列表
            this.renderMonitoredGames();
        }
    }
    
    /**
     * 处理自动下单
     */
    async handleAutoOrder(game, sellersData) {
        if (!sellersData?.length || !sellersData[0].saleId) {
            this.addCdkLog(`游戏 ${game.name} 自动下单失败：无有效的卖家ID`, true);
            return;
        }
        
        const saleId = sellersData[0].saleId;
        const orderedSaleIds = this.getOrderedSaleIds(game.id);
        
        if (orderedSaleIds.includes(saleId)) {
            this.addCdkLog(`游戏 ${game.name} 已经尝试对卖家ID ${saleId} 下单，不再重复下单`, false);
            return;
        }
        
        this.addCdkLog(`游戏 ${game.name} 已启用自动下单，正在下单...`, false, true);
        
        // 记录尝试下单的卖家ID
        orderedSaleIds.push(saleId);
        localStorage.setItem(`ordered_${game.id}`, JSON.stringify(orderedSaleIds));
        
        await this.placeCdkOrder(game.name, saleId);
    }
    
    /**
     * 获取随机间隔
     */
    getRandomInterval(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    /**
     * 获取存储的设置
     */
    getStoredSetting(key, defaultValue) {
        return parseInt(localStorage.getItem(key)) || defaultValue;
    }
    
    /**
     * 获取最低价格
     */
    getLowestPrice(sellersData) {
        if (!sellersData?.length) return 0;
        
        const validPrices = sellersData
            .filter(seller => seller.keyPrice && typeof seller.keyPrice === 'number')
            .map(seller => seller.keyPrice);
            
        return validPrices.length > 0 ? Math.min(...validPrices) : 0;
    }
    
    /**
     * 获取已下单的卖家ID列表
     */
    getOrderedSaleIds(gameId) {
        return JSON.parse(localStorage.getItem(`ordered_${gameId}`) || '[]');
    }
    
    /**
     * 检查是否应该自动下单
     */
    shouldAutoOrder() {
        return localStorage.getItem('cdkAutoOrder') !== 'false';
    }
    
    /**
     * 更新本地游戏缓存
     */
    updateLocalGameCache(gameId, updateTime, price) {
        const gameIndex = this.monitoredGames.findIndex(g => g.id === gameId);
        if (gameIndex !== -1) {
            this.monitoredGames[gameIndex].last_update_time = updateTime;
            this.monitoredGames[gameIndex].current_price = price;
        }
    }
    
    /**
     * API调用方法
     */
    async fetchGameInfo(gameId) {
        const response = await fetch(`/api/games/${gameId}/info`);
        if (!response.ok) {
            throw new Error(`获取游戏信息失败: ${response.status}`);
        }
        return response.json();
    }
    
    async fetchGameSellers(gameId) {
        const response = await fetch(`/api/games/${gameId}/sellers?pageSize=1`);
        if (!response.ok) {
            throw new Error(`获取游戏卖家列表失败: ${response.status}`);
        }
        return response.json();
    }
    
    async updateGameInfo(gameId, updateTime, price) {
        const response = await fetch(`/api/games/monitored/${gameId}`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                last_update_time: updateTime,
                current_price: price
            })
        });
        
        if (!response.ok) {
            throw new Error('更新游戏信息失败');
        }
    }
    
    /**
     * 加载监控游戏列表
     */
    async loadMonitoredGames() {
        try {
            const response = await fetch('/api/games/monitored');
            if (!response.ok) {
                throw new Error('获取监控游戏列表失败');
            }
            
            this.monitoredGames = await response.json();
            this.renderMonitoredGames();
        } catch (error) {
            this.addCdkLog('获取监控游戏列表失败: ' + error.message, true);
        }
    }
    
    /**
     * 渲染监控游戏列表
     */
    renderMonitoredGames() {
        const container = this.elements['cdk-monitored-games-container'];
        if (!container) return;
        
        if (!this.monitoredGames?.length) {
            container.innerHTML = '<div class="text-muted text-center py-4">暂无监控游戏，请从游戏搜索或肥皂盒详情页添加...</div>';
            return;
        }
        
        container.innerHTML = this.monitoredGames.map(game => this.createGameCard(game)).join('');
        this.bindGameCardEvents();
    }
    
    /**
     * 创建游戏卡片HTML
     */
    createGameCard(game) {
        const hasTarget = game.target_price && game.target_price > 0;
        const priceReached = hasTarget && game.current_price && game.current_price <= game.target_price;
        const cardClass = `game-monitor-card ${hasTarget ? 'has-target' : ''} ${priceReached ? 'price-reached' : ''}`;
        
        return `
            <div class="${cardClass}" data-game-id="${game.id}" data-game-name="${game.name}" data-game-ava="${game.gameAva || ''}">
                <img src="${game.gameAva || '/static/images/default-game.png'}" class="game-avatar-lg" />
                ${hasTarget ? `<span class="target-price-badge ${priceReached ? 'reached' : ''}">¥${game.target_price}</span>` : ''}
                <div class="game-info-block">
                    <div class="game-title">${game.name}</div>
                    <div class="game-actions-row">
                        <button class="remove-game" data-game-id="${game.id}" title="移除">
                            <svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-trash-2'><polyline points='3 6 5 6 21 6'></polyline><path d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'></path><line x1='10' y1='11' x2='10' y2='17'></line><line x1='14' y1='11' x2='14' y2='17'></line></svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 绑定游戏卡片事件
     */
    bindGameCardEvents() {
        // 移除按钮事件
        document.querySelectorAll('.remove-game').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const gameId = btn.getAttribute('data-game-id');
                this.removeMonitoredGame(gameId);
            });
        });
        
        // 卡片点击事件
        document.querySelectorAll('.game-monitor-card').forEach(card => {
            card.addEventListener('click', () => {
                const gameId = card.getAttribute('data-game-id');
                const gameName = card.getAttribute('data-game-name');
                const gameAva = card.getAttribute('data-game-ava');
                this.openTargetPriceModal(gameId, gameName, gameAva);
            });
        });
    }
    
    /**
     * 添加监控日志
     */
    addCdkLog(message, isError = false, isSuccess = false) {
        const container = this.elements['cdk-monitor-logs'];
        if (!container) return;
        
        // 清除默认消息
        const defaultMsg = container.querySelector('.text-muted');
        if (defaultMsg) {
            container.innerHTML = '';
        }
        
        const logEntry = document.createElement('div');
        logEntry.className = isError ? 'log-error' : (isSuccess ? 'log-success' : 'log-normal');
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${message}`;
        
        container.appendChild(logEntry);
        container.scrollTop = container.scrollHeight;
        
        // 限制日志条数
        this.limitLogEntries(container, this.config.maxLogEntries);
    }
    
    /**
     * 限制日志条数
     */
    limitLogEntries(container, maxEntries) {
        while (container.children.length > maxEntries) {
            container.removeChild(container.firstChild);
        }
    }
    
    /**
     * 事件处理方法
     */
    handleAutoOrderChange(event) {
        const isChecked = event.target.checked;
        localStorage.setItem('cdkAutoOrder', isChecked);
        
        const message = isChecked ? 
            '自动下单已启用，当游戏价格达到目标时将自动下单' : 
            '自动下单已禁用';
        this.addCdkLog(message, false, isChecked);
    }
    
    handleSearchKeyPress(event) {
        if (event.key === 'Enter') {
            this.searchGames();
        }
    }
    
    handleContextMenu(event) {
        // 检查是否在游戏卡片上右键
        let target = event.target;
        while (target && !target.classList.contains('game-card')) {
            target = target.parentElement;
        }
        
        if (target && target.classList.contains('game-card')) {
            event.preventDefault();
            this.showContextMenu(event, target);
        }
    }
    
    /**
     * 其他方法保持原有逻辑但简化代码结构
     */
    
    // 搜索游戏
    async searchGames() {
        const searchInput = this.elements['game-search-input'];
        const resultsContainer = this.elements['game-search-results'];
        
        if (!searchInput || !resultsContainer) return;
        
        const searchTerm = searchInput.value.trim();
        if (!searchTerm) {
            resultsContainer.innerHTML = '<div class="text-warning text-center py-4">请输入游戏名称</div>';
            return;
        }
        
        try {
            resultsContainer.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="sr-only">搜索中...</span></div></div>';
            
            const response = await fetch(`/api/games/search?query=${encodeURIComponent(searchTerm)}`);
            if (!response.ok) throw new Error('搜索游戏失败');
            
            const results = await response.json();
            this.renderSearchResults(results);
        } catch (error) {
            resultsContainer.innerHTML = `<div class="text-danger text-center py-4">搜索失败: ${error.message}</div>`;
        }
    }
    
    // 渲染搜索结果
    renderSearchResults(results) {
        const resultsContainer = this.elements['game-search-results'];
        if (!resultsContainer) return;
        
        if (!results?.length) {
            resultsContainer.innerHTML = '<div class="text-muted text-center py-4">未找到相关游戏</div>';
            return;
        }
        
        const html = results.map(game => {
            const isAlreadyMonitored = this.monitoredGames.some(mg => mg.id === game.id);
            return `
                <div class="list-group-item d-flex align-items-center">
                    <img src="${game.picUrl || '/static/images/default-game.png'}" alt="${game.gameName}" class="game-avatar-small mr-3">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">${game.gameName}</h6>
                        <small class="text-muted">ID: ${game.id}</small>
                    </div>
                    <button class="btn btn-sm btn-${isAlreadyMonitored ? 'secondary' : 'primary'} add-to-monitor" 
                            data-game-id="${game.id}" 
                            data-game-name="${game.gameName}" 
                            data-game-ava="${game.picUrl || ''}"
                            ${isAlreadyMonitored ? 'disabled' : ''}>
                        ${isAlreadyMonitored ? '已添加' : '添加监控'}
                    </button>
                </div>
            `;
        }).join('');
        
        resultsContainer.innerHTML = `<div class="list-group">${html}</div>`;
        this.bindSearchResultEvents();
    }
    
    // 绑定搜索结果事件
    bindSearchResultEvents() {
        document.querySelectorAll('.add-to-monitor:not([disabled])').forEach(btn => {
            btn.addEventListener('click', () => {
                const gameId = btn.getAttribute('data-game-id');
                const gameName = btn.getAttribute('data-game-name');
                const gameAva = btn.getAttribute('data-game-ava');
                this.addGameToMonitor(gameId, gameName, gameAva);
            });
        });
    }
    
    // 添加游戏到监控
    async addGameToMonitor(gameId, gameName, gameAva) {
        try {
            const minInterval = this.getStoredSetting('cdkMinInterval', this.config.defaultMinInterval);
            const maxInterval = this.getStoredSetting('cdkMaxInterval', this.config.defaultMaxInterval);
            
            const response = await fetch('/api/games/monitored', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    id: gameId,
                    name: gameName,
                    gameAva: gameAva,
                    min_interval: minInterval,
                    max_interval: maxInterval
                })
            });
            
            if (!response.ok) throw new Error('添加游戏到监控列表失败');
            
            this.addCdkLog(`游戏 ${gameName} 已添加到监控列表`, false, true);
            await this.loadMonitoredGames();
            
            // 更新按钮状态
            const btn = document.querySelector(`.add-to-monitor[data-game-id="${gameId}"]`);
            if (btn) {
                btn.textContent = '已添加';
                btn.classList.replace('btn-primary', 'btn-secondary');
                btn.disabled = true;
            }
            
            // 关闭模态框
            this.closeAddGameModal();
        } catch (error) {
            this.addCdkLog('添加游戏到监控列表失败: ' + error.message, true);
        }
    }
    
    /**
     * 从监控列表移除游戏
     */
    async removeMonitoredGame(gameId) {
        if (!confirm('确定要从监控列表中移除此游戏吗？')) {
            return;
        }
        
        try {
            const response = await fetch(`/api/games/monitored/${gameId}`, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error('从监控列表中移除游戏失败');
            }
            
            // 如果正在监控，先停止该游戏的监控
            if (this.cdkMonitorTimers.has(gameId)) {
                clearTimeout(this.cdkMonitorTimers.get(gameId));
                this.cdkMonitorTimers.delete(gameId);
            }
            
            // 从本地缓存中移除
            this.monitoredGames = this.monitoredGames.filter(game => game.id !== gameId);
            
            // 重新渲染整个列表
            this.renderMonitoredGames();
            
            this.addCdkLog(`游戏ID ${gameId} 已从监控列表中移除`, false);
        } catch (error) {
            this.addCdkLog('从监控列表中移除游戏失败: ' + error.message, true);
        }
    }
    
    /**
     * 发送价格通知
     */
    sendPriceNotification(game, currentPrice) {
        if (!('Notification' in window)) return;
        
        if (Notification.permission === 'granted') {
            this.showPriceNotification(game, currentPrice);
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    this.showPriceNotification(game, currentPrice);
                }
            });
        }
    }
    
    /**
     * 显示价格通知
     */
    showPriceNotification(game, currentPrice) {
        const title = `${game.name} 价格达标`;
        const options = {
            body: `当前价格: ¥${currentPrice}\n目标价格: ¥${game.target_price}`,
            icon: game.gameAva || '/static/images/default-game.png'
        };
        
        const notification = new Notification(title, options);
        notification.onclick = () => {
            window.focus();
            if (this.elements['set-target-price-modal'] && 
                this.elements['set-target-price-modal'].style.display !== 'flex') {
                this.openTargetPriceModal(game.id, game.name, game.gameAva);
            }
        };
    }
    
    /**
     * 执行CDK下单
     */
    async placeCdkOrder(gameName, saleId) {
        try {
            // 直接从localStorage获取下单配置
            const isMainAccount = localStorage.getItem('cdkUseMainAccount') !== 'false'; // 默认主账号
            const payType = localStorage.getItem('cdkPayType') || 'AU';
            const walletFlag = localStorage.getItem('cdkUseBalance');

            // 准备下单参数
            const params = new URLSearchParams({
                saleId: saleId,
                payType: payType,
                promoCodeId: '',
                walletFlag: walletFlag,
                version: 'v1',
                is_main_account: isMainAccount.toString(),
                orderType: 'cdk',
                gameName: gameName
            });

            // 显示下单中提示
            this.addCdkLog(`正在处理游戏 ${gameName} 的下单请求...`, false, true);

            // 发起下单请求
            const orderResponse = await fetch(`/api/xboot/steamKeyOrder/payOrder?${params.toString()}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!orderResponse.ok) {
                const errorData = await orderResponse.json();
                throw new Error(errorData.detail || '下单失败');
            }

            const orderResult = await orderResponse.json();

            if (orderResult.success) {
                // 获取支付链接
                const alipayUrl = orderResult.result?.alipay || '';
                const payPrice = orderResult.result?.payPrice || '';

                this.addCdkLog(`游戏 ${gameName} 下单成功，价格: ¥${payPrice}`, false, true);
                this.addCdkLog(`支付链接已发送至邮箱，请查收`, false, true);

                // 下单成功后，从监控列表中移除该游戏
                const gameToRemove = this.monitoredGames.find(game => game.name === gameName);
                if (gameToRemove) {
                    this.addCdkLog(`游戏 ${gameName} 已下单成功，将从监控列表中移除`, false, true);
                    this.removeMonitoredGame(gameToRemove.id);
                }
            } else {
                this.addCdkLog(`游戏 ${gameName} 下单失败: ${orderResult.message || '未知错误'}`, true);
            }
        } catch (error) {
            this.addCdkLog(`游戏 ${gameName} 下单过程中出错: ${error.message}`, true);
        }
    }
    
    /**
     * 打开导出模态框
     */
    openExportModal() {
        const exportModal = this.elements['export-modal'];
        const exportContentTextarea = this.elements['export-content'];
        
        if (!exportModal || !exportContentTextarea) return;
        
        // 准备导出数据
        if (!this.monitoredGames || this.monitoredGames.length === 0) {
            alert('当前没有监控游戏可导出');
            return;
        }
        
        // 格式化导出的数据，只包含id, name 和 threshold (target_price)
        const exportData = this.monitoredGames.map(game => {
            return {
                name: game.name,
                id: game.id,
                threshold: game.target_price || 0
            };
        });
        
        // 格式化为带缩进的JSON字符串，使其易于阅读
        const exportText = JSON.stringify(exportData, null, 2);
        
        // 设置文本区域内容
        exportContentTextarea.value = exportText;
        
        // 显示模态框
        exportModal.style.display = 'block';
    }
    
    /**
     * 关闭导出模态框
     */
    closeExportModal() {
        const exportModal = this.elements['export-modal'];
        if (exportModal) {
            exportModal.style.display = 'none';
        }
    }
    
    /**
     * 复制导出内容到剪贴板
     */
    copyExportContent() {
        const exportContentTextarea = this.elements['export-content'];
        const copyExportBtn = this.elements['copy-export-btn'];
        
        if (!exportContentTextarea || !copyExportBtn) return;
        
        exportContentTextarea.select();
        document.execCommand('copy');
        
        // 显示复制成功提示
        const originalText = copyExportBtn.textContent;
        copyExportBtn.textContent = '已复制!';
        copyExportBtn.classList.add('btn-success');
        copyExportBtn.classList.remove('btn-primary');
        
        // 2秒后恢复按钮状态
        setTimeout(() => {
            copyExportBtn.textContent = originalText;
            copyExportBtn.classList.remove('btn-success');
            copyExportBtn.classList.add('btn-primary');
        }, 2000);
    }
    
    /**
     * 打开目标价格设置模态框
     */
    openTargetPriceModal(gameId, gameName, gameAva) {
        const targetPriceModal = this.elements['set-target-price-modal'];
        const targetGameImg = this.elements['target-game-img'];
        const targetGameName = this.elements['target-game-name'];
        const targetPriceInput = this.elements['target-price-input'];
        const priceNotificationType = this.elements['price-notification-type'];
        
        if (!targetPriceModal) return;
        
        targetPriceModal.style.display = 'flex';
        
        // 设置当前游戏ID
        this.currentGameId = gameId;
        
        // 设置游戏信息
        if (targetGameImg) {
            targetGameImg.src = gameAva || '/static/images/default-game.png';
        }
        if (targetGameName) {
            targetGameName.textContent = gameName;
        }
        
        // 获取当前游戏的设置
        const game = this.monitoredGames.find(g => g.id === gameId);
        if (game) {
            // 设置目标价格输入框
            if (targetPriceInput && game.target_price) {
                targetPriceInput.value = game.target_price;
            } else if (targetPriceInput) {
                targetPriceInput.value = '';
            }
            
            // 设置通知类型
            if (priceNotificationType && game.price_notification_type) {
                priceNotificationType.value = game.price_notification_type;
            } else if (priceNotificationType) {
                priceNotificationType.value = 'highlight';
            }
        } else {
            // 清空表单
            if (targetPriceInput) {
                targetPriceInput.value = '';
            }
            if (priceNotificationType) {
                priceNotificationType.value = 'highlight';
            }
        }
        
        // 自动聚焦价格输入框
        setTimeout(() => {
            if (targetPriceInput) {
                targetPriceInput.focus();
            }
        }, 100);
    }
    
    /**
     * 关闭目标价格设置模态框
     */
    closeTargetPriceModal() {
        const targetPriceModal = this.elements['set-target-price-modal'];
        if (targetPriceModal) {
            targetPriceModal.style.display = 'none';
        }
    }
    
    /**
     * 保存目标价格设置
     */
    async saveTargetPrice() {
        if (!this.currentGameId) {
            this.addCdkLog('无法保存：游戏ID为空', true);
            return;
        }
        
        const targetPriceInput = this.elements['target-price-input'];
        const priceNotificationType = this.elements['price-notification-type'];
        
        if (!targetPriceInput || !priceNotificationType) return;
        
        const targetPrice = parseFloat(targetPriceInput.value);
        if (isNaN(targetPrice) || targetPrice <= 0) {
            alert('请输入有效的目标价格');
            targetPriceInput.focus();
            return;
        }
        
        const notificationType = priceNotificationType.value;
        
        try {
            // 保存到服务器
            const response = await fetch(`/api/games/monitored/${this.currentGameId}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    target_price: targetPrice,
                    price_notification_type: notificationType
                })
            });
            
            if (!response.ok) {
                throw new Error('更新目标价格失败');
            }
            
            const result = await response.json();
            
            // 更新本地缓存
            const gameIndex = this.monitoredGames.findIndex(game => game.id === this.currentGameId);
            if (gameIndex !== -1) {
                this.monitoredGames[gameIndex].target_price = targetPrice;
                this.monitoredGames[gameIndex].price_notification_type = notificationType;
            }
            
            // 重新渲染游戏列表
            this.renderMonitoredGames();
            
            // 关闭模态框
            this.closeTargetPriceModal();
            
            // 添加日志
            this.addCdkLog(`游戏ID ${this.currentGameId} 的目标价格已设置为 ${targetPrice}`, false, true);
        } catch (error) {
            this.addCdkLog('设置目标价格失败: ' + error.message, true);
        }
    }
    
    /**
     * 清除目标价格设置
     */
    async clearTargetPrice() {
        if (!this.currentGameId) {
            this.addCdkLog('无法清除：游戏ID为空', true);
            return;
        }
        
        try {
            // 保存到服务器
            const response = await fetch(`/api/games/monitored/${this.currentGameId}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    target_price: null,
                    price_notification_type: null
                })
            });
            
            if (!response.ok) {
                throw new Error('清除目标价格失败');
            }
            
            const result = await response.json();
            
            // 更新本地缓存
            const gameIndex = this.monitoredGames.findIndex(game => game.id === this.currentGameId);
            if (gameIndex !== -1) {
                delete this.monitoredGames[gameIndex].target_price;
                delete this.monitoredGames[gameIndex].price_notification_type;
            }
            
            // 重新渲染游戏列表
            this.renderMonitoredGames();
            
            // 关闭模态框
            this.closeTargetPriceModal();
            
            // 添加日志
            this.addCdkLog(`游戏ID ${this.currentGameId} 的目标价格已清除`, false);
        } catch (error) {
            this.addCdkLog('清除目标价格失败: ' + error.message, true);
        }
    }
    
    /**
     * 显示右键菜单
     */
    showContextMenu(event, target) {
        const gameId = target.getAttribute('data-game-id');
        const gameName = target.getAttribute('data-game-name');
        const gameAva = target.getAttribute('data-game-ava');
        
        if (gameId && gameName) {
            // 创建右键菜单
            const contextMenu = document.createElement('div');
            contextMenu.className = 'context-menu';
            contextMenu.style.position = 'absolute';
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';
            contextMenu.style.background = 'white';
            contextMenu.style.border = '1px solid #ccc';
            contextMenu.style.boxShadow = '2px 2px 5px rgba(0,0,0,0.2)';
            contextMenu.style.zIndex = '1000';
            contextMenu.style.padding = '5px 0';
            contextMenu.innerHTML = `
                <div class="context-menu-item" id="add-to-monitor">添加到CDK监控</div>
            `;
            
            document.body.appendChild(contextMenu);
            
            // 添加菜单项点击事件
            document.getElementById('add-to-monitor').addEventListener('click', () => {
                this.addGameToMonitor(gameId, gameName, gameAva);
                document.body.removeChild(contextMenu);
            });
            
            // 点击其他地方关闭菜单
            document.addEventListener('click', function closeMenu() {
                if (document.body.contains(contextMenu)) {
                    document.body.removeChild(contextMenu);
                }
                document.removeEventListener('click', closeMenu);
            });
        }
    }
}

/**
 * 初始化CDK监控系统
 */
document.addEventListener('DOMContentLoaded', function() {
    const cdkMonitor = new CDKMonitorSystem();
});