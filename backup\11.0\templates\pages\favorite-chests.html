{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}肥皂盒收藏 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header fade-in">
    <h1>肥皂盒收藏</h1>
    <p class="page-description">管理您收藏的肥皂盒。</p>
</header>

<div class="layout-grid">
    <div class="main-content fade-in">
        <div class="card">
            <header class="article-header">
                <h2>{{ icon('package') }} 我的收藏肥皂盒</h2>
                <div class="header-actions">
                    <a href="/chests" class="btn btn-sm btn-outline">
                        {{ icon('list') }} 浏览全部肥皂盒
                    </a>
                </div>
            </header>
            
            <div id="favorites-container">
                <div id="favorites-loading" class="loading-indicator">
                    <div class="spin"></div>
                    <span>正在加载收藏数据...</span>
                </div>
                
                <div id="favorites-empty" class="empty-notice" style="display: none;">
                    <p>您还没有收藏任何肥皂盒</p>
                    <a href="/chests" class="btn btn-primary mt-3">
                        {{ icon('search') }} 浏览肥皂盒
                    </a>
                </div>
                
                <div id="favorites-content" class="chests-list" style="display: none;">
                    <!-- 内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
/* Toast 通知样式 */
#toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.toast {
    margin-bottom: 10px;
    padding: 12px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    color: #FFF;
    transition: all 0.3s ease;
}

.toast-success {
    background-color: #67C23A;
}

.toast-error {
    background-color: #F56C6C;
}

.toast-warning {
    background-color: #E6A23C;
}

.toast-info {
    background-color: #909399;
}

.toast-hide {
    opacity: 0;
    transform: translateX(20px);
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 常量定义
    const FAVORITE_CHESTS_KEY = 'favorite_chests';
    const favoritesContainer = document.getElementById('favorites-container');
    const favoritesLoading = document.getElementById('favorites-loading');
    const favoritesEmpty = document.getElementById('favorites-empty');
    const favoritesContent = document.getElementById('favorites-content');
    
    // 从本地存储加载收藏数据
    function loadFavorites() {
        const favoritesStr = localStorage.getItem(FAVORITE_CHESTS_KEY);
        const favorites = favoritesStr ? JSON.parse(favoritesStr) : [];
        
        // 隐藏加载指示器
        if (favoritesLoading) {
            favoritesLoading.style.display = 'none';
        }
        
        // 如果没有收藏数据
        if (!favorites || favorites.length === 0) {
            if (favoritesEmpty) {
                favoritesEmpty.style.display = 'block';
            }
            return;
        }
        
        // 显示内容区域
        if (favoritesContent) {
            favoritesContent.style.display = 'flex';
            favoritesContent.style.flexWrap = 'wrap';
            favoritesContent.style.gap = '16px';
            
            // 清空现有内容
            favoritesContent.innerHTML = '';
            
            // 按添加时间排序 (最新的在前面)
            favorites.sort((a, b) => {
                return new Date(b.addTime || 0) - new Date(a.addTime || 0);
            });
            
            // 渲染收藏内容
            favorites.forEach(chest => {
                const chestCard = createChestCard(chest);
                favoritesContent.appendChild(chestCard);
            });
        }
    }
    
    // 创建肥皂盒卡片
    function createChestCard(chest) {
        const card = document.createElement('div');
        card.className = 'chest-item';
        card.style.backgroundColor = '#fff';
        card.style.borderRadius = '8px';
        card.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
        card.style.padding = '16px';
        card.style.width = 'calc(33% - 16px)';
        card.style.minWidth = '250px';
        card.style.maxWidth = '350px';
        card.style.position = 'relative';
        card.style.transition = 'all 0.3s ease';
        card.style.cursor = 'pointer';
        
        // 给卡片添加点击事件，跳转到肥皂盒详情页面
        card.addEventListener('click', function() {
            window.location.href = `/chest-detail?id=${chest.id}`;
        });
        
        // 添加鼠标悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            this.style.transform = 'translateY(-2px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            this.style.transform = 'translateY(0)';
        });
        
        // 卡片头部
        const cardHeader = document.createElement('div');
        cardHeader.style.display = 'flex';
        cardHeader.style.justifyContent = 'space-between';
        cardHeader.style.alignItems = 'center';
        cardHeader.style.marginBottom = '16px';
        
        // 肥皂盒名称
        const chestName = document.createElement('div');
        chestName.className = 'chest-name';
        chestName.textContent = chest.name || '未知肥皂盒';
        chestName.style.fontSize = '18px';
        chestName.style.fontWeight = 'bold';
        chestName.style.flex = '1';
        chestName.style.overflow = 'hidden';
        chestName.style.textOverflow = 'ellipsis';
        chestName.style.whiteSpace = 'nowrap';
        
        cardHeader.appendChild(chestName);
        
        // 添加时间
        const addTimeStr = chest.addTime ? new Date(chest.addTime).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }) : '未知时间';
        
        const addTimeInfo = document.createElement('div');
        addTimeInfo.className = 'add-time';
        addTimeInfo.textContent = `收藏于: ${addTimeStr}`;
        addTimeInfo.style.fontSize = '12px';
        addTimeInfo.style.color = '#999';
        addTimeInfo.style.marginTop = '16px';
        
        // 移除按钮
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-btn';
        removeBtn.innerHTML = '移除收藏';
        removeBtn.style.backgroundColor = '#f56c6c';
        removeBtn.style.color = '#fff';
        removeBtn.style.border = 'none';
        removeBtn.style.borderRadius = '4px';
        removeBtn.style.padding = '6px 12px';
        removeBtn.style.fontSize = '14px';
        removeBtn.style.cursor = 'pointer';
        removeBtn.style.marginTop = '12px';
        removeBtn.style.transition = 'background-color 0.3s ease';
        
        // 给移除按钮添加鼠标悬停效果
        removeBtn.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f78989';
        });
        
        removeBtn.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '#f56c6c';
        });
        
        // 添加移除按钮点击事件
        removeBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡到卡片
            
            // 从收藏中移除
            const favorites = JSON.parse(localStorage.getItem(FAVORITE_CHESTS_KEY) || '[]');
            const newFavorites = favorites.filter(item => item.id !== chest.id);
            localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(newFavorites));
            
            // 显示提示
            showToast('已从收藏中移除', 'info');
            
            // 从DOM中移除卡片，使用动画效果
            card.style.opacity = '0';
            card.style.transform = 'scale(0.8)';
            
            setTimeout(() => {
                card.remove();
                
                // 检查是否还有收藏内容
                if (favoritesContent.children.length === 0) {
                    favoritesContent.style.display = 'none';
                    favoritesEmpty.style.display = 'block';
                }
            }, 300);
        });
        
        // 此时我们需要获取最新的肥皂盒信息，显示加载状态
        const loadingInfo = document.createElement('div');
        loadingInfo.className = 'loading-info';
        loadingInfo.textContent = '加载肥皂盒信息...';
        loadingInfo.style.fontSize = '14px';
        loadingInfo.style.color = '#409EFF';
        loadingInfo.style.margin = '10px 0';
        
        // 将元素添加到卡片
        card.appendChild(cardHeader);
        card.appendChild(loadingInfo);
        card.appendChild(addTimeInfo);
        card.appendChild(removeBtn);
        
        // 异步加载肥皂盒基本信息
        fetchChestBasicInfo(chest.id).then(info => {
            if (info) {
                // 移除加载提示
                loadingInfo.remove();
                
                // 添加价格标签
                const priceTag = document.createElement('div');
                priceTag.className = 'chest-price';
                priceTag.textContent = info.oncePrice ? `¥${parseFloat(info.oncePrice).toFixed(2)}` : 'N/A';
                priceTag.style.color = '#f56c6c';
                priceTag.style.fontWeight = 'bold';
                priceTag.style.fontSize = '16px';
                cardHeader.appendChild(priceTag);
                
                // 添加卖家信息
                const sellerInfo = document.createElement('div');
                sellerInfo.className = 'chest-seller';
                sellerInfo.textContent = `卖家: ${info.nameSeller || '未知'}`;
                sellerInfo.style.fontSize = '14px';
                sellerInfo.style.color = '#666';
                sellerInfo.style.marginBottom = '12px';
                card.insertBefore(sellerInfo, addTimeInfo);
                
                // 添加概率信息
                const probabilityInfo = document.createElement('div');
                probabilityInfo.className = 'chest-probability';
                
                // 计算概率显示
                let probabilityDisplay = '未知';
                if (info.rate1 !== undefined) {
                    // 如果rate1是小数(0-1之间)，则乘以100转为百分比
                    probabilityDisplay = info.rate1 <= 1 ? 
                        `${(info.rate1 * 100).toFixed(2)}%` : 
                        `${parseFloat(info.rate1).toFixed(2)}%`;
                }
                
                probabilityInfo.textContent = `概率: ${probabilityDisplay}`;
                probabilityInfo.style.fontSize = '14px';
                probabilityInfo.style.color = '#409EFF';
                probabilityInfo.style.marginBottom = '12px';
                card.insertBefore(probabilityInfo, addTimeInfo);
                
                // 添加抽取进度信息
                const progressInfo = document.createElement('div');
                progressInfo.className = 'chest-progress';
                
                // 当前未抽 = 总数 - 已抽
                const totalDraw = info.totalDraw || 0;
                const curDraw = info.curDraw || 0;
                const remainDraws = totalDraw - curDraw;
                
                progressInfo.innerHTML = `抽取进度: <span style="font-weight: bold;">${remainDraws}</span>/<span>${totalDraw}</span>`;
                progressInfo.style.fontSize = '14px';
                progressInfo.style.color = '#67c23a';
                progressInfo.style.marginBottom = '16px';
                card.insertBefore(progressInfo, addTimeInfo);
            }
        }).catch(error => {
            // 显示加载失败
            loadingInfo.textContent = '加载肥皂盒信息失败';
            loadingInfo.style.color = '#F56C6C';
            console.error('加载肥皂盒信息失败:', error);
        });
        
        return card;
    }
    
    /**
     * 获取肥皂盒基本信息
     * @param {string} chestId - 肥皂盒ID
     * @returns {Promise<Object|null>} - 肥皂盒信息对象或null
     */
    async function fetchChestBasicInfo(chestId) {
        try {
            const response = await fetch(`/api/xboot/chest/showOne?chestId=${chestId}`);
            
            if (!response.ok) {
                throw new Error(`请求失败: ${response.status} ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.code !== 200 || !data.result) {
                throw new Error(data.message || '获取肥皂盒信息失败');
            }
            
            return data.result;
        } catch (error) {
            console.error(`获取肥皂盒(${chestId})基本信息失败:`, error);
            return null;
        }
    }
    
    /**
     * 显示提示消息
     * @param {string} message - 提示消息
     * @param {string} type - 消息类型 (success, error, warning, info)
     */
    function showToast(message, type = 'success') {
        // 检查是否已存在toast容器，如果不存在则创建
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.style.position = 'fixed';
            toastContainer.style.top = '20px';
            toastContainer.style.right = '20px';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }
        
        // 创建新的toast元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        toast.style.backgroundColor = type === 'success' ? '#67C23A' : 
                                     type === 'error' ? '#F56C6C' : 
                                     type === 'warning' ? '#E6A23C' : '#909399';
        toast.style.color = '#FFF';
        toast.style.padding = '10px 20px';
        toast.style.borderRadius = '4px';
        toast.style.marginBottom = '10px';
        toast.style.boxShadow = '0 2px 12px 0 rgba(0, 0, 0, 0.1)';
        toast.style.transition = 'all 0.3s ease';
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(20px)';
        
        // 添加到容器
        toastContainer.appendChild(toast);
        
        // 触发重排以应用过渡效果
        void toast.offsetWidth;
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
        
        // 设置定时器移除
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(20px)';
            
            setTimeout(() => {
                if (toastContainer.contains(toast)) {
                    toastContainer.removeChild(toast);
                }
            }, 300); // 淡出动画后移除
        }, 3000); // 3秒后开始淡出
    }
    
    // 加载收藏数据
    loadFavorites();
});
</script>
{% endblock %} 