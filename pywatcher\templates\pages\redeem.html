{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}Steam Key激活 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <div class="header-content">
        <h1>
            {{ icon('package') }}
            Steam Key激活
        </h1>
        <p class="page-description">
            批量激活Steam游戏序列号，支持多种激活方式和ASF集成。
        </p>
    </div>
</header>

<!-- 激活设置卡片 -->
<section>
    <div class="card">
        <h3>激活设置</h3>
        <div class="form-grid">
            <div class="form-group">
                <label for="activation-method">激活方式</label>
                <select id="activation-method" class="form-control">
                    <option value="web">Web激活（推荐）</option>
                    <option value="asf">ASF激活</option>
                    <option value="newTab">新标签页激活</option>
                </select>
            </div>
            <div class="form-group" id="asf-settings" style="display: none;">
                <label for="asf-bot">ASF Bot名称</label>
                <input type="text" id="asf-bot" class="form-control" placeholder="留空使用默认Bot">
            </div>
        </div>
        
        <!-- ASF配置 -->
        <div id="asf-config" style="display: none;">
            <h4>ASF IPC配置</h4>
            <div class="form-grid">
                <div class="form-group">
                    <label for="asf-protocol">协议</label>
                    <select id="asf-protocol" class="form-control">
                        <option value="http">HTTP</option>
                        <option value="https">HTTPS</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="asf-host">主机地址</label>
                    <input type="text" id="asf-host" class="form-control" value="127.0.0.1" placeholder="IP地址或域名">
                </div>
                <div class="form-group">
                    <label for="asf-port">端口</label>
                    <input type="number" id="asf-port" class="form-control" value="1242" placeholder="默认1242">
                </div>
                <div class="form-group">
                    <label for="asf-password">密码</label>
                    <input type="password" id="asf-password" class="form-control" placeholder="ASF IPC密码">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Key输入区域 -->
<section>
    <div class="card">
        <h3>Steam Key输入</h3>
        <div class="form-group">
            <label for="steam-keys">游戏序列号</label>
            <textarea id="steam-keys" class="form-control" rows="8" 
                placeholder="支持批量激活，每行一个Key或用逗号分隔
格式示例：
XXXXX-XXXXX-XXXXX
YYYYY-YYYYY-YYYYY,ZZZZZ-ZZZZZ-ZZZZZ

也可以直接粘贴包含Key的文本，会自动提取所有有效的Steam Key"></textarea>
        </div>
        
        <div class="form-actions">
            <button id="extract-keys-btn" class="outline">
                {{ icon('search') }} 提取Key
            </button>
            <button id="activate-keys-btn" class="primary" disabled>
                {{ icon('play') }} 激活Key (<span id="key-count">0</span>个)
            </button>
            <button id="test-asf-btn" class="outline" style="display: none;">
                {{ icon('settings') }} 测试ASF连接
            </button>
        </div>
    </div>
</section>

<!-- Steam登录状态区域 -->
<section id="steam-login-section">
    <div class="card">
        <h3>Steam登录状态</h3>
        <div class="login-status-container">
            <div id="steam-login-status" class="login-status">
                <span class="status-checking">🔄 正在检查登录状态...</span>
            </div>
            <div class="login-actions">
                <button id="steam-login-btn" class="primary" style="display: none;">
                    {{ icon('user') }} 登录Steam
                </button>
                <button id="check-login-btn" class="outline">
                    {{ icon('refresh-cw') }} 检查登录
                </button>
            </div>
        </div>
        
        <div class="login-help">
            <h4>登录说明</h4>
            <ul>
                <li><strong>Web激活</strong>：需要在浏览器中登录Steam账户</li>
                <li><strong>ASF激活</strong>：无需浏览器登录，使用ASF机器人账户</li>
                <li><strong>新标签页激活</strong>：在新标签页打开Steam官方激活页面</li>
            </ul>
            <div class="cookie-helper">
                <p>如果浏览器第三方Cookie受限，导致本站无法携带Steam登录信息，可将Steam原始Cookie串粘贴保存到本站HttpOnly Cookie，供后端代理使用（更安全，前端JS不可读）：</p>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="steam-cookie-input">Steam Cookie（可选）</label>
                        <textarea id="steam-cookie-input" class="form-control" rows="3" placeholder="例如：steamCountry=...; sessionid=...; steamLoginSecure=..."></textarea>
                    </div>
                </div>
                <div class="form-actions">
                    <button id="save-steam-cookie-btn" class="outline">保存到本站（HttpOnly）</button>
                    <small>保存后点击“检查登录”即可使用后端代理激活。</small>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 激活结果区域 -->
<section>
    <div class="card">
        <header class="article-header">
            <h3>激活结果</h3>
            <div class="header-actions">
                <button id="export-unused-btn" class="outline" style="display: none;">
                    {{ icon('package') }} 导出未使用Key
                </button>
                <button id="clear-results-btn" class="outline">
                    {{ icon('trash-2') }} 清空结果
                </button>
            </div>
        </header>
        
        <!-- 统计信息 -->
        <div id="activation-stats" class="stats-grid" style="display: none;">
            <div class="stat-item">
                <span class="stat-label">总数</span>
                <span class="stat-value" id="total-keys">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">成功</span>
                <span class="stat-value success" id="success-keys">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">失败</span>
                <span class="stat-value error" id="failed-keys">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">处理中</span>
                <span class="stat-value" id="processing-keys">0</span>
            </div>
        </div>

        <!-- 未使用Key区域 -->
        <div id="unused-keys-area" class="notice-box" style="display: none;">
            <h4>未使用的Key</h4>
            <div id="unused-keys-list"></div>
        </div>

        <!-- 激活记录表格 -->
        <div id="activation-results" style="display: none;">
            <div class="table-responsive">
                <table class="activation-table">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Steam Key</th>
                            <th>状态</th>
                            <th>详情</th>
                            <th>游戏/包</th>
                        </tr>
                    </thead>
                    <tbody id="activation-tbody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

<!-- ASF指令执行区域 -->
<section id="asf-commands-section" style="display: none;">
    <div class="card">
        <h3>ASF指令执行</h3>
        <div class="form-group">
            <label for="asf-command">ASF指令</label>
            <input type="text" id="asf-command" class="form-control" placeholder="输入ASF指令，如：!status, !2fa, !pause等">
        </div>
        <div class="form-actions">
            <button id="execute-asf-btn" class="primary">
                {{ icon('play') }} 执行指令
            </button>
            <button id="asf-status-btn" class="outline">
                {{ icon('settings') }} 状态
            </button>
            <button id="asf-2fa-btn" class="outline">
                {{ icon('settings') }} 2FA
            </button>
            <button id="asf-pause-btn" class="outline">
                {{ icon('pause') }} 暂停挂卡
            </button>
            <button id="asf-resume-btn" class="outline">
                {{ icon('play') }} 恢复挂卡
            </button>
        </div>
        
        <div id="asf-output" class="code-block" style="display: none;">
            <h4>执行结果</h4>
            <pre id="asf-result"></pre>
        </div>
    </div>
</section>

<style>
/* 页面特定样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 1rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--background-secondary);
    border-radius: 8px;
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.stat-value.success {
    color: var(--success-color);
}

.stat-value.error {
    color: var(--error-color);
}

.activation-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.activation-table th,
.activation-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.activation-table th {
    background: var(--background-secondary);
    font-weight: 600;
}

.activation-table tbody tr:hover {
    background: var(--background-secondary);
}

.status-success {
    color: var(--success-color);
    font-weight: 600;
}

.status-error {
    color: var(--error-color);
    font-weight: 600;
}

.status-processing {
    color: var(--warning-color);
    font-weight: 600;
}

.notice-box {
    border: 1px solid var(--warning-color);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    background: var(--warning-background);
}

.notice-box h4 {
    margin: 0 0 0.5rem 0;
    color: var(--warning-color);
}

.unused-key-item {
    padding: 0.25rem 0.5rem;
    margin: 0.25rem;
    background: var(--background-primary);
    border-radius: 4px;
    display: inline-block;
    font-family: monospace;
    font-size: 0.875rem;
}

.code-block {
    margin-top: 1rem;
}

.code-block pre {
    background: var(--background-secondary);
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* Steam登录状态样式 */
.login-status-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1rem;
}

.login-status {
    flex: 1;
}

.login-actions {
    display: flex;
    gap: 0.5rem;
}

.status-success {
    color: var(--success-color);
    font-weight: 600;
}

.status-error {
    color: var(--error-color);
    font-weight: 600;
}

.status-checking {
    color: var(--warning-color);
    font-weight: 600;
}

.login-help {
    padding: 1rem;
    background: var(--background-secondary);
    border-radius: 8px;
    margin-top: 1rem;
}

.login-help h4 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
}

.login-help ul {
    margin: 0;
    padding-left: 1.5rem;
}

.login-help li {
    margin-bottom: 0.25rem;
    color: var(--text-secondary);
}

.login-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
}

.login-required-content p,
.login-expired-content p {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
}

/* 模态框样式 */
.modal-overlay h3 {
    margin: 0;
    color: var(--text-primary);
}

.login-options button {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-primary);
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.login-options button.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.login-options button.outline:hover {
    background: var(--background-secondary);
}

.login-options button.primary:hover {
    opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .form-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .activation-table {
        font-size: 0.875rem;
    }
    
    .activation-table th,
    .activation-table td {
        padding: 0.5rem;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/redeem.js') }}" defer></script>
{% endblock %}
