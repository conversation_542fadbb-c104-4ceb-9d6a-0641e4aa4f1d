{"web_config_path": "wish_monitor/config.json", "api_monitor_config_path": "API/monitor_config.json", "wishlist_path": "wishlist.json", "detlog_api_url": "https://steampy.com/xboot/detLog/show", "listsale_api_url_template": "https://steampy.com/xboot/steamKeySale/listSale?pageNumber=1&pageSize=1&sort=keyPrice&order=asc&gameId={game_id}", "payorder_api_url": "https://steampy.com/xboot/steamKeyOrder/payOrder", "base_api_url_for_headers": "https://steampy.com", "app_token": "WAP", "detlog_min_interval_seconds": 5, "detlog_max_interval_seconds": 60, "price_fetch_window_seconds": 300, "listsale_min_interval_seconds": 1, "listsale_max_interval_seconds": 8, "http_timeout_seconds": 10, "action_cooldown_seconds": 3600, "api_retry_count": 2, "max_consecutive_cookie_failures": 3, "cookie_cooldown_seconds": 300}