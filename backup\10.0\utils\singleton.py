class Singleton(type):
    """
    一个线程安全的单例元类。
    """
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            # 在多线程环境中，为了确保只创建一个实例，
            # 通常需要在这里加锁，但由于我们的应用是基于asyncio的，
            # 在单个事件循环中，这个简单的检查通常是足够的。
            # 对于跨线程使用（例如，如果将来引入线程池），则需要锁。
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls] 