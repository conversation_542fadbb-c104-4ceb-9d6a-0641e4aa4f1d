/* ---
Root & Theming
--- */
:root {
  /* 主色调 */
  --primary: #4f46e5;
  --primary-hover: #4338ca;
  --primary-focus: rgba(79, 70, 229, 0.125);
  --primary-inverse: #fff;
  
  /* 辅助颜色 */
  --secondary: #0ea5e9;
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;
  
  /* RGB颜色变量，用于rgba */
  --primary-rgb: 79, 70, 229;
  --secondary-rgb: 14, 165, 233;
  --success-rgb: 34, 197, 94;
  --warning-rgb: 245, 158, 11;
  --danger-rgb: 239, 68, 68;
  --text-muted-rgb: 100, 116, 139;
  
  /* 明亮主题 */
  --background-color: #f8fafc;
  --card-background-color: #ffffff;
  --card-border-color: #e2e8f0;
  --card-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --text-color: #0f172a;
  --text-muted-color: #64748b;
  --log-bg-color: #f1f5f9;
  --log-text-color: #334155;
  
  /* 布局变量 */
  --header-height: 60px;
  --bottom-nav-height: 60px;
  --container-padding: 1.5rem;
  --section-spacing: 2rem;
  --card-radius: 0.75rem;
  
  /* 动画 */
  --transition-speed: 0.2s;
  --loader-color: rgba(66, 99, 235, 0.8);
  --loader-size: 40px;

  /* 新添加的状态相关变量 */
  --primary-color: #4a6cf7;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --body-bg: #f5f7fb;
  --body-color: #212529;
  --header-bg: #ffffff;
  --card-bg: #ffffff;
  --border-color: #e9ecef;
  --shadow-color: rgba(0, 0, 0, 0.05);
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

[data-theme="dark"] {
  /* 暗色主题 */
  --primary: #818cf8;
  --primary-hover: #a78bfa;
  --primary-focus: rgba(129, 140, 248, 0.25);
  
  /* RGB颜色变量，用于rgba */
  --primary-rgb: 129, 140, 248;
  --secondary-rgb: 56, 189, 248;
  --success-rgb: 74, 222, 128;
  --warning-rgb: 250, 204, 21;
  --danger-rgb: 248, 113, 113;
  --text-muted-rgb: 148, 163, 184;
  
  --background-color: #020617;
  --card-background-color: #0f172a;
  --card-border-color: #1e293b;
  --card-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --text-color: #e2e8f0;
  --text-muted-color: #94a3b8;
  --log-bg-color: #1e293b;
  --log-text-color: #cbd5e1;
  --loader-color: rgba(116, 143, 252, 0.8);

  /* 新添加的状态相关变量 */
  --primary-color: #4e73df;
  --secondary-color: #858796;
  --success-color: #1cc88a;
  --danger-color: #e74a3b;
  --warning-color: #f6c23e;
  --info-color: #36b9cc;
  --light-color: #f8f9fc;
  --dark-color: #5a5c69;
  --body-bg: #1a1c23;
  --body-color: #e0e0e0;
  --header-bg: #252830;
  --card-bg: #252830;
  --border-color: #2e3035;
  --shadow-color: rgba(0, 0, 0, 0.15);
}

/* ---
基础样式
--- */
body {
  font-family: var(--font-family);
  background-color: var(--body-bg);
  color: var(--body-color);
  /* padding-top is now applied to main content */
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-speed) ease;
  pointer-events: auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部加载条 */
#top-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary), var(--primary));
  background-size: 200% 200%;
  z-index: 9999;
  animation: top-loader-progress 1.5s linear infinite, move-bg 1.5s linear infinite;
  display: none; /* 默认隐藏 */
}

@keyframes top-loader-progress {
  0% { width: 0%; }
  50% { width: 100%; }
  100% { width: 0%; opacity: 0; }
}

@keyframes move-bg {
    0%{background-position:0% 50%}
    50%{background-position:100% 50%}
    100%{background-position:0% 50%}
}

/* Unified container styles */
.container {
  padding: var(--container-padding);
  max-width: 1200px;
  margin: 0 auto;
}

/* Add top padding specifically to the main container to avoid the header */
main.container {
  padding-top: calc(var(--header-height) + var(--section-spacing));
}

/* 排版样式 */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-color);
}

h1 { font-size: 1.875rem; }
h2 { font-size: 1.25rem; }
h3 { font-size: 1.25rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-muted-color);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
}

a:hover {
  color: var(--primary-hover);
}

/* 卡片样式 - 重构为 .card 类 */
.card {
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  box-shadow: var(--card-box-shadow);
  border-radius: var(--card-radius);
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  transition: all 0.2s ease;
}

/* 用于需要限制内容宽度的卡片，例如仪表盘上的状态卡片 */
.card-constrained {
  max-width: 800px; 
  margin-left: auto;
  margin-right: auto;
}

/* article header 样式现在由 .article-header 直接处理，不再需要 article 前缀 */
/*
article header {
  margin-bottom: 1rem;
}
*/

/* 节段间距 */
section {
  margin-bottom: var(--section-spacing);
}

/* ---
导航和布局
--- */
/* 顶部导航栏 */
.app-header {
  background-color: var(--header-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-bottom: 1px solid var(--card-border-color);
  
  /* Restore fixed position */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  z-index: 100;
  display: none; /* Hidden on mobile by default */
  box-shadow: 0 2px 10px var(--shadow-color);
}

[data-theme="dark"] .app-header {
    background-color: var(--header-bg);
}

.app-header .container {
    height: 100%;
}

.app-header nav {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand-logo {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  color: var(--primary-color);
}

.brand-logo i {
  margin-right: 0.5rem;
  color: var(--primary);
}

.brand-logo strong {
    font-weight: 700;
}

.nav-links {
  display: flex;
  gap: 1rem;
}

.app-header .nav-link {
  color: var(--text-muted-color);
  padding: 0.5rem 1rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.app-header .nav-link:hover {
  color: var(--primary);
  background-color: transparent;
}

.app-header .nav-link.is-active {
  color: var(--primary);
  background-color: transparent;
  position: relative;
}

/* 激活状态下划线 */
.app-header .nav-link.is-active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary);
}

.mode-toggle {
  display: flex;
  align-items: center;
  margin-left: 1rem; /* Add some space */
}

/* 底部导航栏（移动端） */
.bottom-nav {
  background-color: var(--header-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-top: 1px solid var(--card-border-color);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottom-nav-height);
  z-index: 100;
  padding: 0.5rem 0;
  box-shadow: 0 -2px 10px var(--shadow-color);
  display: none;
}

[data-theme="dark"] .bottom-nav {
    background-color: var(--header-bg);
}

.bottom-nav .nav-link {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-muted-color);
  text-decoration: none;
  font-size: 0.75rem;
  transition: color var(--transition-speed) ease;
}

.bottom-nav .nav-link i {
  font-size: 1.25rem;
  margin-bottom: 0rem;
  transition: transform var(--transition-speed) ease;
}

.bottom-nav .nav-link.is-active {
  color: var(--primary);
}

.bottom-nav .nav-link.is-active i {
  transform: translateY(-2px);
}

/* 响应式布局调整 */
@media (min-width: 768px) {
  .app-header {
    display: block;
  }
  .bottom-nav {
    display: none;
  }
  body {
    padding-bottom: 0; /* 重置底部内边距 */
  }
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}

@media (max-width: 767px) {
  body {
    padding-bottom: var(--bottom-nav-height);
  }
  
  .bottom-nav {
    display: flex;
    justify-content: space-around;
  }
  
  main.container {
    padding-top: var(--container-padding); 
    padding-top: calc(var(--header-height) + var(--container-padding)); /* 确保内容不被顶部导航栏遮挡 */
  }
}

/* 布局网格 */
.layout-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 992px) {
  .layout-grid {
    /* Make grid more adaptive */
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

/* ---
组件样式
--- */
/* 图标 */
i.icon {
  display: inline-block;
  font-style: normal;
  vertical-align: middle;
  font-size: 1.25rem;
  line-height: 1;
  margin-right: 0.25rem;
}

h1 i, h2 i, header strong i {
  margin-right: 0.75rem;
  color: var(--primary);
}

/* 按钮 */
button, [role="button"] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

button:hover, [role="button"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

button:active, [role="button"]:active {
    transform: translateY(0);
}

button.icon-btn, a.icon-btn {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted-color);
  transition: all var(--transition-speed) ease;
}

button.icon-btn svg, a.icon-btn svg {
  width: 22px;
  height: 22px;
}

button.outline, a.outline {
  background-color: transparent;
  border-color: var(--card-border-color);
  color: var(--text-color);
}

button.outline:hover, a.outline:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--text-muted-color);
}

a.icon-btn:hover, button.icon-btn:hover {
  color: var(--primary);
}

button.secondary {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: white;
}

/* 日志框 */
.log-box {
  background-color: var(--body-bg);
  color: var(--body-color);
  height: 400px;
  overflow-y: auto;
  padding: 1rem;
  border-radius: 8px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
  border: 1px solid var(--border-color);
}

.log-box p {
  margin-bottom: 0.5em;
  word-break: break-all;
  color: var(--body-color) !important;
}

.log-success { color: var(--success) !important; }
.log-warn { color: var(--warning) !important; }
.log-error { color: var(--danger) !important; }

/* 文章头部 */
.article-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  align-items: end;
}

input, select, textarea {
  width: 100%;
  border-radius: 0.5rem;
  padding: 0.75rem;
  border: 1px solid var(--card-border-color);
  background-color: var(--background-color);
  color: var(--text-color);
  transition: all var(--transition-speed) ease;
}

input:focus, select:focus, textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-focus);
}

label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.form-group {
  margin-bottom: 1rem;
}

@media (min-width: 576px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
  .form-group {
    grid-column: span 1;
  }
  .form-actions {
    grid-column: span 2;
  }
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 0.5rem;
}

/* 列表样式 */
#mobile-account-list-container ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

#chest-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

#mobile-account-list-container li {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  margin-bottom: 0.75rem;
  transition: all var(--transition-speed) ease;
  position: relative;
}

#chest-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  transition: all var(--transition-speed) ease;
}

#mobile-account-list-container li:hover {
  box-shadow: var(--card-box-shadow);
}

#chest-list li:hover {
  box-shadow: var(--card-box-shadow);
  transform: translateY(-1px);
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.account-badges {
  display: flex;
  gap: 0.5rem;
  margin: 0.25rem 0;
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

.bg-primary { background-color: #4a6cf7; }
.bg-success { background-color: #28a745; }
.bg-info { background-color: #17a2b8; }
.bg-warning { background-color: #ffc107; color: #212529; }
.bg-secondary { background-color: #6c757d; }

.account-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.account-type-menu {
  position: absolute;
  top: 40px;
  right: 0;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  padding: 0.75rem;
  z-index: 100;
  width: 180px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform-origin: top right;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.menu-header {
  font-weight: 600;
  padding: 0.5rem;
  border-bottom: 1px solid var(--card-border-color);
  margin-bottom: 0.75rem;
  text-align: center;
}

.menu-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.menu-options button {
  background: none;
  border: none;
  text-align: left;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.menu-options button:hover {
  background-color: var(--primary-focus);
  color: var(--primary);
}

/* 全局加载器 */
#global-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

#global-loader svg {
  width: 40px;
  height: 40px;
  color: white;
}

/* 状态标记 */
mark {
  padding: 0.25em 0.5em;
  border-radius: 4px;
  background-color: var(--primary-focus);
  color: var(--primary);
}

mark.success {
  background-color: rgba(64, 192, 87, 0.15);
  color: var(--success);
}

mark.warning {
  background-color: rgba(250, 176, 5, 0.15);
  color: var(--warning);
}

mark.danger {
  background-color: rgba(250, 82, 82, 0.15);
  color: var(--danger);
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn var(--transition-speed) ease;
}

/* 加载指示器样式 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  color: var(--text-muted-color);
}

.error-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  color: var(--danger);
}

.spin {
  animation: spin 1.5s linear infinite;
}

/* 状态样式 */
.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.status-label {
  width: 8rem;
  color: var(--text-muted-color);
}

.text-muted {
  color: var(--text-muted-color);
}

p.page-description {
    font-size: 1.125rem;
    color: var(--text-muted-color);
}

/* 游戏搜索结果列表样式 */
#game-results-container ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 0.75rem;
}

#game-results-container li {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background-color: var(--log-bg-color);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  transition: all var(--transition-speed) ease;
}

#game-results-container li:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--card-box-shadow);
}

.game-image {
  width: 120px;
  height: 45px;
  margin-right: 1rem;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;
  background-color: var(--background-color);
}

.game-info {
    display: flex;
    flex-direction: column;
}

.game-prices {
    display: flex;
    align-items: center;
    gap: 0.75rem; /* Space between price groups */
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.price-label {
    color: var(--text-muted-color);
}

.price-value {
    font-weight: 600;
}

.price-current {
    color: var(--success);
}

.price-average {
    color: var(--warning);
}

/* ---
新功能: 游戏收藏 & 卖家模态框  -> 重构为仅搜索
--- */

/* 页面头部 */
.page-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--card-border-color);
}
.page-header h1 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* 搜索结果区域 */
.results-area {
    margin-top: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

/* 新的结果卡片样式 */
.result-card {
  position: relative;
  display: flex;
  align-items: stretch;
}

.result-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--card-box-shadow);
}

.result-card-image {
  width: 120px;
  height: 68px; /* 根据常见横幅图比例调整，保持一致性 */
  object-fit: cover; /* 切换回 cover 以填充容器，避免留白 */
  border-radius: 0.375rem;
  background-color: #f1f5f9;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
}

.result-card-info {
  flex-grow: 1;
  min-width: 0; /* 防止文本溢出 */
}

.result-card-name {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.result-card-id {
  font-size: 0.875rem;
}

.sellers-btn {
  margin-left: auto; /* 按钮推到最右侧 */
  flex-shrink: 0;
}

/* 查看卖家按钮 */
.sellers-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
    white-space: nowrap;
    margin-top: 8px;
}

.sellers-btn:hover {
    background-color: #45a049;
}

/* 收藏按钮样式 */
.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.favorite-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted-color);
  padding: 0;
  line-height: 1;
  transition: color 0.2s, transform 0.2s;
}

.favorite-btn:hover {
  transform: scale(1.2);
  color: var(--warning-color);
}

.favorite-btn.active {
  color: var(--warning-color);
  transform: scale(1.1);
}

/* 卡片右侧按钮组 */
.card-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.75rem;
  padding-left: 1rem;
}

/* 卖家按钮 */
.sellers-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 14px;
  white-space: nowrap;
}

.sellers-btn:hover {
  background-color: #45a049;
}

/* 模态框标题栏操作按钮 */
.modal-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted-color);
  transition: color 0.2s, transform 0.2s;
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
}

.refresh-btn:hover {
  color: var(--primary-color);
  background-color: rgba(0, 0, 0, 0.05);
}

.refresh-btn svg {
  width: 18px;
  height: 18px;
}

/* 卖家模态框样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.7);
}

.modal-content {
  background-color: var(--card-background-color);
  margin: 2% auto; /* 从5%改为2%，让模态框显示得更高 */
  padding: 0;
  border-radius: 8px;
  width: 90%;
  max-width: 900px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  position: relative;
  max-height: 96vh; /* 确保不超出屏幕，但可以更大 */
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--card-border-color);
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25em;
  color: var(--text-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-muted-color);
}

.close-btn:hover {
  color: var(--text-color);
}

.modal-body {
    padding: 10px 0;
}

#modal-loading {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: #666;
}

.sellers-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.seller-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.seller-item:last-child {
    border-bottom: none;
}

.seller-info-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.seller-name {
    font-weight: 500;
    color: var(--text-color);
}

.seller-price {
    font-weight: bold;
    color: #e53935;
}

.seller-discount {
    color: #ff6d00;
    font-weight: 500;
    background-color: rgba(255, 109, 0, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.seller-quantity {
    color: #2196f3;
}

.seller-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.seller-id {
    font-size: 0.8rem;
    color: var(--text-muted-color);
    opacity: 0.7;
}

.error {
    color: #e53935;
    font-weight: bold;
}

/* 新添加的状态相关样式 */
.status-display {
  background-color: var(--body-bg);
  border-radius: 0.25rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-weight: 500;
}

mark {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.status-running {
  background-color: rgba(40, 167, 69, 0.2);
  color: var(--success-color);
}

.status-stopped {
  background-color: rgba(220, 53, 69, 0.2);
  color: var(--danger-color);
}

/* 工具提示 */
[data-tooltip] {
  position: relative;
}

[data-tooltip]::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.25rem 0.5rem;
  background-color: var(--dark-color);
  color: white;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
}

[data-tooltip]:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Toast通知样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--success-color);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1050;
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s, transform 0.3s;
    font-weight: 500;
    max-width: 300px;
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast.error {
    background-color: var(--danger-color);
}

[data-theme="dark"] .toast {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* ---
游戏搜索页面 - Game Search Page
--- */

/* 为结果容器设置最小高度，防止搜索时页面布局跳动 */
#game-results-container {
  min-height: 300px;
  transition: all var(--transition-speed) ease;
}

/* 游戏结果卡片 */
.result-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: var(--card-radius);
  margin-bottom: 1rem;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: var(--primary);
}

/* 卡片中的游戏图片 */
.result-card-image {
  width: 120px;
  height: 68px; /* 根据常见横幅图比例调整，保持一致性 */
  object-fit: cover; /* 切换回 cover 以填充容器，避免留白 */
  border-radius: 0.375rem;
  background-color: #f1f5f9;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
}

/* 游戏信息容器 */
.result-card-info {
  flex-grow: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  gap: 0.25rem; /* 信息间的垂直间距 */
}

/* 游戏名称 */
.result-card-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--body-color);
}

/* 游戏ID */
.result-card-id {
  font-size: 0.8rem;
  color: var(--text-muted-color);
}

/* 价格信息容器 */
.result-card-prices {
  display: flex;
  gap: 1rem; /* 价格标签间的间距 */
  margin-top: 0.5rem;
}

/* 价格标签 */
.price-tag {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.remain-value {
  color: var(--primary-color);
  font-weight: 600;
}

.price-tag .key-price {
  color: var(--success-color); /* 绿色，表示当前价格 */
  font-weight: 600;
}

.price-tag .key-ave-amt {
  color: var(--info-color); /* 蓝色，表示平均参考价 */
  font-weight: 600;
}

/* 查看卖家按钮 */
.sellers-btn {
  margin-left: auto; /* 将按钮推到最右边 */
  flex-shrink: 0;
}


/* 为搜索卡片设置特定样式，使其与结果区对齐 */
#search-card {
  width: 100%; /* 强制撑满父容器宽度 */
  margin-bottom: var(--section-spacing); /* 增加与下方结果的间距 */
}

/* 收藏按钮样式 */
.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.favorite-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted-color);
  padding: 0;
  line-height: 1;
  transition: color 0.2s, transform 0.2s;
}

.favorite-btn:hover {
  transform: scale(1.2);
  color: var(--warning-color);
}

.favorite-btn.active {
  color: var(--warning-color);
  transform: scale(1.1);
}

/* 收藏列表样式 */
.favorites-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.favorites-list .result-card {
  margin-bottom: 1rem;
}

.empty-favorites {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted-color);
}

/* 收藏入口按钮 */
.favorites-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s;
}

.favorites-link:hover {
  opacity: 0.8;
}

/* 收藏功能提示和刷新提示 */
.refresh-notice {
  background-color: rgba(72, 187, 120, 0.1);
  color: var(--success-color);
  padding: 0.5rem;
  border-radius: 4px;
  text-align: center;
  margin-bottom: 1rem;
  font-weight: 500;
  transition: opacity 0.5s ease;
}

/* 结果卡片调整，让按钮和内容对齐 */
.result-card {
  position: relative;
  display: flex;
  align-items: stretch;
}

.card-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.75rem;
  padding-left: 1rem;
}

/* 使按钮垂直居中 */
.favorite-btn {
  margin-bottom: 0.5rem;
}

/* 游戏卡片图片容器 */
.image-container {
  position: relative;
  width: 120px;
  flex-shrink: 0;
}

/* 收藏按钮 - 悬浮在图片上 */
.favorite-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 255, 255, 0.7);
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.25rem;
  color: #ccc;
  padding: 0;
  line-height: 1;
  transition: all 0.2s ease;
  z-index: 2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.favorite-btn:hover {
  transform: scale(1.1);
  color: var(--warning-color);
  background-color: rgba(255, 255, 255, 0.9);
}

.favorite-btn.active {
  color: var(--warning-color);
}

[data-theme="dark"] .favorite-btn {
  background-color: rgba(0, 0, 0, 0.5);
  color: #aaa;
}

[data-theme="dark"] .favorite-btn:hover,
[data-theme="dark"] .favorite-btn.active {
  color: var(--warning-color);
  background-color: rgba(0, 0, 0, 0.7);
}

/* 刷新按钮 */
.refresh-btn {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted-color);
  transition: color 0.2s, transform 0.2s;
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
  margin-right: 5px;
}

.refresh-btn:hover {
  color: var(--primary-color);
  background-color: rgba(0, 0, 0, 0.05);
}

.refresh-btn svg {
  width: 18px;
  height: 18px;
}

/* 修改结果卡片布局 */
.result-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: var(--card-radius);
  margin-bottom: 1rem;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.result-card-info {
  flex-grow: 1;
  min-width: 0;
}

/* 卖家按钮放在卡片最右侧 */
.sellers-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 14px;
  white-space: nowrap;
  margin-left: auto;
  flex-shrink: 0;
}

.sellers-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
}

/* 肥皂盒页面样式 */
.sort-card {
  padding: 1rem;
}

.sort-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: space-between;
}

/* 排序按钮组样式 */
.sort-btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.sort-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  background-color: #f0f0f0;
  border: none;
  color: #333;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

[data-theme="dark"] .sort-btn {
  background-color: #333;
  color: #f0f0f0;
}

.sort-btn:hover {
  background-color: #4a6cf7;
  color: white;
}

.sort-btn.active {
  background-color: #4a6cf7;
  color: white;
}

/* 页码控制 */
.page-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

.page-input {
  width: 60px;
  text-align: center;
  padding: 0.5rem;
  border: 1px solid var(--card-border-color);
  border-radius: 0.25rem;
  background-color: var(--card-background-color);
  color: var(--text-color);
}

/* 更新肥皂盒卡片样式 */
.chest-item {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: var(--card-radius);
  margin-bottom: 1rem;
  transition: all 0.2s ease-in-out;
  position: relative;
  cursor: pointer;
}

.chest-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

/* 肥皂盒标题区域 */
.chest-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--card-border-color);
}

.chest-title {
  display: flex;
  align-items: baseline;
  gap: 0.75rem;
  flex: 1;
}

.chest-name {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

/* 概率徽章 */
.chest-probability-badge {
  background-color: var(--warning-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 抽取进度 */
.chest-progress {
  display: flex;
  align-items: center;
  gap: 0;
  font-weight: 600;
}

.chest-remain {
  color: var(--primary-color);
}

.chest-total {
  color: var(--text-muted-color);
}

.chest-id-label {
  font-size: 0.8rem;
  color: var(--text-muted-color);
  font-weight: normal;
}

.chest-badge {
  align-self: flex-start;
  padding: 0.25rem 0.75rem;
  color: white;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

/* 肥皂盒详情信息 */
.chest-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  width: 100%;
}

.chest-detail {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.85rem;
  color: var(--text-muted-color);
  margin-bottom: 0;
  flex-shrink: 0;
}

.chest-id, .chest-price, .chest-seller {
  font-weight: 600;
  font-size: 1rem;
}

.chest-price {
  color: var(--success-color);
}

.chest-seller {
  color: var(--primary-color);
}

/* 分页控制样式优化 */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 2rem;
}

.pagination-btn {
  padding: 0.5rem 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 90px;
  font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

#page-indicator {
  font-weight: 500;
  padding: 0.5rem 1rem;
  background-color: rgba(74, 108, 247, 0.1);
  border-radius: 0.25rem;
  min-width: 120px;
  text-align: center;
}

/* 收藏列表排序功能 */
.favorites-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.favorites-item {
  cursor: grab;
  margin-bottom: 1rem;
}

.favorites-item:active {
  cursor: grabbing;
}

.favorites-list .result-card {
  margin-bottom: 0;
  transition: background-color 0.2s ease;
}

.favorites-list .result-card.sortable-ghost {
  opacity: 0.5;
  background-color: var(--primary-focus);
}

.favorites-list .result-card.sortable-drag {
  opacity: 0.8;
}

/* 肥皂盒详情模态框样式 */
.chest-detail-content {
    width: 90%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 10px;
    border: none;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    margin: 5% auto;
}

.modal-header {
    background-color: var(--primary-color);
    color: white;
    padding: 0.6rem 0.8rem;
    border-bottom: none;
    border-radius: 10px 10px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chest-header-info {
  display: flex;
  flex-direction: column;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: white;
}

.chest-subtitle {
  display: flex;
  gap: 15px;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.95);
  margin-top: 5px;
}

.chest-id-label, .chest-seller-label {
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

#detail-chest-id, #detail-seller {
  font-weight: 600;
  color: #ffffff; /* 使用白色而不是黄色 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.price-tag {
  background-color: #ff5722; /* 亮橙色背景 */
  color: white; /* 白色文字 */
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.modal-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-refresh-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.detail-refresh-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  transform: rotate(30deg);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
}

.close-btn:hover {
  color: white;
}

.detail-refresh-btn.rotating {
  animation: rotate-refresh 1s linear;
}

@keyframes rotate-refresh {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.modal-body {
  padding: 0;
  background-color: #f5f7fb;
}

/* 肥皂盒基本信息样式 */
.detail-section {
  margin-bottom: 0;
  border: none;
  border-radius: 0;
  background-color: white;
}

/* 顶部进度和概率区域 */
.chest-summary {
  padding: 15px;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eaedf2;
}

.chest-progress-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chest-draw-progress {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--primary-color);
}

.chest-probability {
  font-size: 0.9rem;
  color: var(--text-muted-color);
}

.probability-value {
  color: var(--warning-color);
  font-weight: 600;
}

.chest-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chest-actions .action-btn {
  margin-left: 0 !important;
}

.action-btn {
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chest-draw-btn {
  background-color: #3490dc;
  color: white;
}

.chest-draw-btn:hover {
  background-color: #2779bd;
}

.price-tag {
  display: inline-block;
  font-size: 0.85rem;
  opacity: 0.9;
  margin-left: 5px;
}

/* 游戏列表部分 */
.games-section {
  margin-top: 0;
  padding: 0 8px 8px 8px;
  background-color: #f5f7fb;
}

/* 游戏池样式 */
.game-pool {
  margin-bottom: 10px;
  background-color: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pool-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eaedf2;
}

.pool-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.pool-level {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  font-size: 0.7rem;
  font-weight: 600;
  color: white;
}

.pool-probability {
  font-size: 0.8rem;
  color: #ff8c00;
  font-weight: 600;
  background-color: rgba(255, 167, 38, 0.15);
  padding: 3px 8px;
  border-radius: 10px;
}

.pool-games {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  width: 100%;
  margin-bottom: 20px;
}

/* 优化游戏卡片样式 */
.game-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  padding-bottom: 5px;
}

.game-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 游戏图片容器样式优化 */
.game-card .game-image {
  position: relative;
  width: 100%;
  height: 68px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-card .game-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.game-card:hover .game-image img {
  transform: scale(1.05);
}

/* 游戏标题样式优化 */
.game-card .game-title {
  font-size: 12px;
  line-height: 1.2;
  margin: 5px 5px 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  color: #333333;
}

/* 游戏信息行样式优化 */
.game-card .game-info-row {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  color: #555555;
  margin: 0 5px 3px;
  padding: 2px 4px;
  border-radius: 3px;
}

/* 库存信息样式优化 - 更显眼 */
.stock-info {
  display: flex;
  align-items: center;
  gap: 3px;
  color: #555555;
}

.stock-info span.stock-number {
  background-color: #f0f0f0;
  color: #000;
  font-weight: 600;
  padding: 1px 4px;
  border-radius: 3px;
  min-width: 20px;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 概率信息样式 */
.probability-info {
  color: #555555;
  font-weight: 500;
}

/* 库存变动指示器样式 */
.stock-decrease {
  color: #ff5252 !important;
  font-weight: bold !important;
  margin-left: 2px;
  animation: pulse 1s ease-in-out;
  background-color: rgba(255, 82, 82, 0.2);
  padding: 0 2px;
  border-radius: 2px;
}

.stock-increase {
  color: #4caf50 !important;
  font-weight: bold !important;
  margin-left: 2px;
  animation: pulse 1s ease-in-out;
  background-color: rgba(76, 175, 80, 0.2);
  padding: 0 2px;
  border-radius: 2px;
}

/* 价格标签样式优化 */
.game-card .price-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: #007bff; /* 蓝色背景 */
  color: white;
  padding: 2px 6px;
  border-radius: 0 0 0 6px;
  font-weight: bold;
  font-size: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  z-index: 2;
}

/* 保持其他价格标签样式不变 */
.price-tag:not(.game-card .price-tag) {
  background-color: #ff5722; /* 保持其他价格标签为橙色 */
  color: white;
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* 池子标题样式优化 */
.pool-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  margin-bottom: 10px;
  font-weight: 600;
  color: white;
}

.pool-probability {
  font-size: 0.9em;
  color: #ffffff;
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 2px 8px;
  border-radius: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 传说池样式 */
.legendary-pool .pool-title {
  background-color: #ff8c00;
  box-shadow: 0 2px 4px rgba(255, 140, 0, 0.3);
}

/* 史诗池样式 */
.epic-pool .pool-title {
  background-color: #9370db;
  box-shadow: 0 2px 4px rgba(147, 112, 219, 0.3);
}

/* 稀有池样式 */
.rare-pool .pool-title {
  background-color: #4682b4;
  box-shadow: 0 2px 4px rgba(70, 130, 180, 0.3);
}

/* 普通池样式 */
.common-pool .pool-title {
  background-color: #2196f3;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

/* 高价值游戏样式 - 修复遮挡价格的问题 */
.game-card.high-value-game {
  position: relative;
  border: 2px solid #ff5722 !important;
  box-shadow: 0 0 10px rgba(255, 87, 34, 0.5) !important;
  background-color: rgba(255, 243, 224, 0.7);
  transform: scale(1.03);
  z-index: 2;
}

.game-card.high-value-game::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background-color: #ff5722;
  z-index: 3;
}

.game-card.high-value-game::after {
  content: "高价值";
  position: absolute;
  top: 6px;
  left: 0; /* 改为左侧显示，避免遮挡价格 */
  background-color: #ff5722;
  color: white;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 0 0 4px 0; /* 调整圆角 */
  z-index: 3;
}

.game-card.high-value-game:hover {
  transform: translateY(-4px) scale(1.03);
  box-shadow: 0 6px 15px rgba(255, 87, 34, 0.6) !important;
}

/* 低价值游戏样式 - 修复判断逻辑 */
.game-card.low-value-game {
  position: relative;
  border: 2px solid #607d8b !important;
  box-shadow: 0 0 10px rgba(96, 125, 139, 0.5) !important;
  background-color: rgba(236, 239, 241, 0.7);
  transform: scale(1.03);
  z-index: 2;
}

.game-card.low-value-game::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background-color: #607d8b;
  z-index: 3;
}

.game-card.low-value-game::after {
  content: "低价值";
  position: absolute;
  top: 6px;
  left: 0; /* 改为左侧显示，避免遮挡价格 */
  background-color: #607d8b;
  color: white;
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 0 0 4px 0; /* 调整圆角 */
  z-index: 3;
}

.game-card.low-value-game:hover {
  transform: translateY(-4px) scale(1.03);
  box-shadow: 0 6px 15px rgba(96, 125, 139, 0.6) !important;
}

/* 库存数量为0的游戏卡片 */
.game-card.out-of-stock {
  opacity: 0.6;
}

/* 确保游戏池容器在小屏幕上也能正确显示 */
@media (max-width: 768px) {
  .pool-games {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}

@media (max-width: 480px) {
  .pool-games {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  }
  
  .game-card .game-image {
    height: 50px;
  }
}

/* 错误信息样式 */
.error-message {
  padding: 1rem;
  border-radius: var(--card-radius);
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
  text-align: center;
  margin: 15px;
}

.detail-refresh-btn svg {
  width: 20px;
  height: 20px;
  color: white;
}

/* 缓存价格样式 - 橙色显示 */
.cached-price {
    color: #ff8c00 !important;
    font-weight: 500;
}

/* 高价值游戏突出显示 */
.high-value-game {
    border: 2px solid #ff5722 !important;
    box-shadow: 0 0 8px rgba(255, 87, 34, 0.3) !important;
}



.high-value-game:hover {
    transform: translateY(-2px) scale(1.02) !important;
    box-shadow: 0 4px 12px rgba(255, 87, 34, 0.4) !important;
    border-color: #ff5722 !important;
}

/* 低价值游戏突出显示 */
.low-value-game {
    border: 2px solid #000000 !important;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.5) !important;
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.02);
    z-index: 1;
}

.low-value-game:hover {
    transform: translateY(-2px) scale(1.02) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6) !important;
    border-color: #000000 !important;
}

/* 计算期望按钮 */
.calculate-ev-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.calculate-ev-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    transform: scale(1.1);
}

.calculate-ev-btn.calculating {
    animation: pulse 1.5s infinite;
    color: #ffeb3b;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

/* 期望计算结果区域 */
.ev-result {
    background-color: white;
    margin: 0;
    padding: 15px;
    border-bottom: 1px solid #eaedf2;
}

.ev-result-header {
    margin-bottom: 10px;
}

.ev-result-header h4 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.ev-result-content {
    font-size: 0.95rem;
    line-height: 1.5;
}

.ev-multiplier {
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 8px;
}

.ev-detail {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.ev-item {
    display: flex;
    flex-direction: column;
}

.ev-item-label {
    font-size: 0.85rem;
    color: var(--text-muted-color);
}

.ev-item-value {
    font-weight: 600;
}

.ev-good {
    color: var(--success-color);
}

.ev-bad {
  color: var(--danger-color);
}

.ev-neutral {
    color: var(--warning-color);
}

/* 设置部分样式 */
.settings-section {
    margin-bottom: 2rem;
}

.settings-section h3 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.setting-group {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: var(--background-color);
    border-radius: var(--card-radius);
    border: 1px solid var(--border-color);
}

.setting-group h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 表单控件样式 */
.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--body-color);
    background-color: var(--card-background-color);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: var(--body-color);
    background-color: var(--card-background-color);
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 下单按钮样式 */
.order-btn {
    padding: 6px 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.order-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.order-btn.disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 更新卖家操作区域样式 */
.seller-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

/* 账号开关样式 */
.account-switch-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.account-switch {
    display: flex;
    align-items: center;
    gap: 10px;
}

.account-switch-checkbox {
    height: 0;
    width: 0;
    visibility: hidden;
    position: absolute;
}

.account-switch-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    width: 60px;
    height: 30px;
    background: #ccc;
    border-radius: 100px;
    position: relative;
    transition: background-color 0.2s;
}

.account-switch-label .account-switch-inner {
    content: "";
    position: absolute;
    top: 4px;
    left: 4px;
    width: 22px;
    height: 22px;
    border-radius: 100%;
    transition: 0.2s;
    background: #fff;
    box-shadow: 0 0 2px 0 rgba(10, 10, 10, 0.29);
}

.account-switch-checkbox:checked + .account-switch-label {
    background: var(--primary-color);
}

.account-switch-checkbox:checked + .account-switch-label .account-switch-inner {
    left: calc(100% - 4px);
    transform: translateX(-100%);
}

/* 兑换按钮样式 */
.redeem-btn {
    padding: 0.35rem 0.75rem;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
}

.redeem-btn.disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

.delete-btn {
    padding: 0.35rem 0.75rem;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-left: 5px;
}

.delete-btn:hover {
    background-color: #c82333;
}

.redeem-btn:hover {
    background-color: #218838;
}

/* 订单表格样式 */
.orders-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.orders-table th {
    background-color: var(--background-color);
    color: var(--text-color);
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--card-border-color);
    font-weight: 600;
}

.orders-table td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--card-border-color);
    color: var(--text-color);
}

.orders-table tr:hover {
    background-color: rgba(0,0,0,0.02);
}

[data-theme="dark"] .orders-table tr:hover {
    background-color: rgba(255,255,255,0.05);
}

.order-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-weight: 500;
    font-size: 0.85rem;
    text-align: center;
}

.order-status.paid {
    background-color: rgba(40, 167, 69, 0.15);
    color: var(--success-color);
}

.order-status.pending {
    background-color: rgba(255, 193, 7, 0.15);
    color: var(--warning-color);
}

.order-status.expired {
    background-color: rgba(108, 117, 125, 0.15);
    color: var(--secondary-color);
}

.empty-notice {
    text-align: center;
    padding: 2rem;
    color: var(--text-muted-color);
}

/* 遮罩层动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

#chest-loading-overlay, #logs-loading-overlay {
  animation: fadeIn 0.3s ease-in-out;
}

.spin {
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 改进刷新按钮动画 */
.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 暗色模式下的遮罩层 */
[data-theme="dark"] #chest-loading-overlay, 
[data-theme="dark"] #logs-loading-overlay {
  background-color: rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .spin {
  border: 3px solid rgba(0, 0, 0, 0.3);
  border-top: 3px solid var(--primary);
}

/* 按钮响应式设计 */
.chest-actions {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.icon-action-btn {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.icon-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 在小屏幕上优化显示 */
@media (max-width: 576px) {
    .chest-draw-btn {
        flex: 1;
        min-width: 90px;
    }
    
    .chest-actions {
        gap: 8px;
    }
    
    .chest-summary {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    /* 确保图标按钮显示在同一行 */
    .icon-action-btn {
        width: 36px;
        height: 36px;
    }
} 

/* ----- MONITOR PAGE STYLES ----- */
.log-container {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    background-color: var(--surface-color);
    height: 300px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 0.9rem;
}

.control-panel {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.small-input {
    width: 80px;
}

.inline-form {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-group {
    display: flex;
    align-items: center;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 8px;
}

.custom-checkbox {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.section-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 10px;
    color: var(--heading-color);
}

/* 肥皂盒搜索框样式 */
.chest-search {
    display: flex;
    align-items: center;
    margin-left: 16px;
    margin-right: 16px;
    position: relative;
}

.chest-search-input {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    width: 200px;
    transition: all 0.2s ease;
}

.chest-search-input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);
    outline: none;
}

.chest-search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted-color);
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
}

.chest-search-btn:hover {
    color: var(--primary);
}






















