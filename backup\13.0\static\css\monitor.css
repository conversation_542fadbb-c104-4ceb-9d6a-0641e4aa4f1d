/* 监控页面特定样式 */

/* 防止水平溢出 */
body {
    overflow-x: hidden;
}

/* 确保所有容器不会导致水平溢出 */
.container, .card, .card-body {
    max-width: 100%;
    overflow-x: hidden;
}

/* 确保表格可横向滚动而不影响整体页面 */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* 监控控制面板 */
.monitor-control-panel {
    background-color: rgba(var(--primary-rgb), 0.03);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* CDK监控相关样式 */
.cdk-monitor-controls {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.games-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-start;
}

/* 游戏卡片容器确保不会溢出 */
.games-container {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(135px, 1fr));
    gap: 8px;
    padding: 4px 0;
}

.game-monitor-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06), 0 1px 3px rgba(0,0,0,0.04);
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0,0,0,0.06);
    position: relative;
    cursor: pointer;
    overflow: visible;
    /* backdrop-filter: blur(10px); 移除以提升性能 */
    min-height: 150px;
}

.game-monitor-card:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    /* transform: translateY(-4px) scale(1.02); 简化悬停效果 */
    border-color: rgba(var(--primary-rgb), 0.3);
}

.game-monitor-card.has-target {
    border: 2px solid rgba(var(--primary-rgb), 0.6);
    background: linear-gradient(135deg, #ffffff 0%, rgba(var(--primary-rgb), 0.02) 100%);
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.15), 0 2px 4px rgba(0,0,0,0.04);
}

.game-monitor-card.price-reached {
    border: 2px solid rgba(var(--success-rgb), 0.8);
    background: linear-gradient(135deg, #ffffff 0%, rgba(var(--success-rgb), 0.08) 100%);
    box-shadow: 0 4px 12px rgba(var(--success-rgb), 0.2), 0 2px 4px rgba(0,0,0,0.04);
    /* animation: pulse-success 2s infinite; 移除动画提升性能 */
}

@keyframes pulse-success {
    0%, 100% { 
        box-shadow: 0 4px 12px rgba(var(--success-rgb), 0.2), 0 2px 4px rgba(0,0,0,0.04);
    }
    50% { 
        box-shadow: 0 6px 20px rgba(var(--success-rgb), 0.3), 0 4px 8px rgba(0,0,0,0.06);
    }
}

.target-price-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, var(--primary-color) 0%, rgba(var(--primary-rgb), 0.8) 100%);
    color: white;
    border-radius: 12px;
    padding: 4px 8px;
    font-size: 0.7rem;
    font-weight: 700;
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.4), 0 2px 4px rgba(0,0,0,0.1);
    border: 2px solid white;
    z-index: 10;
    transform: rotate(-5deg);
    transition: all 0.3s ease;
}

.target-price-badge:hover {
    transform: rotate(0deg) scale(1.1);
}

.target-price-badge.reached {
    background: linear-gradient(135deg, var(--success-color) 0%, rgba(var(--success-rgb), 0.8) 100%);
    box-shadow: 0 4px 12px rgba(var(--success-rgb), 0.4), 0 2px 4px rgba(0,0,0,0.1);
    /* animation: bounce-badge 1s ease-in-out infinite alternate; 移除动画提升性能 */
}

@keyframes bounce-badge {
    0% { transform: rotate(-5deg) scale(1); }
    100% { transform: rotate(5deg) scale(1.05); }
}

.game-avatar-lg {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: cover;
    margin-bottom: 6px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.12);
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.8);
    position: relative;
    overflow: hidden;
}

.game-avatar-lg:hover {
    /* transform: scale(1.05); 简化悬停效果 */
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

/* 移除头像光泽效果以提升性能
.game-avatar-lg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.game-monitor-card:hover .game-avatar-lg::before {
    opacity: 1;
}
*/

.game-info-block {
    width: 100%;
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.game-title {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 4px;
    color: #1a202c;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.2;
    max-height: 2.4em;
    transition: color 0.3s ease;
}

.game-monitor-card:hover .game-title {
    color: var(--primary-color);
}

/* 价格信息样式 */
.game-price-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
    margin: 6px 0;
    align-items: center;
}

.current-price, .target-price {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 700;
    text-align: center;
    min-width: 70px;
    height: 22px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.current-price {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    box-shadow: 0 2px 6px rgba(34, 197, 94, 0.25);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

/* .current-price:hover 悬停效果已移除以提升性能 */

.target-price {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 2px 6px rgba(245, 158, 11, 0.25);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

/* .target-price:hover 悬停效果已移除以提升性能 */

/* 价格标签的光泽效果 - 移除以提升性能
.current-price::before, .target-price::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.current-price:hover::before, .target-price:hover::before {
    left: 100%;
}
*/

.game-price-info .text-muted {
    color: #6b7280;
    font-size: 0.75rem;
    font-style: italic;
    padding: 3px 8px;
    background: linear-gradient(135deg, rgba(0,0,0,0.03) 0%, rgba(0,0,0,0.06) 100%);
    border-radius: 12px;
    border: 1px solid rgba(0,0,0,0.08);
    min-height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-actions-row {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-top: 8px;
    position: relative;
    z-index: 10;
}

.remove-game, .set-target-price {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    /* backdrop-filter: blur(10px); 移除以提升性能 */
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.remove-game {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
    color: white;
}

.remove-game:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    /* transform: scale(1.1); 简化悬停效果 */
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
}

.set-target-price {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(37, 99, 235, 0.9) 100%);
    color: white;
}

.set-target-price:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    /* transform: scale(1.1); 简化悬停效果 */
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.remove-game svg, .set-target-price svg {
    width: 12px;
    height: 12px;
    stroke-width: 2.5px;
}

/* 按钮激活效果 */
.remove-game:active, .set-target-price:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

.icon-trash {
    width: 18px;
    height: 18px;
}

/* 游戏搜索结果 */
.game-search-results {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.game-search-results .list-group-item {
    padding: 10px 15px;
    border-left: none;
    border-right: none;
}

.game-search-results .list-group-item:first-child {
    border-top: none;
}

.game-search-results .list-group-item:last-child {
    border-bottom: none;
}

/* CDK监控日志 */
#cdk-monitor-logs {
    height: 200px;
    overflow-y: auto;
}

/* 监控记录标题美化 */
.border-left {
    border-left: 4px solid var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.pl-2 {
    padding-left: 0.5rem !important;
}

/* 按钮样式 */
#toggleMonitor {
    transition: all 0.3s ease;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    min-height: 48px;
}

#toggleMonitor.monitoring {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

#toggleMonitor.monitoring:hover {
    background-color: rgba(var(--danger-rgb), 0.9);
}

/* SVG图标样式 */
.icon-play, .icon-stop, .icon-trash {
    display: inline-block;
    vertical-align: middle;
    margin-right: 6px;
}

.mr-1 {
    margin-right: 4px;
}

.mr-2 {
    margin-right: 8px;
}

.feather {
    display: inline-block;
    vertical-align: middle;
}

.feather.mr-1 {
    margin-right: 4px;
}

.feather.mr-2 {
    margin-right: 8px;
}

#toggleMonitor .feather {
    stroke-width: 2.5px;
}

#toggleMonitor.monitoring .feather {
    stroke: white;
}

#clearLogs .feather {
    stroke-width: 2px;
}

/* 监控间隔输入框 */
.interval-controls {
    display: flex;
    align-items: center;
}

.interval-controls label {
    min-width: 85px;
    color: var(--text-color);
}

.interval-controls .input-group {
    flex: 1;
}

.interval-controls .input-group-text {
    background-color: var(--primary-color);
    color: #fff;
    border: none;
}

/* 开关样式 */
.switch-control {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin: 0;
}

.switch-control input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--success-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--success-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* 监控模式面板 */
.monitor-modes-panel {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
    margin-top: 25px;
}

.section-title {
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.section-title:before {
    content: "";
    display: inline-block;
    width: 4px;
    height: 18px;
    background-color: var(--primary-color);
    margin-right: 10px;
    border-radius: 2px;
}

/* 监控模式选项 */
.monitor-mode-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: flex-start;
    max-width: 100%;
    overflow: hidden;
}

.mode-option {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 10px;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    flex: 1 1 calc(25% - 15px);
    min-width: 200px;
    max-width: 100%;
    box-sizing: border-box;
}

.mode-option:hover {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.06);
}

.mode-option input {
    position: absolute;
    opacity: 0;
}

.mode-check {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(var(--primary-rgb), 0.5);
    border-radius: 4px;
    margin-right: 10px;
}

.mode-option input:checked + .mode-check:before {
    content: "\2713";
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.mode-label {
    font-weight: 600;
    color: var(--text-color);
    margin-right: 10px;
}

.mode-description {
    flex-basis: 100%;
    margin-top: 8px;
    margin-left: 30px;
    font-size: 0.85rem;
    color: var(--text-muted-color);
}

/* 日志面板 */
.monitor-logs-panel {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
    margin-top: 25px;
}

.logs-header {
    margin-bottom: 15px;
}

.monitor-logs-container {
    height: 300px;
    overflow-y: auto;
    padding: 15px;
    background-color: var(--log-bg-color);
    border-radius: 8px;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

.monitor-logs-container::-webkit-scrollbar {
    width: 8px;
}

.monitor-logs-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.monitor-logs-container::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

.monitor-logs-container div {
    padding: 6px 0;
    border-bottom: 1px dashed rgba(var(--text-muted-rgb), 0.1);
}

.monitor-logs-container div:last-child {
    border-bottom: none;
}

/* 日志样式 */
.log-time {
    color: var(--text-muted-color);
    margin-right: 8px;
    font-weight: 500;
}

.log-normal {
    color: var(--text-color);
}

.log-error {
    color: var(--danger-color) !important;
}

.log-success {
    color: var(--success-color) !important;
}

.log-warning {
    color: var(--warning-color) !important;
}

/* 游戏分组标签增强动画 */
.game-group-badge small {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.game-group-badge small::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.game-group-badge small:hover::before {
    left: 100%;
}

/* 卡片加载动画 */
@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* .game-monitor-card 加载动画已移除以提升性能 */

.game-monitor-card:nth-child(even) {
    animation-delay: 0.1s;
}

.game-monitor-card:nth-child(3n) {
    animation-delay: 0.2s;
}

/* 价格变动动画 */
@keyframes priceUpdate {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
    100% { transform: scale(1); }
}

/* .current-price.updated 价格更新动画已移除以提升性能 */

/* 响应式调整 */
@media (max-width: 1200px) {
    .games-container {
        grid-template-columns: repeat(auto-fill, minmax(125px, 1fr));
        gap: 6px;
    }
}

@media (max-width: 768px) {
    .games-container {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 6px;
    }
    
    .game-monitor-card {
        min-height: 140px;
        padding: 6px;
    }
    
    .game-avatar-lg {
        width: 42px;
        height: 42px;
    }
    
    .monitor-control-panel {
        padding: 15px;
    }
    
    .monitor-mode-options {
        grid-template-columns: 1fr;
    }
    
    .mode-option {
        padding: 10px;
    }
    
    .monitor-logs-container {
        height: 250px;
    }
}

@media (max-width: 576px) {
    .games-container {
        grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
        gap: 4px;
    }
    
    .game-monitor-card {
        min-height: 130px;
        padding: 6px;
    }
    
    .game-avatar-lg {
        width: 38px;
        height: 38px;
    }
    
    .game-title {
        font-size: 0.85rem;
    }
    
    .current-price, .target-price {
        font-size: 0.75rem;
        padding: 3px 6px;
        min-width: 70px;
    }
    
    .remove-game, .set-target-price {
        width: 28px;
        height: 28px;
    }
    
    .remove-game svg, .set-target-price svg {
        width: 14px;
        height: 14px;
    }
}

/* 命中记录面板 */
.monitor-hits-panel {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
    margin-top: 25px;
}

.hits-header {
    margin-bottom: 15px;
}

.monitor-hits-container {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.monitor-hits-container table {
    margin-bottom: 0;
}

.monitor-hits-container th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    border-top: none;
}

.monitor-hits-container td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.chest-id-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.chest-id-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.monitor-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

.monitor-type-expected {
    background-color: rgba(0, 123, 255, 0.1);
    color: #0062cc;
}

.monitor-type-probability {
    background-color: rgba(40, 167, 69, 0.1);
    color: #1e7e34;
}

.monitor-type-wrongPrice {
    background-color: rgba(220, 53, 69, 0.1);
    color: #bd2130;
}

.monitor-type-specific {
    background-color: rgba(108, 117, 125, 0.1);
    color: #5a6268;
}

/* 特定监控面板 */
.monitor-specific-panel {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
    margin-top: 25px;
}

.specific-header {
    margin-bottom: 15px;
}

.specific-chests-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.favorite-chests-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.favorite-chest-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
    position: relative;
}

.favorite-chest-item:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    /* transform: translateY(-2px); 简化悬停效果 */
}

.favorite-chest-name {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 8px;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.favorite-chest-id {
    font-size: 0.8rem;
    color: var(--text-muted-color);
    margin-bottom: 10px;
}

.favorite-chest-price {
    font-weight: 500;
    color: var(--primary-color);
}

.favorite-chest-actions {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    gap: 5px;
}

.favorite-action-btn {
    background: none;
    border: none;
    color: var(--text-muted-color);
    padding: 2px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.favorite-action-btn:hover {
    color: var(--primary-color);
}

.favorite-action-btn.remove:hover {
    color: var(--danger-color);
}

/* 收藏管理模态框 */
.favorites-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
    padding: 20px;
}

.favorites-modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin: 30px auto;
}

/* 确保模态框在小屏幕上能正常展示 */
@media (max-width: 576px) {
    .favorites-modal-content {
        width: 95%;
        margin: 10px auto;
        max-height: 85vh;
    }
    
    /* 导出文本区域样式改进 */
    #export-content {
        max-height: 50vh;
        font-size: 12px;
    }
}

/* 导出文本区域样式 */
#export-content {
    font-family: monospace;
    font-size: 13px;
    line-height: 1.4;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    resize: none;
    white-space: pre;
}

/* 紧凑型表格 */
.table-compact th,
.table-compact td {
    padding: 0.4rem 0.5rem;
    font-size: 0.85rem;
}

.table-compact .btn-sm {
    padding: 0.15rem 0.4rem;
    font-size: 0.75rem;
} 

/* 修复间隔控制区域的响应式问题 */
.interval-controls {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

@media (max-width: 576px) {
    .interval-controls {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .interval-controls label {
        margin-bottom: 5px;
        width: 100%;
    }
    
    .input-group {
        width: 100%;
    }
    
    /* 针对小屏幕设备改善按钮间距 */
    .card-header {
        flex-wrap: wrap;
    }
    
    .card-header .btn {
        margin-top: 5px;
    }
    
    /* 优化监控模式选项 */
    .mode-option {
        flex: 1 1 100%;
    }
    
    /* 减少某些元素内边距 */
    .card-body {
        padding: 12px;
    }
} 

/* 模态框头部和底部样式 */
.favorites-modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.favorites-modal-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.favorites-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted-color);
}

.favorites-modal-body {
    padding: 20px;
}

.favorites-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    text-align: right;
}

@media (max-width: 767px) {
    .favorite-chests-list {
        grid-template-columns: 1fr;
    }
} 