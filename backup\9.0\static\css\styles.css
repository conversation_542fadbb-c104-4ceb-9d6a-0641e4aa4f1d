/* 添加肥皂盒记录表格样式 */
.section-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    color: var(--text-color);
}

.logs-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.logs-table th, 
.logs-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color-light);
}

.logs-table th {
    font-weight: 600;
    background-color: var(--bg-secondary);
    color: var(--text-color);
}

.logs-table tr:hover {
    background-color: var(--hover-color);
}

.logs-table .no-data,
.logs-table .error-message {
    text-align: center;
    padding: 1.5rem;
    color: var(--text-secondary);
    font-style: italic;
}

.logs-table .error-message {
    color: var(--error-color);
}

.with-tooltip {
    cursor: help;
    text-decoration: underline dotted;
    text-decoration-color: var(--text-secondary);
}

.logs-pagination {
    margin-top: 1rem;
}

/* 肥皂盒详情页面样式 */
.chest-detail-header {
    margin-bottom: 1.5rem;
}

.chest-detail-header h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.chest-detail-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 0.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.meta-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.meta-value {
    color: var(--text-color);
}

/* 广告风格的肥皂盒记录展示 */
.advert-style-card {
    background: linear-gradient(to right, #2c85eb, #5babf6);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.advert-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.advert-header h2 {
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
}

.advert-style-card .logs-controls {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 8px 12px;
    margin-bottom: 15px;
}

.advert-style-card .log-sort-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
}

.advert-style-card .log-sort-btn.active {
    background-color: rgba(255, 255, 255, 0.4);
    font-weight: 500;
}

.advert-style-card .page-control {
    color: white;
}

.advert-style-card .page-input {
    background-color: rgba(255, 255, 255, 0.3);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.logs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    padding: 5px;
}

.log-card {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

.log-card-chest {
    font-weight: 500;
    margin-bottom: 5px;
    color: #0063cc;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.log-card-game {
    font-size: 0.95rem;
    color: #333;
    margin-bottom: 8px;
}

.log-card-time {
    font-size: 0.8rem;
    color: #777;
    margin-top: auto;
}

.advert-style-card .pagination-controls {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 10px;
    margin-top: 15px;
}

.advert-style-card .pagination-btn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 5px 12px;
    border-radius: 4px;
}

.advert-style-card .pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.advert-style-card #logs-page-indicator {
    color: white;
}

/* 高铁信息栏样式 */
.info-ticker-container {
    background-color: #0a224e;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 15px 0;
    overflow: hidden;
    position: relative;
    height: 120px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.info-ticker {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.ticker-item {
    padding: 8px 0;
    border-bottom: 1px dashed rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
}

.ticker-item:last-child {
    border-bottom: none;
}

.ticker-chest {
    font-weight: 500;
    color: #3ebbff;
    margin-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px;
}

.ticker-game {
    flex-grow: 1;
    margin-right: 10px;
}

.ticker-time {
    color: #aad4ff;
    white-space: nowrap;
}

/* 添加页面标题样式 */
.info-ticker-header {
    background-color: #002a6a;
    color: white;
    padding: 6px 12px;
    border-radius: 4px 4px 0 0;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin: -8px -12px 8px -12px;
}

.info-ticker-header-title {
    display: flex;
    align-items: center;
}

.info-ticker-header-title svg {
    margin-right: 8px;
    fill: #5babf6;
}

.info-ticker-header-time {
    font-size: 0.85rem;
    color: #aad4ff;
}

/* 添加滚动动画定义 */
@keyframes marquee {
    0% { transform: translateX(100%); }  /* 从视窗右侧开始 */
    100% { transform: translateX(-150%); }  /* 完全移出视窗左侧 */
}

/* 自定义加载指示器样式 */
.loading-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
}

.loading-indicator .spin {
    width: 20px;
    height: 20px;
    border: 2px solid #fff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 确保marquee元素样式正确 */
.marquee-container {
    overflow: hidden !important;
    position: relative;
    z-index: 10;
}

#logs-marquee {
    display: inline-block;
    white-space: nowrap;
    animation: marquee 150s linear infinite;
    margin: 0;
    text-align: left;
}

/* 跑马灯项目悬停样式 */
.marquee-item {
    position: relative;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.marquee-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 详情框样式 */
.detail-box {
    position: absolute;
    bottom: -120px;
    left: 0;
    width: 300px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    padding: 15px;
    z-index: 1000;
    color: #333;
    text-align: left;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: opacity 0.3s, transform 0.3s, visibility 0.3s;
    pointer-events: none;
}

.marquee-item:hover .detail-box {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.detail-box h4 {
    margin: 0 0 10px;
    color: #2196f3;
    font-size: 16px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.detail-box-row {
    margin: 8px 0;
    display: flex;
}

.detail-box-label {
    font-weight: 500;
    width: 70px;
    color: #666;
}

.detail-box-value {
    flex: 1;
}

/* 改进的滚动信息栏 */
.marquee-container {
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px 0;
    margin: 15px 0;
    overflow: hidden;
    position: relative;
    white-space: nowrap;
    height: 30px;
    line-height: 30px;
}

.marquee-content {
    display: inline-block;
    white-space: nowrap;
    animation: marquee 30s linear infinite;
    padding-left: 100%;
    color: #333;
    font-size: 14px;
    height: 30px;
    line-height: 30px;
}

.marquee-item {
    display: inline-block;
    margin: 0 10px;
    font-size: 14px;
    border-right: none;
    background-color: #f8f9fa;
    padding: 0 10px;
    border-radius: 3px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.marquee-separator {
    display: inline-block;
    margin: 0 15px;
    color: #999;
    font-size: 18px;
    font-weight: bold;
}

.marquee-chest {
    font-weight: 500;
    color: #0063cc;
    margin-right: 12px;
    border-right: 1px solid #ddd;
    padding-right: 12px;
}

.marquee-game {
    color: #333;
    margin-right: 8px;
}

.marquee-time {
    color: #777;
} 