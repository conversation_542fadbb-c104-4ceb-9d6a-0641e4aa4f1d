from fastapi import APIRouter, HTTPException, Body, Depends
from typing import List
from pydantic import BaseModel, Field

from pywatcher.models.cookie_pool import MobileAccount
from pywatcher.crud.cookie_pool_manager import <PERSON><PERSON><PERSON>ool<PERSON>anager
from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.api.dependencies import get_steam_client_for_login, get_cookie_pool_manager

router = APIRouter(
    prefix="/accounts",
    tags=["Accounts"]
)

class SmsLoginRequest(BaseModel):
    phone: str
    code: str
    remark: str = "" # Add an optional remark field

@router.get("", response_model=List[MobileAccount])
async def get_accounts(
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """获取所有存储在 Mobile Cookie Pool 中的账户列表。"""
    return cookie_manager.get_all_mobile_accounts()

@router.post("/login", response_model=MobileAccount)
async def login_and_add_account(
    login_data: SmsLoginRequest,
    client: SteamPyClient = Depends(get_steam_client_for_login),
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """通过短信验证码登录，如果成功，则将账户添加到 Mobile Cookie Pool 中。"""
    response = await client.verify_login_code(login_data.phone, login_data.code)
    
    if not response or response.get("code") != 200:
        raise HTTPException(status_code=401, detail=response.get("message", "登录失败或验证码错误"))
        
    user_info = response.get("result", {})
    access_token = user_info.get("token")
    
    if not access_token:
        raise HTTPException(status_code=500, detail="登录成功，但未能获取到Token")

    # The password is not returned by the API, so we leave it empty or handle as needed.
    # We use the phone number as the 'account' identifier.
    new_account = MobileAccount(
        account=login_data.phone,
        password="", # Password is not available from SMS login
        access_token=access_token,
        token_expire_time=user_info.get("tokenExpireTime"),
        remark=login_data.remark or f"用户-{login_data.phone[-4:]}",
        status=1 # Assume status is active upon successful login
    )
    
    try:
        # The manager handles adding and saving to the JSON file.
        added_account = cookie_manager.add_mobile_account(new_account)
        cookie_manager.save_pool()  # 持久化更改
        return added_account
    except ValueError as e:
        # This will catch duplicates
        raise HTTPException(status_code=409, detail=str(e))


@router.delete("/{account_name}", status_code=204)
async def delete_account(
    account_name: str,
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """从 Mobile Cookie Pool 中删除一个账户。"""
    success = cookie_manager.remove_mobile_account(account_name)
    if not success:
        raise HTTPException(status_code=404, detail=f"未在移动账户池中找到账户: {account_name}")
    
    cookie_manager.save_pool()  # 持久化更改
    return None 