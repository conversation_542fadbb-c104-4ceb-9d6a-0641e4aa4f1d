import requests
import time
import uuid

# 配置信息
MOBILE = "16583372657"  # 替换为你的手机号
BASE_URL = "https://steampy.com/xboot"
HEADERS = {
    "User-Agent": "APPAPK",  # 使用APPAPK用户代理
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}

# 创建会话保持Cookies
session = requests.Session()
session.headers.update(HEADERS)

def init_captcha():
    """初始化验证码，获取captchaId"""
    url = f"{BASE_URL}/common/captcha/init"
    try:
        response = session.get(url)
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                captcha_id = data.get("result")
                print(f"获取验证码ID成功: {captcha_id}")
                return captcha_id
        print(f"初始化验证码失败: {response.text}")
        return None
    except Exception as e:
        print(f"初始化验证码异常: {e}")
        return None

def get_captcha_image(captcha_id):
    """获取验证码图片"""
    url = f"https://steampy.com/about/common/captcha/draw/{captcha_id}"
    try:
        response = session.get(url)
        if response.status_code == 200:
            # 保存验证码图片
            with open("captcha.png", "wb") as f:
                f.write(response.content)
            print("验证码图片已保存为 captcha.png")
            return True
        print(f"获取验证码图片失败: {response.status_code}")
        return False
    except Exception as e:
        print(f"获取验证码图片异常: {e}")
        return False

def send_registration_sms(captcha_id, captcha_code):
    """发送注册短信验证码"""
    url = f"{BASE_URL}/common/captcha/sendRegistAppSms/{MOBILE}"
    params = {
        "captchald": captcha_id,
        "code": captcha_code
    }
    
    try:
        response = session.get(url, params=params)
        data = response.json()
        if data.get("success"):
            print("短信验证码发送成功")
            return True
        print(f"短信发送失败: {data.get('message')}")
        return False
    except Exception as e:
        print(f"发送短信异常: {e}")
        return False

def register(sms_code):
    """提交注册"""
    url = f"{BASE_URL}/user/smsAppLogin"
    params = {
        "cid": "",
        "mobile": MOBILE,
        "code": sms_code,
        "inviter": "",
        "eventId": "",
        "sawelLogin": "true",  # 注意抓包中是sawelLogin不是saveLogin
        "mobileLog": "true"
    }
    
    try:
        response = session.post(url, params=params)
        data = response.json()
        if data.get("success"):
            access_token = data.get("result")
            print(f"注册成功! Access Token: {access_token}")
            return access_token
        print(f"注册失败: {data.get('message')}")
        return None
    except Exception as e:
        print(f"注册异常: {e}")
        return None

if __name__ == "__main__":
    # 步骤1: 初始化验证码获取captchaId
    captcha_id = init_captcha()
    if not captcha_id:
        exit(1)
    
    # 步骤2: 获取验证码图片
    if not get_captcha_image(captcha_id):
        exit(1)
    
    # 步骤3: 用户输入图形验证码
    captcha_code = input("请查看captcha.png并输入图形验证码: ").strip()
    
    # 步骤4: 发送短信验证码
    if send_registration_sms(captcha_id, captcha_code):
        print("短信验证码已发送，请查看手机")
    else:
        exit(1)
    
    # 步骤5: 用户输入短信验证码
    sms_code = input("请输入6位短信验证码: ").strip()
    
    # 步骤6: 提交注册
    access_token = register(sms_code)
    if access_token:
        print("注册流程完成，请保存Access Token")
        # 这里可以保存token到文件或环境变量
        with open("steampy_token.txt", "w") as f:
            f.write(access_token)