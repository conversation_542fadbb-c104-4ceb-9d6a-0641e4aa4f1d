{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}账户管理 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <h1>账户管理</h1>
    <p class="page-description">管理您的网页端和移动端账户凭据，保持登录状态。</p>
</header>

<div class="layout-grid">
    <section id="mobile-account-management">
        <div class="card card-constrained">
            <header>
                <h2>移动端登录 (SMS)</h2>
            </header>
            <form id="sms-login-form" class="form-grid">
                <div class="form-group">
                    <label for="phone">手机号</label>
                    <input type="tel" id="phone" name="phone" placeholder="请输入手机号" required>
                </div>
                <div class="form-group">
                    <label for="code">验证码</label>
                    <input type="text" id="code" name="code" placeholder="请输入验证码" required>
                </div>
                <div class="form-actions">
                    <button type="button" id="send-sms-btn" class="secondary">发送验证码</button>
                    <button type="submit">登录/添加</button>
                </div>
            </form>
        </div>
        
        <div class="card card-constrained">
             <header class="article-header">
                <h2>已添加的移动账户</h2>
                <button id="refresh-mobile-accounts-btn" class="outline icon-btn" aria-label="刷新">{{ icon('refresh-cw') }}</button>
            </header>
            <div id="mobile-account-list-container">
                <p>正在加载账户...</p>
            </div>
        </div>
    </section>
</div>
<style>
.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}
@media (min-width: 768px) {
    .form-grid {
        grid-template-columns: repeat(2, 1fr);
        align-items: flex-end;
    }
    .form-actions {
        grid-column: 1 / -1;
        justify-content: flex-end;
    }
}
.form-actions {
    display: flex;
    gap: 1rem;
}
#mobile-account-list-container ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/accounts.js') }}" defer></script>
{% endblock %} 