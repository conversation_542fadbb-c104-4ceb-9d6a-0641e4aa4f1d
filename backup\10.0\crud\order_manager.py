import json
import os
import logging
from typing import List, Optional
from datetime import datetime
from pathlib import Path
import aiofiles

from pywatcher.models.order import OrderRecord

logger = logging.getLogger(__name__)

# 订单数据文件路径
ORDERS_FILE_PATH = "pywatcher/data/orders.json"

class OrderManager:
    """订单管理器，处理订单的存储和查询"""
    
    def __init__(self, file_path: str = ORDERS_FILE_PATH):
        self.file_path = file_path
        self.orders = []
        
    async def load_orders(self):
        """从文件加载订单数据"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            
            # 如果文件不存在，创建一个空文件
            if not os.path.exists(self.file_path):
                async with aiofiles.open(self.file_path, 'w') as f:
                    await f.write('{"orders": []}')
                self.orders = []
                return
                
            # 从文件读取数据
            async with aiofiles.open(self.file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                data = json.loads(content)
                
                # 转换为订单对象
                self.orders = []
                for order_data in data.get('orders', []):
                    # 转换日期字符串为datetime对象
                    if 'order_time' in order_data and isinstance(order_data['order_time'], str):
                        try:
                            order_data['order_time'] = datetime.fromisoformat(order_data['order_time'])
                        except ValueError:
                            order_data['order_time'] = datetime.now()
                            
                    self.orders.append(OrderRecord(**order_data))
                    
        except Exception as e:
            logger.error(f"加载订单数据失败: {e}", exc_info=True)
            self.orders = []
    
    async def save_orders(self):
        """保存订单数据到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            
            # 将订单对象转换为字典
            data = {
                "orders": [order.dict() for order in self.orders]
            }
            
            # 写入文件
            async with aiofiles.open(self.file_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(data, default=self._json_serial, indent=2))
                
            logger.info(f"已保存{len(self.orders)}条订单记录")
            
        except Exception as e:
            logger.error(f"保存订单数据失败: {e}", exc_info=True)
    
    def _json_serial(self, obj):
        """处理JSON序列化中的特殊对象"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Type {type(obj)} not serializable")
    
    async def add_order(self, order: OrderRecord):
        """添加新订单"""
        # 检查订单ID是否已存在
        if any(o.id == order.id for o in self.orders):
            logger.warning(f"订单ID {order.id} 已存在，不添加")
            return
            
        self.orders.append(order)
        await self.save_orders()
        logger.info(f"已添加新订单: {order.game_name}, ID: {order.id}")
    
    def get_orders(self) -> List[OrderRecord]:
        """获取所有订单"""
        # 按照下单时间倒序排序
        return sorted(self.orders, key=lambda x: x.order_time, reverse=True)
    
    def get_order_by_id(self, order_id: str) -> Optional[OrderRecord]:
        """通过ID获取订单"""
        for order in self.orders:
            if order.id == order_id:
                return order
        return None
        
    async def delete_order(self, order_id: str) -> bool:
        """删除指定订单"""
        for i, order in enumerate(self.orders):
            if order.id == order_id:
                # 找到订单，从列表中删除
                self.orders.pop(i)
                # 保存更改
                await self.save_orders()
                logger.info(f"已删除订单: ID={order_id}, 游戏={order.game_name}")
                return True
        
        logger.warning(f"删除订单失败: 未找到订单ID={order_id}")
        return False
        
# 全局订单管理器实例
order_manager = None

async def get_order_manager() -> OrderManager:
    """获取全局订单管理器实例"""
    global order_manager
    if order_manager is None:
        order_manager = OrderManager()
        await order_manager.load_orders()
    return order_manager 