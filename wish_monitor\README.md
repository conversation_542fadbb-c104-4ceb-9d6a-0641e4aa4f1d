# Wish Game Monitor

这是一个Python脚本，用于监控指定游戏的价格，并在价格达到预设阈值时自动尝试下单并通过邮件发送通知。

## 功能特性

- **愿望单管理**：通过独立的 `wishlist.json` 文件管理您想监控的游戏及其价格阈值。
- **动态日志监控**：通过访问 `detlog` API 获取最新的游戏动态信息。
- **智能价格获取**：
    - 当 `detlog` 中出现愿望单内的游戏时，会为该游戏启动一个5分钟的价格监控窗口。
    - 在此窗口期内，脚本会使用 `API/monitor_config.json` 中配置的查询Cookie，以随机间隔（默认为5-15秒）调用 `listsale` API 获取游戏价格。
    - 5分钟窗口结束后，对该特定游戏的价格获取会暂停，直到 `detlog` 再次报告该游戏动态。
- **自动下单与通知**：
    - 如果获取到的价格低于或等于愿望单中设定的阈值，脚本会使用 `API/monitor_config.json` 中的下单Cookie尝试调用 `payorder` API 进行下单。
    - 下单成功并获取到支付链接后，会通过邮件发送通知。
- **灵活的配置**：大部分运行参数（如API地址、各种时间间隔、重试次数、Cookie冷却时间等）都通过 `wish_monitor_settings.json` 文件进行配置。
- **Cookie管理**：
    - 查询Cookie具有状态管理，包括连续失败次数统计和自动冷却机制，以提高稳定性。
    - `detlog` API调用使用 `WEB/config.json` 中的 `accessToken`。
    - 价格获取和下单使用 `API/monitor_config.json` 中的 `bbsToken` (存在于各Cookie条目中)。
- **日志记录**：详细的操作日志会记录在 `wish_monitor.log` 文件中，并在控制台输出。

## 文件结构

```
wish_monitor/
├── main.py                     # 主程序脚本
├── wish_monitor_settings.json  # 核心配置文件 (API路径, 时间间隔等)
├── wishlist.json               # 您的游戏愿望单 (游戏名, ID, 价格阈值)
├── .env                        # (可选) 环境变量文件，用于邮件配置
└── README.md                   # 本文档

# 脚本依赖的外部文件 (位于项目根目录，与 wish_monitor/ 同级)
WEB/
└── config.json                 # 包含 detlog API 所需的 accessToken
API/
└── monitor_config.json         # 包含查询Cookie池和下单Cookie
```

## 安装依赖

脚本依赖以下Python库：

- `httpx`: 用于发起异步HTTP请求。
- `python-dotenv`: 用于从 `.env` 文件加载环境变量。

您可以通过pip安装它们：

```bash
pip install httpx python-dotenv
```

## 配置步骤

1.  **`wish_monitor/wish_monitor_settings.json`**:
    *   这个文件包含了脚本运行的主要参数。通常情况下，您可以保留大部分默认值。
    *   关键路径配置（确保它们正确指向您项目中的文件）：
        *   `"web_config_path": "../WEB/config.json"`
        *   `"api_monitor_config_path": "../API/monitor_config.json"`
        *   `"wishlist_path": "wishlist.json"` (此文件应与`main.py`在同一目录)
    *   `detlog_min_interval_seconds` 和 `detlog_max_interval_seconds`：`detlog` API调用的最小和最大随机时间间隔（秒）。
    *   `price_fetch_window_seconds`: 每个游戏从 `detlog` 匹配到后，进行价格监控的活动窗口时长（秒，例如300秒代表5分钟）。
    *   `listsale_min_interval_seconds` 和 `listsale_max_interval_seconds`: 在游戏的价格监控活动窗口期内，调用 `listsale` API 的最小和最大随机时间间隔（秒）。
    *   其他参数如 `http_timeout_seconds`, `api_retry_count`, `max_consecutive_cookie_failures`, `cookie_cooldown_seconds`, `action_cooldown_seconds` 可根据网络情况和API限制进行调整。

2.  **`wish_monitor/wishlist.json`**:
    *   编辑此文件，列出您想要监控的游戏。每个游戏是一个JSON对象，包含：
        *   `name`: 游戏名称 (易于识别即可，主要用于日志)。
        *   `id`: 游戏的唯一ID (必须准确，用于API调用)。
        *   `threshold`: 您期望的价格阈值。当实际价格小于或等于此值时，会触发下单尝试。
    *   示例格式：
        ```json
        [
          {
            "name": "绝地求生",
            "id": "game_id_pubg",
            "threshold": 20.00
          },
          {
            "name": "赛博朋克2077",
            "id": "game_id_cp2077",
            "threshold": 99.50
          }
        ]
        ```

3.  **`WEB/config.json`** (位于项目根目录的 `WEB` 文件夹下):
    *   确保此文件存在，并且包含一个有效的 `"access_token"` 字段，用于 `detlog` API的认证。
    *   示例片段：
        ```json
        {
          "access_token": "your_detlog_access_token_here",
          // ... 其他可能的WEB应用配置 ...
        }
        ```

4.  **`API/monitor_config.json`** (位于项目根目录的 `API` 文件夹下):
    *   此文件用于提供查询Cookie和下单Cookie。
    *   `"query_cookies_pool"`: 一个包含多个查询Cookie对象的列表。每个对象应有：
        *   `"label"`: Cookie的描述性标签 (例如 "query_account_1")。
        *   `"cookie_data"`: 包含实际Cookie键值对的字典 (例如 `{"bbsToken": "...", ...}` )。
    *   `"order_cookies"`: 一个包含下单所需Cookie的对象，其结构也应包含 `"cookie_data"` 或直接是Cookie键值对，特别是 `bbsToken` 或 `userInfo`。
    *   脚本会从此文件的 `query_cookies_pool` 中随机选择Cookie进行价格查询，并使用 `order_cookies` 进行下单。

5.  **邮件通知配置 (`.env` 文件)**:
    *   在 `wish_monitor` 文件夹下创建一个名为 `.env` 的文本文件 (如果它尚不存在)。
    *   在此文件中设置以下环境变量，用于配置邮件发送：
        ```env
        SENDER_EMAIL=<EMAIL>
        RECEIVER_EMAIL=<EMAIL>
        EMAIL_PASSWORD=your_email_app_password_or_login_password
        SMTP_SERVER=your_smtp_server_address (e.g., smtp.qq.com)
        SMTP_PORT=your_smtp_port (e.g., 465 for SSL, 587 for TLS)
        ```
    *   **重要**: `EMAIL_PASSWORD` 很多情况下需要使用邮箱服务商提供的"应用专用密码"或"授权码"，而不是您的邮箱登录密码，特别是对于开启了两步验证的账户。

## 运行脚本

1.  确保所有配置文件都已按上述说明正确设置。
2.  打开终端或命令行界面。
3.  导航到 `wish_monitor` 文件夹。
4.  运行主脚本：
    ```bash
    python main.py
    ```

## 脚本执行流程

1.  **初始化**: 
    *   加载日志配置。
    *   从 `.env` 文件加载邮件相关的环境变量。
    *   加载 `wish_monitor_settings.json` 主配置文件。
    *   根据主配置文件中的路径，加载 `WEB/config.json` (获取`detlog`的`accessToken`)，`API/monitor_config.json` (获取查询Cookie池和下单Cookie)，以及 `wishlist.json` (获取愿望单)。
    *   初始化查询Cookie的状态列表。
2.  **主循环**: 脚本进入一个无限循环，持续执行以下操作：
    *   **`detlog` 获取**: 以 `detlog_min_interval_seconds` 到 `detlog_max_interval_seconds` 之间的随机间隔，调用 `detlog` API。这个API需要 `accessToken` 进行认证。
    *   **匹配愿望单**: 将 `detlog` API返回的游戏名称与 `wishlist.json` 中的游戏进行比较（忽略大小写）。
    *   **价格监控窗口管理**: 
        *   对于每个匹配到的游戏ID，检查其价格监控窗口状态（存储在 `active_price_fetching_windows` 字典中）。
        *   如果一个游戏的窗口是新激活的（即首次匹配或从暂停状态恢复），或者当前仍在其5分钟的活动窗口期内，则继续下一步。
        *   如果窗口刚过期，则将其标记为暂停，并跳过后续的价格获取步骤，直到 `detlog` 再次匹配到它以重新激活窗口。
    *   **价格获取 (`listsale` API)**: 
        *   如果匹配到的游戏处于有效的价格监控窗口期内：
            *   从 `QUERY_COOKIE_STATUSES` 中选择一个可用的查询Cookie (非冷却状态)。
            *   使用此Cookie（包含`bbsToken`）调用 `listsale` API 获取该游戏的当前最低价格和对应的 `saleId`。
            *   API调用包含重试机制。如果Cookie连续失败次数过多，会被置于冷却状态。
    *   **价格比较与下单 (`payorder` API)**:
        *   如果成功获取到价格，并且价格小于或等于愿望单中设定的阈值：
            *   检查此特定的 `(game_id, sale_id)` 组合是否在最近的 `action_cooldown_seconds` 内已被处理过。如果是，则跳过以避免重复操作。
            *   使用 `API/monitor_config.json` 中配置的 `order_cookies` (包含`bbsToken`) 调用 `payorder` API 尝试下单。
            *   记录此次操作，将 `(game_id, sale_id)` 加入冷却列表。
    *   **邮件通知**: 
        *   如果下单成功并返回了支付链接，脚本会构造一封包含商品信息、价格和支付链接的邮件，发送到 `.env` 文件中配置的收件人邮箱。
        *   如果下单因商品售罄（"被抢"）而失败，则记录日志但通常不会发送失败邮件（除非您修改逻辑）。
    *   **延时**: 在处理完一个从 `detlog` 匹配到的游戏后（无论是否下单），会有一个小的随机延时 (`listsale_min_interval_seconds` 到 `listsale_max_interval_seconds`)，然后处理 `detlog` 结果中的下一个匹配项（如果有的话）。主循环在每次检查是否到了获取 `detlog` 时间之前也会有短暂停顿。
3.  **日志**: 所有重要操作、API调用结果、错误信息等都会被记录到 `wish_monitor` 文件夹下的 `wish_monitor.log` 文件中，同时也会在控制台输出。

## 注意事项

- **API限制**: 请注意API的调用频率限制。过于频繁的请求可能导致IP被临时禁止或账户受到限制。脚本中的时间间隔参数应根据实际情况合理配置。
- **Cookie有效期**: Cookie（特别是 `accessToken` 和 `bbsToken`）通常有有效期。如果脚本开始出现认证失败的错误，您可能需要更新 `WEB/config.json` 中的 `accessToken` 或 `API/monitor_config.json` 中的Cookie数据。
- **错误处理**: 脚本包含基本的错误处理和重试机制，但复杂的网络环境或API行为可能需要进一步调整代码。
- **环境依赖**: 确保您的Python环境满足脚本运行的要求。

通过以上步骤，您应该能够成功配置并运行此游戏愿望单监控脚本。 