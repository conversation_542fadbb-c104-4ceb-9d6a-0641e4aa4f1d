/**
 * 夜间模式定时器服务
 * 负责监控时间变化并自动切换配置
 */

class NightModeScheduler {
    constructor() {
        this.checkInterval = null;
        this.backupInterval = null;
        this.lastNightModeState = null;
        this.listeners = [];
        
        // 启动定时器
        this.start();
        
        // 页面可见性变化时重新检查
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkNightModeChange();
            }
        });
        
        // 启动备用检查机制（每小时检查一次，作为安全网）
        this.startBackupCheck();
    }
    
    /**
     * 启动定时器
     */
    start() {
        // 立即检查一次并计算下次检查时间
        this.checkNightModeChange();
        this.scheduleNextCheck();
        
        console.log('夜间模式定时器已启动');
    }
    
    /**
     * 计算并安排下次检查时间
     */
    scheduleNextCheck() {
        const now = new Date();
        const nightStartTime = localStorage.getItem('nightStartTime') || '22:00';
        const nightEndTime = localStorage.getItem('nightEndTime') || '06:30';
        
        // 计算下一个时间节点（开始时间或结束时间）
        const nextCheckTime = this.calculateNextCheckTime(now, nightStartTime, nightEndTime);
        const delayMs = nextCheckTime.getTime() - now.getTime();
        
        console.log(`下次夜间模式检查时间: ${nextCheckTime.toLocaleString()}`);
        
        // 清除之前的定时器
        if (this.checkInterval) {
            clearTimeout(this.checkInterval);
        }
        
        // 设置精确的定时器
        this.checkInterval = setTimeout(() => {
            this.checkNightModeChange();
            this.scheduleNextCheck(); // 递归安排下次检查
        }, delayMs);
    }
    
    /**
     * 计算下一个检查时间点
     */
    calculateNextCheckTime(currentTime, startTime, endTime) {
        const [startHour, startMin] = startTime.split(':').map(Number);
        const [endHour, endMin] = endTime.split(':').map(Number);
        
        const today = new Date(currentTime);
        const tomorrow = new Date(currentTime);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        // 创建今天的开始和结束时间
        const todayStart = new Date(today);
        todayStart.setHours(startHour, startMin, 0, 0);
        
        const todayEnd = new Date(today);
        todayEnd.setHours(endHour, endMin, 0, 0);
        
        // 如果结束时间小于开始时间，说明跨日期
        if (endHour < startHour || (endHour === startHour && endMin < startMin)) {
            todayEnd.setDate(todayEnd.getDate() + 1);
        }
        
        // 创建明天的开始时间
        const tomorrowStart = new Date(tomorrow);
        tomorrowStart.setHours(startHour, startMin, 0, 0);
        
        // 判断当前时间并返回下一个检查点
        if (currentTime < todayStart) {
            return todayStart; // 今天的开始时间
        } else if (currentTime < todayEnd) {
            return todayEnd; // 今天的结束时间
        } else {
            return tomorrowStart; // 明天的开始时间
        }
    }
    
    /**
     * 启动备用检查机制
     */
    startBackupCheck() {
        // 每小时检查一次作为安全网，防止精确定时器失效
        this.backupInterval = setInterval(() => {
            console.log('备用检查：验证夜间模式状态');
            this.checkNightModeChange();
        }, 3600000); // 1小时
    }
    
    /**
     * 停止定时器
     */
    stop() {
        if (this.checkInterval) {
            clearTimeout(this.checkInterval);
            this.checkInterval = null;
        }
        
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
            this.backupInterval = null;
        }
        
        console.log('夜间模式定时器已停止');
    }
    
    /**
     * 检查夜间模式状态变化
     */
    checkNightModeChange() {
        const currentNightModeState = this.isNightTime();
        
        // 如果状态发生变化
        if (this.lastNightModeState !== null && this.lastNightModeState !== currentNightModeState) {
            this.onNightModeChange(currentNightModeState);
        }
        
        this.lastNightModeState = currentNightModeState;
    }
    
    /**
     * 检查当前是否为夜间时间
     */
    isNightTime() {
        const enableNightMode = localStorage.getItem('enableNightMode') === 'true';
        if (!enableNightMode) return false;
        
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        
        const nightStartTime = localStorage.getItem('nightStartTime') || '22:00';
        const nightEndTime = localStorage.getItem('nightEndTime') || '06:30';
        
        const [startHour, startMin] = nightStartTime.split(':').map(Number);
        const [endHour, endMin] = nightEndTime.split(':').map(Number);
        
        const startMinutes = startHour * 60 + startMin;
        const endMinutes = endHour * 60 + endMin;
        
        // 处理跨日期的情况（如22:00到次日06:30）
        if (startMinutes > endMinutes) {
            return currentTime >= startMinutes || currentTime <= endMinutes;
        } else {
            return currentTime >= startMinutes && currentTime <= endMinutes;
        }
    }
    
    /**
     * 夜间模式状态变化时的处理
     */
    onNightModeChange(isNightMode) {
        const modeText = isNightMode ? '夜间模式' : '日间模式';
        console.log(`切换到${modeText}`);
        
        // 显示状态通知
        this.showModeChangeNotification(isNightMode);
        
        // 通知监听器
        this.listeners.forEach(listener => {
            try {
                listener(isNightMode);
            } catch (error) {
                console.error('夜间模式监听器执行出错:', error);
            }
        });
        
        // 记录日志
        this.logModeChange(isNightMode);
    }
    
    /**
     * 显示模式切换通知
     */
    showModeChangeNotification(isNightMode) {
        const notification = document.createElement('div');
        notification.className = 'night-mode-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${isNightMode ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'};
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            animation: slideInRight 0.5s ease-out, fadeOut 0.5s ease-in 2.5s;
            opacity: 1;
        `;
        
        const icon = isNightMode ? '🌙' : '☀️';
        const modeText = isNightMode ? '夜间模式' : '日间模式';
        notification.innerHTML = `${icon} 已切换到${modeText}`;
        
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
    
    /**
     * 记录模式切换日志
     */
    logModeChange(isNightMode) {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN');
        const modeText = isNightMode ? '夜间模式' : '日间模式';
        
        // 尝试添加到CDK监控日志
        if (typeof addCdkLog === 'function') {
            addCdkLog(`🔄 ${timeString} 自动切换到${modeText}`, false, true);
        }
        
        // 尝试添加到肥皂盒监控日志
        if (typeof addMonitorLog === 'function') {
            addMonitorLog(`🔄 ${timeString} 自动切换到${modeText}`, false, true);
        }
        
        console.log(`[夜间模式调度器] ${timeString} 切换到${modeText}`);
    }
    
    /**
     * 添加状态变化监听器
     */
    addListener(listener) {
        if (typeof listener === 'function') {
            this.listeners.push(listener);
        }
    }
    
    /**
     * 移除状态变化监听器
     */
    removeListener(listener) {
        const index = this.listeners.indexOf(listener);
        if (index !== -1) {
            this.listeners.splice(index, 1);
        }
    }
    
    /**
     * 获取当前有效的下单配置
     */
    getCurrentOrderSettings() {
        const isNight = this.isNightTime();
        
        const settings = {
            cdk: {
                payType: localStorage.getItem(isNight ? 'nightCdkPayType' : 'cdkPayType') || 'AU',
                useBalance: localStorage.getItem(isNight ? 'nightCdkUseBalance' : 'cdkUseBalance') || '',
                useMainAccount: localStorage.getItem(isNight ? 'nightCdkUseMainAccount' : 'cdkUseMainAccount') !== 'false'
            },
            chest: {
                payType: localStorage.getItem(isNight ? 'nightChestPayType' : 'chestPayType') || 'AA',
                useBalance: localStorage.getItem(isNight ? 'nightChestUseBalance' : 'chestUseBalance') || '',
                useMainAccount: localStorage.getItem(isNight ? 'nightChestUseMainAccount' : 'chestUseMainAccount') !== 'false'
            },
            isNightMode: isNight
        };
        
        return settings;
    }
    
    /**
     * 手动触发检查
     */
    triggerCheck() {
        this.checkNightModeChange();
    }
    
    /**
     * 重新计算定时器（当时间设置发生变化时调用）
     */
    reschedule() {
        console.log('夜间模式时间设置已更改，重新计算定时器');
        this.scheduleNextCheck();
    }
}

// 添加必要的CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes fadeOut {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }
    
    .night-mode-notification {
        pointer-events: none;
        user-select: none;
    }
`;
document.head.appendChild(style);

// 创建全局实例
window.nightModeScheduler = new NightModeScheduler();

// 在页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.nightModeScheduler) {
        window.nightModeScheduler.stop();
    }
});

console.log('夜间模式调度器已初始化');