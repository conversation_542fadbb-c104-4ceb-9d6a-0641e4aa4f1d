document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const detailChestTitle = document.getElementById('detail-chest-title');
    const detailChestId = document.getElementById('detail-chest-id');
    const detailSeller = document.getElementById('detail-seller');
    const detailOncePrice = document.getElementById('detail-once-price');
    const detailMultiDraw = document.getElementById('detail-multi-draw');
    const detailMultiPrice = document.getElementById('detail-multi-price');
    const detailRemain = document.getElementById('detail-remain');
    const detailTotal = document.getElementById('detail-total');
    const detailLoading = document.getElementById('detail-loading');
    const detailError = document.getElementById('detail-error');
    const chestBasicInfo = document.getElementById('chest-basic-info');
    const chestGamesList = document.getElementById('chest-games-list');
    const refreshChestBtn = document.getElementById('refresh-chest-btn');
    const calculateEvBtn = document.getElementById('calculate-ev-btn');
    const chestEvResult = document.getElementById('chest-ev-result');
    const logsContent = document.getElementById('logs-content');
    
    // 确保日志加载指示器状态正确
    const logsLoadingIndicator = document.querySelector('#chest-logs-container .loading-indicator');
    if (logsLoadingIndicator) {
        logsLoadingIndicator.style.display = 'none';
    }

    // 添加计算期望按钮的提示
    if (calculateEvBtn) {
        // 添加tooltip
        calculateEvBtn.setAttribute('data-tooltip', '计算期望值');
    }
    
    // 游戏模板
    const poolTemplate = document.getElementById('pool-template');
    const gameCardTemplate = document.getElementById('game-card-template');
    
    // 添加旋转动画样式
    if (!document.getElementById('rotate-animation-style')) {
        const style = document.createElement('style');
        style.id = 'rotate-animation-style';
        style.innerHTML = `
            @keyframes rotate {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .rotating {
                animation: rotate 1s linear infinite;
            }
        `;
        document.head.appendChild(style);
    }
    
    // 当前肥皂盒数据
    let currentChestDetail = null;
    let currentChestGames = null;
    
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const chestId = urlParams.get('id');
    
    // 防止重复请求标志
    let isLoadingData = false;
    let isLoadingLogs = false;
    
    // 页面加载时获取肥皂盒详情
    if (chestId) {
        loadChestDetail(chestId);
        
        // 添加刷新按钮事件
        if (refreshChestBtn) {
            refreshChestBtn.addEventListener('click', function() {
                loadChestDetail(chestId);
                loadChestLogs(chestId);
            });
        }
        
        // 添加计算期望按钮事件
        if (calculateEvBtn) {
            calculateEvBtn.addEventListener('click', async function() {
                if (!currentChestDetail || !currentChestGames || currentChestGames.length === 0) {
                    showToast('请先等待肥皂盒数据和游戏列表加载完成', 'error');
                    return;
                }
                // 显示期望计算结果区域
                if (chestEvResult) chestEvResult.style.display = 'block';
                // 显示加载中
                const evResultContent = document.getElementById('ev-result-content');
                if (evResultContent) evResultContent.innerHTML = '<p>正在计算期望值，可能需要获取多个游戏价格...</p>';
                // 执行期望计算
                await calculateChestExpectation(currentChestDetail, currentChestGames);
            });
        }
        
        // 添加刷新日志按钮事件
        const refreshLogsBtn = document.getElementById('refresh-logs-btn');
        if (refreshLogsBtn) {
            refreshLogsBtn.addEventListener('click', async function() {
                // 添加旋转动画
                this.classList.add('rotating');
                try {
                    // 重新加载活动记录
                    await loadChestLogs(chestId);
                } finally {
                    // 无论成功或失败，1秒后移除旋转动画
                    setTimeout(() => {
                        this.classList.remove('rotating');
                    }, 1000);
                }
            });
        }
    } else {
        showDetailError('未提供有效的肥皂盒ID');
    }
    
    // 加载肥皂盒详情和游戏列表
    async function loadChestDetail(chestId) {
        if (isLoadingData) return;
        
        isLoadingData = true;
        
        // 添加一个半透明遮罩层，而不是隐藏内容
        const addOverlay = () => {
            // 如果已存在遮罩层，先移除
            removeOverlay();
            
            // 创建遮罩层
            const overlay = document.createElement('div');
            overlay.id = 'chest-loading-overlay';
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(255, 255, 255, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 100;
                backdrop-filter: blur(2px);
                transition: opacity 0.3s ease;
            `;
            
            // 添加加载动画
            const spinner = document.createElement('div');
            spinner.className = 'spin';
            spinner.style.width = '40px';
            spinner.style.height = '40px';
            
            overlay.appendChild(spinner);
            
            // 添加到页面主体部分
            const mainSection = document.querySelector('.detail-main-section .card');
            if (mainSection) {
                // 确保主体有相对定位
                mainSection.style.position = 'relative';
                mainSection.appendChild(overlay);
            }
        };
        
        // 移除遮罩层
        const removeOverlay = () => {
            const existingOverlay = document.getElementById('chest-loading-overlay');
            if (existingOverlay) {
                // 使用淡出效果
                existingOverlay.style.opacity = '0';
                setTimeout(() => {
                    existingOverlay.remove();
                }, 300);
            }
        };
        
        try {
            // 添加遮罩层
            addOverlay();
            
            // 不再直接隐藏内容
            if (detailError) detailError.style.display = 'none';
            
            // 获取肥皂盒详情
            const detailData = await fetchChestDetail(chestId);
            if (!detailData) {
                throw new Error('获取详情失败');
            }
            
            // 设置标题
            if (detailChestTitle) {
                detailChestTitle.textContent = detailData.name || '肥皂盒详情';
            }
            
            // 填充基本信息
            fillChestBasicInfo(detailData);
            
            // 获取肥皂盒游戏列表
            const gamesData = await fetchChestGames(chestId);
            
            // 提取概率信息
            const rateInfo = {
                rate1: detailData.rate1,
                rate2: detailData.rate2,
                rate3: detailData.rate3,
                rate4: detailData.rate4
            };
            
            // 填充游戏列表
            fillChestGames(gamesData, rateInfo);
            
            // 不再使用加载器，而是直接确保内容可见
            if (detailLoading) detailLoading.style.display = 'none';
            if (chestBasicInfo && chestBasicInfo.style.display !== 'block') chestBasicInfo.style.display = 'block';
            if (chestGamesList && chestGamesList.style.display !== 'block') chestGamesList.style.display = 'block';
            
            // 保存当前数据用于期望计算
            currentChestDetail = detailData;
            currentChestGames = gamesData;
            
            console.log("数据加载成功", currentChestDetail, currentChestGames);
            
            // 加载当前肥皂盒的抽取记录
            loadChestLogs(chestId);
            
            // 移除遮罩层
            setTimeout(removeOverlay, 300);
            
        } catch (error) {
            console.error('获取肥皂盒详情失败:', error);
            showDetailError(error.message || '获取肥皂盒详情失败');
            // 移除遮罩层
            removeOverlay();
        } finally {
            isLoadingData = false;
        }
    }
    
    /**
     * 获取肥皂盒详情
     * @param {string} chestId - 肥皂盒ID
     */
    async function fetchChestDetail(chestId) {
        const url = `/api/xboot/chest/showOne?chestId=${chestId}`;
        
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
            throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
        }
        
        const data = await response.json();
        if (data.code !== 200) {
            throw new Error(data.message || '获取详情失败');
        }
        
        return data.result;
    }
    
    /**
     * 获取肥皂盒游戏列表
     * @param {string} chestId - 肥皂盒ID
     */
    async function fetchChestGames(chestId) {
        const url = `/api/xboot/chest/showGame?chestId=${chestId}&pageNumber=1&pageSize=100&sort=lv&order=asc`;
        
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
            throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
        }
        
        const data = await response.json();
        if (data.code !== 200) {
            throw new Error(data.message || '获取游戏列表失败');
        }
        
        return data.result.content || [];
    }
    
    /**
     * 获取指定游戏ID的缓存价格
     * @param {Array} gameIds - 要获取价格的游戏ID数组
     * @returns {Promise<Object>} 游戏ID到价格的映射
     */
    async function fetchCachedPrices(gameIds) {
        if (!gameIds || !gameIds.length) {
            return {};
        }
        
        try {
            // 获取所有缓存价格
            const response = await fetch('/api/games/cached-prices');
            if (!response.ok) {
                throw new Error(`获取缓存价格失败: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 将数组转换为以游戏ID为键的对象
            const priceMap = {};
            if (Array.isArray(data)) {
                data.forEach(item => {
                    if (item.game_id && gameIds.includes(item.game_id) && item.price) {
                        priceMap[item.game_id] = item.price;
                    }
                });
            }
            
            return priceMap;
        } catch (error) {
            console.error("获取缓存价格时出错:", error);
            return {}; // 返回空对象
        }
    }
    
    /**
     * 获取游戏价格
     * @param {string} gameId - 游戏ID
     * @param {string} gameName - 游戏名称，用于日志（可选）
     * @returns {Promise<number>} 游戏价格
     */
    async function fetchGamePrice(gameId, gameName = null) {
        if (!gameId) {
            console.error('fetchGamePrice: 游戏ID为空');
            return 0;
        }

        try {
            // 直接使用现有的sellers API，但只获取一个卖家信息
            const url = `/api/games/${gameId}/sellers?pageSize=1`;
            const response = await fetch(url);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }));
                console.error(`获取游戏价格失败: ${errorData.detail || '请求失败'}`);
                return 0;
            }
            
            const sellersData = await response.json();
            
            // 检查返回的卖家数据
            if (Array.isArray(sellersData) && sellersData.length > 0) {
                const firstSeller = sellersData[0];
                if (firstSeller && typeof firstSeller.keyPrice === 'number') {
                    console.log(`获取到游戏 ${gameName || gameId} 的价格: ${firstSeller.keyPrice}`);
                    return firstSeller.keyPrice;
                }
            }
            
            console.warn(`没有找到游戏 ${gameName || gameId} 的有效价格`);
            return 0;
        } catch (error) {
            console.error(`获取游戏价格时出错: ${error.message || error}`);
            return 0;
        }
    }
    
    /**
     * 更新后端价格缓存
     * @param {string} gameId - 游戏ID
     * @param {string} gameName - 游戏名称
     * @param {number} price - 游戏价格
     * @returns {Promise<boolean>} 是否更新成功
     */
    async function updatePriceInBackendCache(gameId, gameName, price) {
        if (!gameId || !gameName || price === undefined || price === null) {
            console.warn('updatePriceInBackendCache: 参数不完整', { gameId, gameName, price });
            return false;
        }
        
        try {
            const response = await fetch('/api/games/cache-price', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ game_id: gameId, name: gameName, price: price })
            });
            
            if (!response.ok) {
                console.error(`更新价格缓存失败: 状态码 ${response.status}`);
                return false;
            }
            
            const data = await response.json();
            return data.success === true;
        } catch (error) {
            console.error(`更新价格缓存时出错: ${error.message || error}`);
            return false;
        }
    }
    
    /**
     * 获取肥皂盒抽取记录
     * @param {string} chestId - 肥皂盒ID
     */
    async function loadChestLogs(chestId) {
        if (isLoadingLogs || !logsContent) return;
        
        isLoadingLogs = true;
        
        // 为日志区域添加遮罩层
        const addLogsOverlay = () => {
            // 如果已存在遮罩层，先移除
            removeLogsOverlay();
            
            // 创建遮罩层
            const overlay = document.createElement('div');
            overlay.id = 'logs-loading-overlay';
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(255, 255, 255, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 100;
                backdrop-filter: blur(1px);
                transition: opacity 0.3s ease;
                border-radius: 12px;
            `;
            
            // 添加加载动画
            const spinner = document.createElement('div');
            spinner.className = 'spin';
            spinner.style.width = '20px';
            spinner.style.height = '20px';
            
            overlay.appendChild(spinner);
            
            // 添加到日志区域
            const logsContainer = document.querySelector('#chest-logs-container');
            if (logsContainer) {
                // 确保容器有相对定位
                logsContainer.style.position = 'relative';
                logsContainer.appendChild(overlay);
            }
        };
        
        // 移除日志遮罩层
        const removeLogsOverlay = () => {
            const existingOverlay = document.getElementById('logs-loading-overlay');
            if (existingOverlay) {
                // 使用淡出效果
                existingOverlay.style.opacity = '0';
                setTimeout(() => {
                    existingOverlay.remove();
                }, 300);
            }
        };
        
        try {
            // 添加遮罩层
            addLogsOverlay();
            
            console.log('开始加载活动记录，当前肥皂盒ID:', chestId);
            
            // 构建API URL - 使用原有正确的API路径
            const url = '/api/chests/xboot/detLog/show';
            const params = new URLSearchParams();
            if (chestId) {
                params.append('chestId', chestId);
            }
            params.append('pageNumber', 1);
            params.append('pageSize', 20);
            params.append('sort', 'createTime');
            params.append('order', 'desc');

            const fullUrl = `${url}?${params.toString()}`;
            console.log('API请求URL:', fullUrl);
            
            // 发送API请求
            const response = await fetch(fullUrl);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
                throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 检查API返回数据格式
            let logsData = null;
            if (data && data.result && Array.isArray(data.result.content)) {
                logsData = data.result.content;
            } else if (data && data.result && Array.isArray(data.result.records)) {
                logsData = data.result.records;
            } else if (data && data.data && Array.isArray(data.data.records)) {
                logsData = data.data.records;
            } else {
                console.log('未找到有效的记录数据');
                logsContent.innerHTML = '<div style="padding: 12px; color: rgba(255,255,255,0.7); text-align: center;">暂无抽取记录</div>';
                return;
            }
            
            if (logsData && logsData.length > 0) {
                // 渲染记录
                renderChestLogs(logsData);
            } else {
                logsContent.innerHTML = '<div style="padding: 12px; color: rgba(255,255,255,0.7); text-align: center;">暂无抽取记录</div>';
            }
            
        } catch (error) {
            console.error('获取肥皂盒抽取记录失败:', error);
            logsContent.innerHTML = `<div style="padding: 12px; color: rgba(255,255,255,0.7); text-align: center;">获取记录失败: ${error.message || '未知错误'}</div>`;
        } finally {
            isLoadingLogs = false;
            // 移除遮罩层
            setTimeout(removeLogsOverlay, 300);
        }
    }
    
    /**
     * 渲染抽取记录
     * @param {Array} logs - 肥皂盒记录数据数组
     */
    function renderChestLogs(logs) {
        // 获取新的列表容器
        const logsContent = document.getElementById('logs-content');
        if (!logsContent) {
            console.error('找不到logs-content元素');
            return;
        }
        
        // 清空现有内容
        logsContent.innerHTML = '';
        
        // 检查是否有记录
        if (!logs || logs.length === 0) {
            logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
            return;
        }
        
        // 显示全部记录
        const recentLogsList = document.createElement('div');
        recentLogsList.className = 'activity-list';
        
        logs.forEach((log) => {
            // 创建记录项
            const logItem = document.createElement('div');
            logItem.style.display = 'flex';
            logItem.style.alignItems = 'center';
            logItem.style.padding = '8px 16px';
            logItem.style.marginBottom = '8px';
            
            // 根据等级选择图标内容和颜色
            let iconContent = '4';
            let iconBackground = '#2196f3';
            const lv = log.lv || 4;
            
            if (lv === 1) {
                iconContent = '1';
                iconBackground = '#ff8c00';
            } else if (lv === 2) {
                iconContent = '2';
                iconBackground = '#9370db';
            } else if (lv === 3) {
                iconContent = '3';
                iconBackground = '#4682b4';
            }
            
            // 创建图标容器
            const iconContainer = document.createElement('div');
            iconContainer.style.width = '32px';
            iconContainer.style.height = '32px';
            iconContainer.style.backgroundColor = iconBackground;
            iconContainer.style.borderRadius = '50%';
            iconContainer.style.display = 'flex';
            iconContainer.style.justifyContent = 'center';
            iconContainer.style.alignItems = 'center';
            iconContainer.style.color = '#fff';
            iconContainer.style.fontWeight = '600';
            iconContainer.style.fontSize = '14px';
            iconContainer.style.marginRight = '10px';
            iconContainer.style.flexShrink = '0';
            iconContainer.textContent = iconContent;
            
            // 创建内容容器
            const contentContainer = document.createElement('div');
            contentContainer.style.flex = '1';
            
            // 游戏名称
            const gameName = document.createElement('div');
            gameName.style.fontSize = '14px';
            gameName.style.fontWeight = '500';
            gameName.style.marginBottom = '2px';
            gameName.textContent = log.gameName || '未知游戏';
            
            // 等级标签
            const levelLabel = document.createElement('div');
            levelLabel.style.fontSize = '12px';
            levelLabel.style.color = 'rgba(255,255,255,0.7)';
            levelLabel.textContent = `—${getLevelName(log.lv)}`;
            
            // 添加到内容容器
            contentContainer.appendChild(gameName);
            contentContainer.appendChild(levelLabel);
            
            // 创建时间容器
            const timeContainer = document.createElement('div');
            timeContainer.style.fontSize = '12px';
            timeContainer.style.color = 'rgba(255,255,255,0.7)';
            timeContainer.style.textAlign = 'right';
            timeContainer.style.flexShrink = '0';
            
            // 格式化时间
            const date = new Date(log.createTime);
            const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
            timeContainer.textContent = formattedDate;
            
            // 组装记录项
            logItem.appendChild(iconContainer);
            logItem.appendChild(contentContainer);
            logItem.appendChild(timeContainer);
            
            // 添加到列表
            recentLogsList.appendChild(logItem);
        });
        
        logsContent.appendChild(recentLogsList);
    }
    
    /**
     * 根据等级获取等级名称
     */
    function getLevelName(level) {
        switch (level) {
            case 1:
                return '传说';
            case 2:
                return '史诗';
            case 3:
                return '稀有';
            default:
                return '普通';
        }
    }
    
    /**
     * 显示详情错误信息
     * @param {string} message - 错误信息
     */
    function showDetailError(message) {
        if (detailLoading) detailLoading.style.display = 'none';
        if (chestBasicInfo) chestBasicInfo.style.display = 'none';
        if (chestGamesList) chestGamesList.style.display = 'none';
        if (chestEvResult) chestEvResult.style.display = 'none';
        
        if (detailError) {
            const errorElement = detailError.querySelector('p');
            if (errorElement) {
                errorElement.textContent = message || '获取详情失败，请稍后再试。';
            }
            
            detailError.style.display = 'block';
        }
    }
    
    /**
     * 填充肥皂盒基本信息
     * @param {Object} detail - 肥皂盒详情数据
     */
    function fillChestBasicInfo(detail) {
        if (!detail) return;
        
        // 设置ID
        if (detailChestId) detailChestId.textContent = detail.id || 'N/A';
        
        // 设置卖家
        if (detailSeller) detailSeller.textContent = detail.nameSeller || '未知卖家';
        
        // 设置抽取进度
        const totalDraw = detail.totalDraw || 0;
        const curDraw = detail.curDraw || 0;
        const remainDraws = totalDraw - curDraw;
        
        if (detailRemain) detailRemain.textContent = remainDraws;
        if (detailTotal) detailTotal.textContent = totalDraw;
        
        // 设置单抽价格
        if (detailOncePrice) {
            detailOncePrice.textContent = detail.oncePrice ? `¥${detail.oncePrice.toFixed(2)}` : 'N/A';
        }
        
        // 设置多抽次数和价格
        if (detailMultiDraw && detail.multidraw !== undefined) {
            detailMultiDraw.textContent = detail.multidraw;
        }
        
        if (detailMultiPrice && detail.multiPrice !== undefined) {
            detailMultiPrice.textContent = `¥${detail.multiPrice.toFixed(2)}`;
        }
    }
    
    /**
     * 填充肥皂盒游戏列表
     * @param {Array} games - 肥皂盒中的游戏数据
     * @param {Object} rateInfo - 概率信息对象
     */
    function fillChestGames(games, rateInfo) {
        if (!games || !games.length || !chestGamesList) return;
        
        // 清空容器
        chestGamesList.innerHTML = '';
        
        // 转换概率函数 - 确保概率正确显示为百分比
        const formatRate = (rateValue) => {
            if (rateValue === null || rateValue === undefined) return "未知";
            
            // 将字符串转换为数字
            let rateNum = parseFloat(rateValue);
            if (isNaN(rateNum)) return "未知";
            
            // 如果概率已经是百分比形式（大于1），则直接显示
            if (rateNum > 1) {
                return `${rateNum.toFixed(2)}%`.replace(/0+$/, '0');
            } 
            // 如果概率是小数形式（0-1之间），则转换为百分比
            else {
                return `${(rateNum * 100).toFixed(2)}%`.replace(/0+$/, '0');
            }
        };
        
        // 按等级分组游戏 (将等级视为池子类型)
        const poolMap = {
            "1": { name: "传说池", class: "legendary-pool", probability: formatRate(rateInfo.rate1) },
            "2": { name: "史诗池", class: "epic-pool", probability: formatRate(rateInfo.rate2) },
            "3": { name: "稀有池", class: "rare-pool", probability: formatRate(rateInfo.rate3) },
            "4": { name: "普通池", class: "common-pool", probability: formatRate(rateInfo.rate4) }
        };
        
        // 按等级分组游戏
        const gamesByLevel = {};
        games.forEach(game => {
            // 跳过没有名称的游戏
            if (!game.gameNameCn) return;
            
            const lv = game.lv || '1';
            if (!gamesByLevel[lv]) {
                gamesByLevel[lv] = [];
            }
            gamesByLevel[lv].push(game);
        });
        
        // 获取肥皂盒单抽价格 - 修复可能的undefined错误
        let chestOncePrice = 0;
        try {
            chestOncePrice = parseFloat(detailOncePrice?.textContent?.replace('¥', '') || 90);
        } catch (e) {
            console.warn("无法正确解析肥皂盒价格，使用默认值90");
            chestOncePrice = 90;
        }
        
        // 获取所有游戏ID
        const gameIds = games.map(game => game.gameId).filter(id => id);
        
        // 先从数据库获取缓存价格，然后渲染游戏列表
        fetchCachedPrices(gameIds).then(cachedPrices => {
            renderGamePools(cachedPrices, chestOncePrice);
        }).catch(error => {
            console.error("获取缓存价格失败:", error);
            renderGamePools({}, chestOncePrice);
        });
        
        // 渲染游戏池函数
        function renderGamePools(cachedPrices, chestOncePrice) {
            // 按等级顺序渲染游戏池
            const sortedLevels = Object.keys(gamesByLevel).sort((a, b) => parseInt(a) - parseInt(b));
            
            sortedLevels.forEach(level => {
                const poolInfo = poolMap[level] || { name: `等级 ${level}`, class: "default-pool", probability: "未知" };
                
                // 克隆池子模板
                const poolElement = poolTemplate.content.cloneNode(true).firstElementChild;
                
                // 设置池子名称和等级标签
                const poolNameEl = poolElement.querySelector('.pool-name');
                poolNameEl.textContent = poolInfo.name;
                
                // 创建并添加等级标签
                const levelTag = document.createElement('span');
                levelTag.className = `pool-level lv${level}`;
                levelTag.textContent = `Lv.${level}`;
                poolNameEl.appendChild(levelTag);
                
                // 添加池子样式类
                poolElement.classList.add(poolInfo.class);
                
                // 设置概率
                const probabilityEl = poolElement.querySelector('.pool-probability');
                if (probabilityEl) {
                    probabilityEl.textContent = poolInfo.probability;
                }
                
                // 获取游戏容器
                const poolGamesContainer = poolElement.querySelector('.pool-games');
                
                // 渲染该等级的所有游戏
                gamesByLevel[level].forEach(game => {
                    // 创建游戏卡片
                    const gameCard = document.createElement('div');
                    gameCard.className = 'game-card';
                    
                    // 设置游戏标题
                    const titleDiv = document.createElement('div');
                    titleDiv.className = 'game-title';
                    titleDiv.textContent = game.gameNameCn || '未知游戏';
                    gameCard.appendChild(titleDiv);
                    
                    // 创建信息行容器
                    const infoRow = document.createElement('div');
                    infoRow.className = 'game-info-row';
                    
                    // 添加库存信息
                    const stockSpan = document.createElement('span');
                    stockSpan.textContent = `库存: ${game.stock || 0}`;
                    infoRow.appendChild(stockSpan);
                    
                    // 创建价格显示元素
                    const priceEl = document.createElement('span');
                    priceEl.className = 'game-price-display';
                    
                    // 检查是否有缓存价格
                    const cachedPrice = cachedPrices[game.gameId];
                    if (cachedPrice) {
                        // 以橙色显示缓存价格
                        priceEl.textContent = `¥${cachedPrice.toFixed(2)}`;
                        priceEl.classList.add('cached-price');
                        
                        // 如果价格大于单抽价格+10，突出显示游戏卡片
                        if (cachedPrice > (chestOncePrice + 10)) {
                            gameCard.classList.add('high-value-game');
                        }
                    }
                    
                    infoRow.appendChild(priceEl);
                    
                    // 添加信息行到卡片
                    gameCard.appendChild(infoRow);
                    
                    // 将游戏ID和名称添加为数据属性
                    gameCard.dataset.gameId = game.gameId;
                    gameCard.dataset.gameName = game.gameNameCn;
                    
                    // 添加点击事件获取价格
                    gameCard.addEventListener('click', async () => {
                        try {
                            // 显示加载中
                            if (!priceEl.textContent) {
                                priceEl.textContent = '...';
                            }
                            
                            // 获取游戏价格
                            const price = await fetchGamePrice(game.gameId, game.gameNameCn);
                            
                            // 如果获取到有效价格，则保存到数据库
                            if (price > 0) {
                                const result = await updatePriceInBackendCache(game.gameId, game.gameNameCn, price);
                                
                                // 更新显示价格，移除缓存样式（因为现在是实时价格）
                                priceEl.textContent = `¥${price.toFixed(2)}`;
                                priceEl.classList.remove('cached-price');
                                
                                // 检查是否是高价值游戏
                                if (price > (chestOncePrice + 10)) {
                                    gameCard.classList.add('high-value-game');
                                } else {
                                    gameCard.classList.remove('high-value-game');
                                }
                                
                                // 显示成功获取价格的通知
                                showToast(`已获取价格: ¥${price.toFixed(2)}`, 'success');
                            } else {
                                priceEl.textContent = '';
                                console.warn(`未获取到有效的价格，游戏 ${game.gameNameCn} (ID: ${game.gameId})`);
                                showToast(`获取价格失败`, 'error');
                            }
                        } catch (error) {
                            priceEl.textContent = '';
                            console.error(`获取游戏 ${game.gameNameCn} (ID: ${game.gameId}) 价格时出错:`, error);
                            showToast(`获取价格失败`, 'error');
                        }
                    });
                    
                    // 库存为0时，仅变暗，不加标签
                    if (!game.stock || game.stock === 0) {
                        gameCard.style.opacity = '0.5';
                    }
                    
                    // 添加卡片到容器
                    poolGamesContainer.appendChild(gameCard);
                });
                
                // 添加池子到游戏列表
                chestGamesList.appendChild(poolElement);
            });
        }
    }
    
    /**
     * 计算肥皂盒的期望值
     * @param {Object} chestDetail - 肥皂盒详情数据
     * @param {Array} games - 肥皂盒中的游戏数据
     */
    async function calculateChestExpectation(chestDetail, games) {
        const evResultContent = document.getElementById('ev-result-content');
        if (!chestDetail || !games || !games.length || !evResultContent) {
            showToast('无法计算期望：数据不完整', 'error');
            return;
        }
        let totalExpectedValue = 0;
        let anErrorOccurred = false;
        // 获取单抽成本
        const singleDrawCost = Number(chestDetail.oncePrice);
        if (isNaN(singleDrawCost)) {
            evResultContent.innerHTML = '<p style="color:red;">错误：未能获取到有效的单抽成本</p>';
            return;
        }
        // 收集所有有效游戏ID
        const allGameIdsInChest = [...new Set(games
            .filter(game => game.gameId && Number(game.stock ?? 0) > 0)
            .map(game => game.gameId))];
        // 如果没有有效游戏ID
        if (allGameIdsInChest.length === 0) {
            evResultContent.innerHTML = '<p>肥皂盒中没有库存大于0的游戏，无法计算期望</p>';
            return;
        }
        // 批量获取缓存价格
        const cachedPricesMap = await fetchCachedPrices(allGameIdsInChest);
        // 按等级分组游戏
        const gamesByLevel = games.reduce((acc, game) => {
            const level = game.lv ?? '未知';
            if (!acc[level]) acc[level] = [];
            acc[level].push(game);
            return acc;
        }, {});
        // 处理每个等级
        for (const level of Object.keys(gamesByLevel)) {
            if (level === '未知') continue;
            // 获取当前等级的概率
            const levelRate = Number(chestDetail[`rate${level}`]);
            if (isNaN(levelRate) || levelRate === 0) continue;
            const gamesInLevel = gamesByLevel[level];
            let weightedSumValueInLevel = 0;
            let totalStockInLevel = gamesInLevel.reduce(
                (sum, game) => sum + (Number(game.stock ?? 0) > 0 ? Number(game.stock) : 0),
                0
            );
            if (totalStockInLevel <= 0) continue;
            // 处理该等级中的每个游戏
            for (const game of gamesInLevel) {
                const gameId = game.gameId;
                const gameStock = Number(game.stock ?? 0);
                if (!gameId || gameStock <= 0) continue;
                let itemPrice = null;
                // 尝试从缓存获取价格
                if (cachedPricesMap[gameId]) {
                    itemPrice = Number(cachedPricesMap[gameId]);
                } else {
                    // 如果缓存中没有，尝试获取实时价格
                    try {
                        itemPrice = await fetchGamePrice(gameId);
                        // 如果获取到有效价格，保存到数据库
                        if (itemPrice !== null && !isNaN(itemPrice) && itemPrice > 0) {
                            const gameName = game.gameNameCn || '';
                            await updatePriceInBackendCache(gameId, gameName, itemPrice);
                        }
                    } catch (error) {
                        console.error(`获取游戏 ${gameId} 价格失败:`, error);
                        anErrorOccurred = true;
                    }
                }
                // 如果获取到有效价格，计算该游戏对期望的贡献
                if (itemPrice !== null && !isNaN(itemPrice)) {
                    const probabilityOfThisItemInLevel = gameStock / totalStockInLevel;
                    weightedSumValueInLevel += probabilityOfThisItemInLevel * itemPrice;
                }
            }
            // 计算该等级对总期望的贡献
            totalExpectedValue += levelRate * weightedSumValueInLevel;
        }
        // 显示计算结果
        if (anErrorOccurred) {
            evResultContent.innerHTML += '<p style="color:orange;">警告：部分游戏价格获取失败，结果可能不准确</p>';
        }
        // 计算净期望收益和期望倍率
        const netEV = totalExpectedValue - singleDrawCost;
        const multiplier = singleDrawCost > 0 ? (totalExpectedValue / singleDrawCost) : 0;
        // 确定期望倍率的解释文字和样式
        let explanation = "";
        let multiplierClass = "";
        if (multiplier > 1.1) {
            explanation = "期望 > 1.1，可能盈利";
            multiplierClass = "ev-good";
        } else if (multiplier > 0.9 && multiplier <= 1.1) {
            explanation = "期望接近1，不赚不亏";
            multiplierClass = "ev-neutral";
        } else {
            explanation = "期望 < 0.9，可能亏损";
            multiplierClass = "ev-bad";
        }
        // 构建结果HTML
        let resultHTML = `
            <div class="ev-multiplier ${multiplierClass}">
                期望倍率: ${multiplier.toFixed(4)} <span>(${explanation})</span>
            </div>
            <div class="ev-detail">
                <div class="ev-item">
                    <span class="ev-item-label">毛期望价值:</span>
                    <span class="ev-item-value">¥${totalExpectedValue.toFixed(2)}</span>
                </div>
                <div class="ev-item">
                    <span class="ev-item-label">单抽成本:</span>
                    <span class="ev-item-value">¥${singleDrawCost.toFixed(2)}</span>
                </div>
                <div class="ev-item">
                    <span class="ev-item-label">净期望收益:</span>
                    <span class="ev-item-value ${netEV > 0 ? 'ev-good' : 'ev-bad'}">¥${netEV.toFixed(2)}</span>
                </div>
                <div class="ev-item">
                    <span class="ev-item-label">ROI:</span>
                    <span class="ev-item-value ${netEV > 0 ? 'ev-good' : 'ev-bad'}">${(netEV / singleDrawCost * 100).toFixed(2)}%</span>
                </div>
            </div>
        `;
        evResultContent.innerHTML = resultHTML;
    }
    
    /**
     * 显示提示信息
     * @param {string} message - 提示消息
     * @param {string} type - 提示类型：success, error, info
     */
    function showToast(message, type = 'success') {
        // 检查是否已有toast元素
        let toast = document.querySelector('.toast');
        if (!toast) {
            // 创建toast元素
            toast = document.createElement('div');
            toast.className = 'toast';
            document.body.appendChild(toast);
        }
        
        // 设置消息内容和类型
        toast.textContent = message;
        toast.className = 'toast';
        
        // 添加类型样式
        if (type) {
            toast.classList.add(type);
        }
        
        // 显示toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }
});