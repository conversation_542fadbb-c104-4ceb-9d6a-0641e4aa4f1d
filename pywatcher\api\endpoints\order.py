from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import logging
from pywatcher.crud.config_manager import get_config, update_config
from pydantic import BaseModel

router = APIRouter()
logger = logging.getLogger(__name__)

class OrderConfig(BaseModel):
    payType: str = "Alipay"
    useWallet: bool = False

@router.get("/order-config")
async def get_order_config() -> Dict[str, Any]:
    """获取下单配置信息"""
    config = get_config()
    
    # 如果配置中不存在下单配置，则创建默认配置
    if "order_config" not in config:
        config["order_config"] = {
            "payType": "Alipay",
            "useWallet": False
        }
        update_config(config)
    
    return config["order_config"]

@router.post("/order-config")
async def update_order_config(config: OrderConfig) -> Dict[str, Any]:
    """更新下单配置信息"""
    current_config = get_config()
    
    current_config["order_config"] = {
        "payType": config.payType,
        "useWallet": config.useWallet
    }
    
    update_config(current_config)
    return {"status": "success", "message": "下单配置已更新"} 