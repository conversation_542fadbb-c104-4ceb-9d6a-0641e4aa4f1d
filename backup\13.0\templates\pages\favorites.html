{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}我的收藏 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <div class="header-content">
        <h1>
            {{ icon('star') }}
            我的游戏收藏
        </h1>
        <p class="page-description">
            您收藏的 Steam 游戏列表。
        </p>
    </div>
</header>

<section>
    <header class="article-header">
        <h2>收藏游戏</h2>
        <a href="/games" class="btn outline">{{ icon('search') }} 搜索游戏</a>
    </header>
    <div id="favorites-container">
        <div class="loading-indicator"><div class="spin"></div><span>加载中...</span></div>
    </div>
</section>

<script type="text/javascript">
    // 初始化排序功能
    document.addEventListener('DOMContentLoaded', function() {
        // 监听收藏容器变化
        const favoritesContainer = document.getElementById('favorites-container');
        if (favoritesContainer) {
            // 使用MutationObserver监听DOM变化
            const observer = new MutationObserver(function(mutations) {
                // 一旦列表加载完成，初始化Sortable
                if (document.querySelector('.favorites-list')) {
                    initSortable();
                    observer.disconnect();
                }
            });
            
            // 开始监听DOM变化
            observer.observe(favoritesContainer, { childList: true });
        }
        
        // 初始化排序功能
        function initSortable() {
            const favoritesList = document.querySelector('.favorites-list');
            if (!favoritesList) return;
            
            // 初始化Sortable
            const sortable = new Sortable(favoritesList, {
                animation: 150,
                handle: '.favorites-item',
                onEnd: function(evt) {
                    // 保存新的排序顺序
                    saveFavoritesOrder();
                }
            });
        }
        
        // 保存收藏排序顺序
        function saveFavoritesOrder() {
            const favoritesList = document.querySelector('.favorites-list');
            if (!favoritesList) return;
            
            // 获取所有游戏ID
            const gameIds = [];
            const items = favoritesList.querySelectorAll('.favorites-item');
            items.forEach(item => {
                const idElement = item.querySelector('.result-card-id');
                if (idElement) {
                    // 提取ID: xxx 格式中的ID
                    const idMatch = idElement.textContent.match(/ID:\s*(\S+)/);
                    if (idMatch && idMatch[1]) {
                        gameIds.push(idMatch[1]);
                    }
                }
            });
            
            // 获取当前收藏
            let favorites = JSON.parse(localStorage.getItem('gamesFavorites') || '[]');
            
            // 重新排序收藏
            const newFavorites = [];
            gameIds.forEach(id => {
                const favorite = favorites.find(fav => fav.id === id);
                if (favorite) {
                    newFavorites.push(favorite);
                }
            });
            
            // 保存新排序
            localStorage.setItem('gamesFavorites', JSON.stringify(newFavorites));
        }
    });
</script>

<!-- 游戏卡片模板 (隐藏) -->
<template id="game-card-template">
    <div class="result-card">
        <div class="image-container">
            <img class="result-card-image" src="" alt="Game Image">
            <button class="favorite-btn" aria-label="取消收藏游戏">★</button>
        </div>
        <div class="result-card-info">
            <strong class="result-card-name"></strong>
            <span class="text-muted result-card-id"></span>
            <div class="result-card-prices">
                <span class="price-tag">Key 价格: <strong class="key-price">N/A</strong></span>
                <span class="price-tag">平均 Key 价: <strong class="key-ave-amt">N/A</strong></span>
            </div>
        </div>
        <!-- 卖家信息按钮 -->
        <button class="sellers-btn" role="button">查看卖家</button>
    </div>
</template>

<!-- 卖家信息模态框 -->
<div id="sellers-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-game-title">卖家信息</h3>
            <div class="modal-actions">
                <button class="refresh-btn" title="刷新卖家列表">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-refresh-cw"><polyline points="23 4 23 10 17 10"></polyline><polyline points="1 20 1 14 7 14"></polyline><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path></svg>
                </button>
                <button class="close-btn" title="关闭">&times;</button>
            </div>
        </div>
        <div class="modal-body">
            <div id="modal-loading" style="display: none;">正在加载卖家信息...</div>
            <div id="modal-sellers-list"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/app.js') }}" defer></script>
{% endblock %} 