// This script is intended for the games page.
document.addEventListener('DOMContentLoaded', () => {
    // DOM Element References
    const searchInput = document.getElementById('game-search-input');
    const searchForm = document.getElementById('game-search-form');
    const resultsContainer = document.getElementById('game-results-container');
    const gameCardTemplate = document.getElementById('game-card-template');

    // Modal elements
    const modal = document.getElementById('sellers-modal');
    const closeModalButton = modal?.querySelector('.close-btn');
    const modalGameTitle = document.getElementById('modal-game-title');
    const modalSellersList = document.getElementById('modal-sellers-list');
    const modalLoading = document.getElementById('modal-loading');

    /**
     * Create a game card element from game data
     * @param {object} game - Game data
     * @returns {HTMLElement}
     */
    function createGameCard(game) {
        const card = gameCardTemplate.content.cloneNode(true).firstElementChild;
        card.querySelector('.result-card-image').src = game.picUrl;
        card.querySelector('.result-card-name').textContent = game.gameName;
        card.querySelector('.result-card-id').textContent = `ID: ${game.id}`;

        // Populate prices
        const keyPriceEl = card.querySelector('.key-price');
        if (game.keyPrice !== null && game.keyPrice !== undefined) {
            keyPriceEl.textContent = `¥${game.keyPrice.toFixed(2)}`;
        }

        const keyAveAmtEl = card.querySelector('.key-ave-amt');
        if (game.keyAveAmt !== null && game.keyAveAmt !== undefined) {
            keyAveAmtEl.textContent = `¥${game.keyAveAmt.toFixed(2)}`;
        }
        
        const sellersButton = card.querySelector('.sellers-btn');
        sellersButton.addEventListener('click', () => openSellersModal(game));

        return card;
    }

    /**
     * Handle the game search
     */
    async function handleGameSearch(event) {
        // Ensure to prevent the default form submission behavior
        event.preventDefault();
        event.stopPropagation();

        const query = searchInput.value.trim();
        if (!query) {
            resultsContainer.innerHTML = '<p class="text-muted">请输入关键词进行搜索。</p>';
            return;
        }

        resultsContainer.innerHTML = '<div class="loading-indicator"><div class="spin"></div><span>正在搜索...</span></div>';
        
        try {
            const searchUrl = `/api/games/search?query=${encodeURIComponent(query)}`;
            const response = await fetch(searchUrl);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }));
                throw new Error(errorData.detail || '搜索请求失败');
            }
            const games = await response.json();
            
            resultsContainer.innerHTML = '';
            if (!Array.isArray(games) || games.length === 0) {
                resultsContainer.innerHTML = '<p>没有找到相关游戏。</p>';
                return;
            }
            
            games.forEach(game => {
                const card = createGameCard(game);
                resultsContainer.appendChild(card);
            });

        } catch (error) {
            resultsContainer.innerHTML = `<p class="error-indicator">搜索失败: ${error.message}</p>`;
        }
    }

    /**
     * Open the sellers modal
     * @param {object} game
     */
    async function openSellersModal(game) {
        modal.style.display = 'block';
        modalGameTitle.textContent = `卖家列表 - ${game.gameName}`;
        modalSellersList.innerHTML = '';
        modalLoading.style.display = 'block';

        try {
            const response = await fetch(`/api/games/${game.id}/sellers`);
            const responseData = await response.json().catch(e => ({ error: '解析响应失败' }));
            
            modalLoading.style.display = 'none';
            
            if (!response.ok) {
                // 非200响应，显示错误信息
                const errorMessage = responseData.detail || '获取卖家信息失败';
                console.error('Error fetching sellers:', errorMessage);
                modalSellersList.innerHTML = `<li class="error">错误: ${errorMessage}</li>`;
                return;
            }
            
            // 响应正常，但可能是空数组
            if (!Array.isArray(responseData) || responseData.length === 0) {
                modalSellersList.innerHTML = '<li>暂无卖家信息。</li>';
                return;
            }

            // 有卖家数据，创建列表
            const list = document.createElement('ul');
            list.className = 'sellers-list';
            
            responseData.forEach(seller => {
                const item = document.createElement('li');
                
                // 根据API返回字段获取卖家信息
                const sellerName = seller.steamName || 'N/A';
                const price = typeof seller.keyPrice === 'number' ? `¥${seller.keyPrice.toFixed(2)}` : 'N/A';
                const quantity = seller.stock || 0;
                const saleId = seller.saleId || '';
                const discount = seller.discount ? `${(seller.discount * 100).toFixed(0)}%` : '';
                
                item.className = 'seller-item';
                item.setAttribute('data-sale-id', saleId); // 将saleId存储为数据属性
                
                // 创建更详细的卖家信息显示
                item.innerHTML = `
                    <div class="seller-info-group">
                        <span class="seller-name">卖家: ${sellerName}</span>
                        <span class="seller-price">价格: ${price}</span>
                        ${discount ? `<span class="seller-discount">折扣: ${discount}</span>` : ''}
                        <span class="seller-quantity">库存: ${quantity}</span>
                    </div>
                    <div class="seller-actions">
                        <span class="seller-id text-muted">订单号: ${saleId}</span>
                    </div>
                `;
                list.appendChild(item);
            });
            
            modalSellersList.appendChild(list);

        } catch (error) {
            console.error('Error fetching sellers:', error);
            modalLoading.style.display = 'none';
            modalSellersList.innerHTML = `<li class="error">加载卖家信息失败: ${error.message || '未知错误'}</li>`;
        }
    }

    // Event Listeners
    if (searchForm) {
        searchForm.addEventListener('submit', handleGameSearch);
    }

    // Modal event listeners
    if (closeModalButton) {
        closeModalButton.addEventListener('click', () => {
            modal.style.display = 'none';
        });
    }

    if (modal) {
        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
});