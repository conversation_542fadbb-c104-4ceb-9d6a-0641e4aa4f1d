from fastapi import APIRouter, Depends, Query, HTTPException
from typing import Dict, Any, List
import logging

from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.api.dependencies import get_steam_py_client

router = APIRouter(
    prefix="/chests",
    tags=["Chests"]
)

logger = logging.getLogger(__name__)

@router.get("", response_model=Dict[str, Any])
async def get_chests(
    page_number: int = Query(1, ge=1, description="页码，从1开始"),
    page_size: int = Query(10, ge=1, le=50, description="每页显示条数"),
    sort: str = Query("sortOrder", description="排序字段"),
    order: str = Query("asc", description="排序方向：asc或desc"),
    client: SteamPyClient = Depends(get_steam_py_client)
):
    """
    获取肥皂盒记录
    """
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据，请先登录。")
    
    try:
        logger.info(f"获取肥皂盒记录: 页码={page_number}, 排序={sort}, 顺序={order}")
        response = await client.get_chests(
            page_number=page_number,
            page_size=page_size,
            sort=sort,
            order=order
        )
        
        if response.get("code") == 200:
            return response
        elif response.get("code") == 401:
            raise HTTPException(status_code=401, detail="认证失败或登录已过期，请重新登录。")
        else:
            logger.warning(f"获取肥皂盒数据失败: {response}")
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "获取肥皂盒数据失败")
            )
    except Exception as e:
        logger.error(f"获取肥皂盒数据时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取肥皂盒数据失败: {str(e)}")

@router.get("/xboot/chest/showOne", response_model=Dict[str, Any])
async def get_chest_detail(
    chestId: str = Query(..., description="肥皂盒ID"),
    client: SteamPyClient = Depends(get_steam_py_client)
):
    """
    获取肥皂盒详情
    """
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据，请先登录。")
    
    try:
        logger.info(f"获取肥皂盒详情: ID={chestId}")
        response = await client.get_chest_details(chest_id=chestId)
        
        if response.get("code") == 200:
            return response
        elif response.get("code") == 401:
            raise HTTPException(status_code=401, detail="认证失败或登录已过期，请重新登录。")
        else:
            logger.warning(f"获取肥皂盒详情失败: {response}")
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "获取肥皂盒详情失败")
            )
    except Exception as e:
        logger.error(f"获取肥皂盒详情时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取肥皂盒详情失败: {str(e)}")

@router.get("/xboot/chest/showGame", response_model=Dict[str, Any])
async def get_chest_games(
    chestId: str = Query(..., description="肥皂盒ID"),
    pageNumber: int = Query(1, ge=1, description="页码，从1开始"),
    pageSize: int = Query(100, ge=1, le=200, description="每页显示条数"),
    sort: str = Query("lv", description="排序字段"),
    order: str = Query("asc", description="排序方向：asc或desc"),
    client: SteamPyClient = Depends(get_steam_py_client)
):
    """
    获取肥皂盒游戏列表
    """
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据，请先登录。")
    
    try:
        logger.info(f"获取肥皂盒游戏列表: ID={chestId}, 页码={pageNumber}, 排序={sort}, 顺序={order}")
        response = await client.get_chest_games(
            chest_id=chestId,
            page_number=pageNumber,
            page_size=pageSize,
            sort_order=sort,
            order_direction=order
        )
        
        if response.get("code") == 200:
            return response
        elif response.get("code") == 401:
            raise HTTPException(status_code=401, detail="认证失败或登录已过期，请重新登录。")
        else:
            logger.warning(f"获取肥皂盒游戏列表失败: {response}")
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "获取肥皂盒游戏列表失败")
            )
    except Exception as e:
        logger.error(f"获取肥皂盒游戏列表时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取肥皂盒游戏列表失败: {str(e)}")

@router.get("/xboot/detLog/show", response_model=Dict[str, Any])
async def get_chest_logs(
    chestId: str = Query(None, description="肥皂盒ID，不提供则获取所有记录"),
    pageNumber: int = Query(1, ge=1, description="页码，从1开始"),
    pageSize: int = Query(40, ge=1, le=100, description="每页显示条数"),
    sort: str = Query("createTime", description="排序字段"),
    order: str = Query("desc", description="排序方向：asc或desc"),
    client: SteamPyClient = Depends(get_steam_py_client)
):
    """
    获取肥皂盒抽取记录
    """
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据，请先登录。")
    
    try:
        if chestId:
            logger.info(f"获取特定肥皂盒记录: ID={chestId}, 页码={pageNumber}, 排序={sort}, 顺序={order}")
        else:
            logger.info(f"获取所有肥皂盒记录: 页码={pageNumber}, 排序={sort}, 顺序={order}")
            
        response = await client.get_chest_logs(
            chest_id=chestId,
            page_number=pageNumber,
            page_size=pageSize,
            sort=sort,
            order=order
        )
        
        if response.get("code") == 200:
            return response
        elif response.get("code") == 401:
            raise HTTPException(status_code=401, detail="认证失败或登录已过期，请重新登录。")
        else:
            logger.warning(f"获取肥皂盒记录失败: {response}")
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "获取肥皂盒记录失败")
            )
    except Exception as e:
        logger.error(f"获取肥皂盒记录时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取肥皂盒记录失败: {str(e)}") 