import json
from typing import List, Optional
from datetime import datetime
from pathlib import Path

from pywatcher.models.cookie_pool import MobileAccount, MobileCookiePool
from pywatcher.utils.file_io import read_json_file, write_json_file

class CookiePoolManager:
    """负责管理内存中的 MobileCookiePool 对象，并提供持久化方法。"""
    
    def __init__(self, pool: MobileCookiePool, file_path: str = "pywatcher/Mobile_cookie_pool.json"):
        """
        使用一个已加载和验证的 MobileCookiePool 对象进行初始化。
        
        Args:
            pool: 经过 Pydantic 验证的 MobileCookiePool 实例。
            file_path: 用于保存数据的目标 JSON 文件路径。
        """
        self._mobile_pool = pool
        self._file_path = file_path

    def save_pool(self):
        """将当前的账户池状态持久化到 JSON 文件。"""
        write_json_file(
            self._file_path,
            self._mobile_pool.model_dump(mode="json"),
        )

    def get_all_mobile_accounts(self) -> List[MobileAccount]:
        """获取所有移动端账户。"""
        return self._mobile_pool.accounts

    def get_mobile_account(self, account_id: str) -> Optional[MobileAccount]:
        """通过账户ID获取单个移动端账户。"""
        for acc in self._mobile_pool.accounts:
            if acc.id == account_id:
                return acc
        
        # 为了向后兼容，如果通过ID找不到，尝试通过account字段查找
        for acc in self._mobile_pool.accounts:
            if acc.account == account_id:
                return acc
                
        return None

    def add_mobile_account(self, account: MobileAccount) -> MobileAccount:
        """添加一个新的移动端账户，如果账户已存在则抛出异常。"""
        if self.get_mobile_account(account.id):
            raise ValueError(f"账户 '{account.id}' 已存在。")
        self._mobile_pool.accounts.append(account)
        # 注意：保存操作现在由调用方在API端点完成
        return account

    def remove_mobile_account(self, account_id: str) -> bool:
        """通过账户ID移除一个移动端账户。"""
        initial_count = len(self._mobile_pool.accounts)
        
        # 首先尝试通过id字段移除
        new_accounts = [acc for acc in self._mobile_pool.accounts if acc.id != account_id]
        
        # 如果没有移除任何账户，尝试通过account字段移除(向后兼容)
        if len(new_accounts) == initial_count:
            new_accounts = [acc for acc in self._mobile_pool.accounts if acc.account != account_id]
            
        self._mobile_pool.accounts = new_accounts
        return len(self._mobile_pool.accounts) < initial_count 