import json
from pathlib import Path
from typing import Any, Dict

def read_json_file(file_path: Path | str) -> Dict[str, Any]:
    """
    读取并解析一个 JSON 文件。
    如果文件不存在或内容为空，则返回一个空字典。
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
            if not content:
                return {}
            return json.loads(content)
    except (FileNotFoundError, json.JSONDecodeError):
        return {}

def write_json_file(file_path: Path | str, data: Any):
    """
    将数据以 JSON 格式写入文件。
    """
    with open(file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False) 