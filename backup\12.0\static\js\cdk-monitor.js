// 全局变量
let monitoredGames = [];
// 添加游戏到监控列表
function addGameToMonitor(gameId, gameName, gameAva) {
    const minIntervalInput = document.getElementById('cdk-min-interval');
    const maxIntervalInput = document.getElementById('cdk-max-interval');
    const minInterval = parseInt(minIntervalInput.value) || 5000;
    const maxInterval = parseInt(maxIntervalInput.value) || 15000;
    
    const newGame = {
        id: gameId,
        name: gameName,
        gameAva: gameAva,
        min_interval: minInterval,
        max_interval: maxInterval
    };
    
    fetch('/api/games/monitored', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(newGame)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('添加游戏到监控列表失败');
        }
        return response.json();
    })
    .then(game => {
        addCdkLog(`游戏 ${gameName} 已添加到监控列表`, false, true);
        loadMonitoredGames();
        
        // 更新按钮状态
        const btn = document.querySelector(`.add-to-monitor[data-game-id="${gameId}"]`);
        if (btn) {
            btn.textContent = '已添加';
            btn.classList.replace('btn-primary', 'btn-secondary');
            btn.disabled = true;
        }
    })
    .catch(error => {
        addCdkLog('添加游戏到监控列表失败: ' + error.message, true);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // 控制元素
    const toggleCdkMonitorBtn = document.getElementById('toggle-cdk-monitor');
    const addMonitorGameBtn = document.getElementById('add-monitor-game');
    const exportCdkGamesBtn = document.getElementById('export-cdk-games');
    const cdkMinIntervalInput = document.getElementById('cdk-min-interval');
    const cdkMaxIntervalInput = document.getElementById('cdk-max-interval');
    const cdkNotifyChangesCheckbox = document.getElementById('cdk-notify-changes');
    const cdkAutoOrderCheckbox = document.getElementById('cdk-auto-order');
    const cdkMonitoredGamesContainer = document.getElementById('cdk-monitored-games-container');
    const cdkMonitorLogsContainer = document.getElementById('cdk-monitor-logs');
    const clearCdkLogsBtn = document.getElementById('clear-cdk-logs');
    const cdkPriceChangesContainer = document.getElementById('cdk-price-changes');
    const clearCdkChangesBtn = document.getElementById('clear-cdk-changes');
    const saveCdkSettingsBtn = document.getElementById('save-cdk-settings');
    
    // 导出模态框元素
    const exportModal = document.getElementById('export-modal');
    const closeExportModalXBtn = document.getElementById('close-export-modal-x');
    const closeExportModalBtn = document.getElementById('close-export-modal');
    const exportContentTextarea = document.getElementById('export-content');
    const copyExportBtn = document.getElementById('copy-export-btn');
    
    // 初始化时，如果localStorage没有cdkAutoOrder设置，则默认设为true
    if (localStorage.getItem('cdkAutoOrder') === null) {
        localStorage.setItem('cdkAutoOrder', 'true');
    }
    
    // 确保全局自动下单复选框状态与localStorage同步
    if (cdkAutoOrderCheckbox) {
        cdkAutoOrderCheckbox.checked = localStorage.getItem('cdkAutoOrder') !== 'false';
        
        // 为自动下单复选框添加change事件处理器，变化时立即更新localStorage
        cdkAutoOrderCheckbox.addEventListener('change', function() {
            localStorage.setItem('cdkAutoOrder', this.checked);
            if (this.checked) {
                addCdkLog('自动下单已启用，当游戏价格达到目标时将自动下单', false, true);
            } else {
                addCdkLog('自动下单已禁用', false);
            }
        });
    }
    
    // 搜索模态框元素
    const addGameModal = document.getElementById('add-game-modal');
    const closeAddGameModalXBtn = document.getElementById('close-add-game-modal-x');
    const closeAddGameModalBtn = document.getElementById('close-add-game-modal');
    const gameSearchInput = document.getElementById('game-search-input');
    const searchGameBtn = document.getElementById('search-game-btn');
    const gameSearchResults = document.getElementById('game-search-results');
    
    // 目标价格模态框元素
    const targetPriceModal = document.getElementById('set-target-price-modal');
    const closeTargetPriceModalXBtn = document.getElementById('close-target-price-modal-x');
    const closeTargetPriceModalBtn = document.getElementById('close-target-price-modal');
    const saveTargetPriceBtn = document.getElementById('save-target-price-btn');
    const clearTargetPriceBtn = document.getElementById('clear-target-price-btn');
    const targetGameImg = document.getElementById('target-game-img');
    const targetGameName = document.getElementById('target-game-name');
    const targetPriceInput = document.getElementById('target-price-input');
    const priceNotificationType = document.getElementById('price-notification-type');
    
    // 监控状态
    let isCdkMonitoring = false;
    let cdkMonitorTimers = {};  // 存储每个游戏的定时器ID
    
    // 游戏监控数据缓存
    let monitoredGames = [];
    
    // 当前选中的游戏ID (用于设置目标价格)
    let currentGameId = null;
    
    // 初始化
    loadMonitoredGames();
    loadCdkPriceChanges();
    loadCdkSettings();
    
    // 绑定事件
    if (toggleCdkMonitorBtn) {
        toggleCdkMonitorBtn.addEventListener('click', function() {
            if (isCdkMonitoring) {
                stopCdkMonitoring();
            } else {
                startCdkMonitoring();
            }
        });
    }
    
    if (addMonitorGameBtn) {
        addMonitorGameBtn.addEventListener('click', function() {
            openAddGameModal();
        });
    }
    
    if (closeAddGameModalXBtn) {
        closeAddGameModalXBtn.addEventListener('click', function() {
            closeAddGameModal();
        });
    }
    
    if (closeAddGameModalBtn) {
        closeAddGameModalBtn.addEventListener('click', function() {
            closeAddGameModal();
        });
    }
    
    if (searchGameBtn) {
        searchGameBtn.addEventListener('click', function() {
            searchGames();
        });
    }
    
    if (gameSearchInput) {
        gameSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchGames();
            }
        });
    }
    
    if (clearCdkLogsBtn) {
        clearCdkLogsBtn.addEventListener('click', function() {
            cdkMonitorLogsContainer.innerHTML = '<div class="text-muted text-center py-4">监控记录已清空...</div>';
        });
    }
    
    if (clearCdkChangesBtn) {
        clearCdkChangesBtn.addEventListener('click', function() {
            cdkPriceChangesContainer.innerHTML = '<tr class="text-center text-muted"><td colspan="5">暂无价格变动记录...</td></tr>';
            localStorage.removeItem('cdkPriceChanges');
        });
    }
    
    if (saveCdkSettingsBtn) {
        saveCdkSettingsBtn.addEventListener('click', function() {
            saveCdkSettings();
        });
    }
    
    // 绑定导出模态框相关事件
    if (exportCdkGamesBtn) {
        exportCdkGamesBtn.addEventListener('click', function() {
            openExportModal();
        });
    }
    
    if (closeExportModalXBtn) {
        closeExportModalXBtn.addEventListener('click', function() {
            closeExportModal();
        });
    }
    
    if (closeExportModalBtn) {
        closeExportModalBtn.addEventListener('click', function() {
            closeExportModal();
        });
    }
    
    if (copyExportBtn) {
        copyExportBtn.addEventListener('click', function() {
            copyExportContent();
        });
    }
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === exportModal) {
            closeExportModal();
        }
    });
    
    // 目标价格模态框事件绑定
    if (closeTargetPriceModalXBtn) {
        closeTargetPriceModalXBtn.addEventListener('click', function() {
            closeTargetPriceModal();
        });
    }
    
    if (closeTargetPriceModalBtn) {
        closeTargetPriceModalBtn.addEventListener('click', function() {
            closeTargetPriceModal();
        });
    }
    
    if (saveTargetPriceBtn) {
        saveTargetPriceBtn.addEventListener('click', function() {
            saveTargetPrice();
        });
    }
    
    if (clearTargetPriceBtn) {
        clearTargetPriceBtn.addEventListener('click', function() {
            if (confirm('确定要清除此游戏的目标价格设置吗？')) {
                clearTargetPrice();
            }
        });
    }
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === targetPriceModal) {
            closeTargetPriceModal();
        }
    });
    
    // 加载监控游戏列表
    function loadMonitoredGames() {
        fetch('/api/games/monitored')
            .then(response => {
                if (!response.ok) {
                    throw new Error('获取监控游戏列表失败');
                }
                return response.json();
            })
            .then(games => {
                monitoredGames = games;
                renderMonitoredGames(games);
            })
            .catch(error => {
                addCdkLog('获取监控游戏列表失败: ' + error.message, true);
            });
    }
    
    // 渲染监控游戏列表
    function renderMonitoredGames(games) {
        if (!games || games.length === 0) {
            cdkMonitoredGamesContainer.innerHTML = '<div class="text-muted text-center py-4">暂无监控游戏，请从游戏搜索或肥皂盒详情页添加...</div>';
            return;
        }
        let html = '';
        games.forEach(game => {
            const hasTarget = game.target_price && game.target_price > 0;
            const priceReached = hasTarget && game.current_price && game.current_price <= game.target_price;
            
            const cardClass = `game-monitor-card ${hasTarget ? 'has-target' : ''} ${priceReached ? 'price-reached' : ''}`;
            
            html += `
            <div class="${cardClass}" data-game-id="${game.id}" data-game-name="${game.name}" data-game-ava="${game.gameAva || game.picUrl || ''}">
                <img src="${game.gameAva || '/static/images/default-game.png'}" class="game-avatar-lg" />
                ${hasTarget ? `<span class="target-price-badge ${priceReached ? 'reached' : ''}">¥${game.target_price}</span>` : ''}
                <div class="game-info-block">
                  <div class="game-title">${game.name}</div>
                  <div class="game-actions-row">
                    <button class="remove-game" data-game-id="${game.id}" title="移除">
                      <svg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' class='feather feather-trash-2'><polyline points='3 6 5 6 21 6'></polyline><path d='M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2'></path><line x1='10' y1='11' x2='10' y2='17'></line><line x1='14' y1='11' x2='14' y2='17'></line></svg>
                    </button>
                  </div>
                </div>
            </div>
            `;
        });
        cdkMonitoredGamesContainer.innerHTML = html;
        
        // 绑定移除按钮事件
        document.querySelectorAll('.remove-game').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡到卡片
                const gameId = this.getAttribute('data-game-id');
                removeMonitoredGame(gameId);
            });
        });
        
        // 绑定卡片点击事件，打开目标价格设置模态框
        document.querySelectorAll('.game-monitor-card').forEach(card => {
            card.addEventListener('click', function() {
                const gameId = this.getAttribute('data-game-id');
                const gameName = this.getAttribute('data-game-name');
                const gameAva = this.getAttribute('data-game-ava');
                openTargetPriceModal(gameId, gameName, gameAva);
            });
        });
    }
    
    // 搜索游戏
    function searchGames() {
        const searchTerm = gameSearchInput.value.trim();
        if (!searchTerm) {
            gameSearchResults.innerHTML = '<div class="text-warning text-center py-4">请输入游戏名称</div>';
            return;
        }
        
        gameSearchResults.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="sr-only">搜索中...</span></div></div>';
        
        fetch(`/api/games/search?query=${encodeURIComponent(searchTerm)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('搜索游戏失败');
                }
                return response.json();
            })
            .then(results => {
                if (!results || results.length === 0) {
                    gameSearchResults.innerHTML = '<div class="text-muted text-center py-4">未找到相关游戏</div>';
                    return;
                }
                
                let html = '<div class="list-group">';
                results.forEach(game => {
                    const isAlreadyMonitored = monitoredGames.some(mg => mg.id === game.id);
                    html += `
                    <div class="list-group-item d-flex align-items-center">
                        <img src="${game.picUrl || '/static/images/default-game.png'}" alt="${game.gameName}" class="game-avatar-small mr-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-0">${game.gameName}</h6>
                            <small class="text-muted">ID: ${game.id}</small>
                        </div>
                        <button class="btn btn-sm btn-${isAlreadyMonitored ? 'secondary' : 'primary'} add-to-monitor" 
                                data-game-id="${game.id}" 
                                data-game-name="${game.gameName}" 
                                data-game-ava="${game.gameAva || game.picUrl || ''}"
                                ${isAlreadyMonitored ? 'disabled' : ''}>
                            ${isAlreadyMonitored ? '已添加' : '添加监控'}
                        </button>
                    </div>`;
                });
                html += '</div>';
                
                gameSearchResults.innerHTML = html;
                
                // 绑定添加监控事件
                document.querySelectorAll('.add-to-monitor').forEach(btn => {
                    if (btn.getAttribute('disabled') !== null) return;
                    
                    btn.addEventListener('click', function() {
                        const gameId = this.getAttribute('data-game-id');
                        const gameName = this.getAttribute('data-game-name');
                        const gameAva = this.getAttribute('data-game-ava');
                        addGameToMonitor(gameId, gameName, gameAva);
                    });
                });
            })
            .catch(error => {
                gameSearchResults.innerHTML = `<div class="text-danger text-center py-4">搜索失败: ${error.message}</div>`;
            });
    }
    
    
    
    // 从监控列表移除游戏
    function removeMonitoredGame(gameId, skipConfirm = false) {
        if (!skipConfirm && !confirm('确定要从监控列表中移除此游戏吗？')) {
            return;
        }
        
        fetch(`/api/games/monitored/${gameId}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('从监控列表中移除游戏失败');
            }
            
            // 如果正在监控，先停止该游戏的监控
            if (cdkMonitorTimers[gameId]) {
                clearTimeout(cdkMonitorTimers[gameId]);
                delete cdkMonitorTimers[gameId];
            }
            
            // 从本地缓存中移除
            monitoredGames = monitoredGames.filter(game => game.id !== gameId);
            
            // 重新渲染整个列表，而不是单独删除元素
            renderMonitoredGames(monitoredGames);
            
            addCdkLog(`游戏ID ${gameId} 已从监控列表中移除`, false);
        })
        .catch(error => {
            addCdkLog('从监控列表中移除游戏失败: ' + error.message, true);
        });
    }
    
    
    // 添加监控日志
    function addCdkLog(message, isError = false, isSuccess = false) {
        // 如果有默认消息，先清除它
        const defaultMsg = cdkMonitorLogsContainer.querySelector('.text-muted');
        if (defaultMsg) {
            cdkMonitorLogsContainer.innerHTML = '';
        }

        const logEntry = document.createElement('div');
        logEntry.className = isError ? 'log-error' : (isSuccess ? 'log-success' : 'log-normal');
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${message}`;
        
        cdkMonitorLogsContainer.appendChild(logEntry);
        cdkMonitorLogsContainer.scrollTop = cdkMonitorLogsContainer.scrollHeight;
    }
    
    // 开始CDK监控
    function startCdkMonitoring() {
        if (isCdkMonitoring) return;
        
        if (monitoredGames.length === 0) {
            addCdkLog('没有可监控的游戏，请先添加游戏', true);
            return;
        }
        
        // 获取设置值
        const minInterval = parseInt(localStorage.getItem('cdkMinInterval') || cdkMinIntervalInput?.value || 5000);
        const maxInterval = parseInt(localStorage.getItem('cdkMaxInterval') || cdkMaxIntervalInput?.value || 15000);
        
        isCdkMonitoring = true;
        toggleCdkMonitorBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-stop-circle mr-1"><circle cx="12" cy="12" r="10"></circle><rect x="9" y="9" width="6" height="6"></rect></svg>停止监控`;
        toggleCdkMonitorBtn.classList.replace('btn-outline-success', 'btn-outline-danger');
        
        addCdkLog('开始CDK监控...', false, true);
        
        // 开始监控所有游戏
        monitoredGames.forEach(game => {
            startGameMonitoring(game);
        });
    }
    
    // 停止CDK监控
    function stopCdkMonitoring() {
        isCdkMonitoring = false;
        toggleCdkMonitorBtn.innerHTML = '<i class="icon-play mr-1"></i>开始监控';
        toggleCdkMonitorBtn.classList.replace('btn-outline-danger', 'btn-outline-success');
        
        // 清除所有定时器
        Object.keys(cdkMonitorTimers).forEach(gameId => {
            clearTimeout(cdkMonitorTimers[gameId]);
            delete cdkMonitorTimers[gameId];
        });
        
        addCdkLog('CDK监控已停止', false);
    }
    
    // 开始监控单个游戏
    function startGameMonitoring(game) {
        // 如果已经在监控中，先停止
        if (cdkMonitorTimers[game.id]) {
            clearTimeout(cdkMonitorTimers[game.id]);
            delete cdkMonitorTimers[game.id];
        }
        
        // 获取游戏自定义的监控间隔，如果没有则使用默认值
        const minInterval = game.min_interval || parseInt(localStorage.getItem('cdkMinInterval') || cdkMinIntervalInput?.value || 5000);
        const maxInterval = game.max_interval || parseInt(localStorage.getItem('cdkMaxInterval') || cdkMaxIntervalInput?.value || 15000);
        
        // 随机生成本次监控间隔
        const interval = Math.floor(Math.random() * (maxInterval - minInterval + 1)) + minInterval;
        
        // 开始监控
        const monitorTask = async () => {
            if (!isCdkMonitoring) return;
            
            try {
                await monitorGameInfo(game);
            } catch (error) {
                addCdkLog(`监控游戏 ${game.name} 时出错: ${error.message}`, true);
            }
            
            // 如果仍在监控中，继续下一次监控
            if (isCdkMonitoring) {
                // 随机生成下一次监控间隔
                const nextInterval = Math.floor(Math.random() * (maxInterval - minInterval + 1)) + minInterval;
                cdkMonitorTimers[game.id] = setTimeout(monitorTask, nextInterval);
            }
        };
        
        // 立即执行一次，然后设置定时器
        monitorTask();
    }
    
    // 监控单个游戏的信息
    async function monitorGameInfo(game) {
        try {
            // 发起第一个请求
            const infoPromise = fetch(`/api/games/${game.id}/info`);
            
            // 第一次监控时，同时发起第二个请求
            let sellersPromise = null;
            if (!game.last_update_time) {
                sellersPromise = fetch(`/api/games/${game.id}/sellers?pageSize=1`);
            }
            
            // 等待第一个请求完成
            const response = await infoPromise;
            if (!response.ok) {
                throw new Error(`获取游戏信息失败: ${response.status}`);
            }
            
            const gameInfo = await response.json();
            const currentUpdateTime = gameInfo.updateTime;
            
            // 如果之前有记录的updateTime
            if (game.last_update_time) {
                // 检查updateTime是否有变化
                if (currentUpdateTime !== game.last_update_time) {
                    addCdkLog(`检测到游戏 ${game.name} 信息更新，正在获取最新价格...`, false, true);
                    
                    // 获取最新价格
                    const sellersResponse = await fetch(`/api/games/${game.id}/sellers?pageSize=1`);
                    if (!sellersResponse.ok) {
                        throw new Error(`获取游戏卖家列表失败: ${sellersResponse.status}`);
                    }
                    
                    const sellersData = await sellersResponse.json();
                    if (sellersData && sellersData.length > 0) {
                        // 找出最低价格
                        const lowestPrice = Math.min(...sellersData
                            .filter(seller => seller.keyPrice && typeof seller.keyPrice === 'number')
                            .map(seller => seller.keyPrice));
                            
                        // 记录价格更新到日志
                        addCdkLog(`游戏 ${game.name} 价格已更新: ¥${lowestPrice}`, false, true);
                        
                        // 更新游戏的当前价格
                        await fetch(`/api/games/monitored/${game.id}`, {
                            method: 'PATCH',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ 
                                last_update_time: currentUpdateTime,
                                current_price: lowestPrice 
                            })
                        });
                        
                        // 更新本地缓存
                        const gameIndex = monitoredGames.findIndex(g => g.id === game.id);
                        if (gameIndex !== -1) {
                            monitoredGames[gameIndex].last_update_time = currentUpdateTime;
                            monitoredGames[gameIndex].current_price = lowestPrice;
                            
                            // 检查是否达到了目标价格
                            if (monitoredGames[gameIndex].target_price && 
                                lowestPrice <= monitoredGames[gameIndex].target_price) {
                                // 达到目标价格
                                addCdkLog(`游戏 ${game.name} 达到目标价格！当前价格: ${lowestPrice}, 目标价格: ${monitoredGames[gameIndex].target_price}`, false, true);
                                
                                // 如果设置了通知，则发送通知
                                if (monitoredGames[gameIndex].price_notification_type === 'browser') {
                                    sendPriceNotification(monitoredGames[gameIndex], lowestPrice);
                                }
                                
                                // 如果启用了自动下单，执行下单
                                // 检查是否已经尝试过对该卖家下单（通过记录已尝试下单的卖家ID）
                                if (localStorage.getItem('cdkAutoOrder') !== 'false') {
                                    
                                    // 确保有卖家数据且包含saleId
                                    if (sellersData && sellersData.length > 0 && sellersData[0].saleId) {
                                        const saleId = sellersData[0].saleId;
                                        
                                        // 检查是否已对此卖家尝试下单
                                        const orderedSaleIds = JSON.parse(localStorage.getItem(`ordered_${game.id}`) || '[]');
                                        
                                        if (!orderedSaleIds.includes(saleId)) {
                                            addCdkLog(`游戏 ${game.name} 已启用自动下单，正在下单...`, false, true);
                                            
                                            // 记录此卖家ID已尝试下单
                                            orderedSaleIds.push(saleId);
                                            localStorage.setItem(`ordered_${game.id}`, JSON.stringify(orderedSaleIds));
                                            
                                            await placeCdkOrder(game.name, saleId);
                                        } else {
                                            addCdkLog(`游戏 ${game.name} 已经尝试对卖家ID ${saleId} 下单，不再重复下单`, false);
                                        }
                                    } else {
                                        addCdkLog(`游戏 ${game.name} 自动下单失败：无有效的卖家ID`, true);
                                    }
                                }
                                
                                // 重新渲染游戏列表，显示达标标记
                                renderMonitoredGames(monitoredGames);
                            }
                        }
                    } else {
                        addCdkLog(`游戏 ${game.name} 暂无卖家信息`, false);
                    }
                }
            } else {
                // 首次监控，记录updateTime并获取当前价格
                addCdkLog(`首次监控游戏 ${game.name}...`, false, true);
                
                // 已经并行发起了请求，现在等待结果
                let sellersResponse, sellersData;
                if (sellersPromise) {
                    sellersResponse = await sellersPromise;
                    if (!sellersResponse.ok) {
                        throw new Error(`获取游戏卖家列表失败: ${sellersResponse.status}`);
                    }
                    sellersData = await sellersResponse.json();
                }
                let lowestPrice = 0;
                
                if (sellersData && sellersData.length > 0) {
                    // 找出最低价格
                    lowestPrice = Math.min(...sellersData
                        .filter(seller => seller.keyPrice && typeof seller.keyPrice === 'number')
                        .map(seller => seller.keyPrice));
                        
                    addCdkLog(`游戏 ${game.name} 初始价格: ¥${lowestPrice}`, false, true);
                }
                
                // 保存初始信息
                await fetch(`/api/games/monitored/${game.id}`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        last_update_time: currentUpdateTime,
                        current_price: lowestPrice
                    })
                });
                
                // 更新本地缓存
                const gameIndex = monitoredGames.findIndex(g => g.id === game.id);
                if (gameIndex !== -1) {
                    monitoredGames[gameIndex].last_update_time = currentUpdateTime;
                    monitoredGames[gameIndex].current_price = lowestPrice;
                    
                    // 检查是否达到了目标价格
                    if (monitoredGames[gameIndex].target_price && 
                        lowestPrice <= monitoredGames[gameIndex].target_price) {
                        // 达到目标价格
                        addCdkLog(`游戏 ${game.name} 达到目标价格！当前价格: ${lowestPrice}, 目标价格: ${monitoredGames[gameIndex].target_price}`, false, true);
                        
                        // 如果设置了通知，则发送通知
                        if (monitoredGames[gameIndex].price_notification_type === 'browser') {
                            sendPriceNotification(monitoredGames[gameIndex], lowestPrice);
                        }
                        
                        // 如果启用了自动下单，执行下单
                        if (localStorage.getItem('cdkAutoOrder') !== 'false') {
                            // 确保有卖家数据且包含saleId
                            if (sellersData && sellersData.length > 0 && sellersData[0].saleId) {
                                const saleId = sellersData[0].saleId;
                                
                                // 检查是否已对此卖家尝试下单
                                const orderedSaleIds = JSON.parse(localStorage.getItem(`ordered_${game.id}`) || '[]');
                                
                                if (!orderedSaleIds.includes(saleId)) {
                                    addCdkLog(`游戏 ${game.name} 已启用自动下单，正在下单...`, false, true);
                                    
                                    // 记录此卖家ID已尝试下单
                                    orderedSaleIds.push(saleId);
                                    localStorage.setItem(`ordered_${game.id}`, JSON.stringify(orderedSaleIds));
                                    
                                    await placeCdkOrder(game.name, saleId);
                                } else {
                                    addCdkLog(`游戏 ${game.name} 已经尝试对卖家ID ${saleId} 下单，不再重复下单`, false);
                                }
                            } else {
                                addCdkLog(`游戏 ${game.name} 自动下单失败：无有效的卖家ID`, true);
                            }
                        }
                        
                        // 重新渲染游戏列表，显示达标标记
                        renderMonitoredGames(monitoredGames);
                    }
                }
            }
        } catch (error) {
            addCdkLog(`监控游戏 ${game.name} 出错: ${error.message}`, true);
            throw error; // 重新抛出错误以便上层处理
        }
    }
    
    // 发送价格通知
    function sendPriceNotification(game, currentPrice) {
        if (!('Notification' in window)) return;
        
        if (Notification.permission === 'granted') {
            showPriceNotification(game, currentPrice);
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    showPriceNotification(game, currentPrice);
                }
            });
        }
    }
    
    // 显示价格通知
    function showPriceNotification(game, currentPrice) {
        const title = `${game.name} 价格达标`;
        const options = {
            body: `当前价格: ¥${currentPrice}\n目标价格: ¥${game.target_price}`,
            icon: game.gameAva || '/static/images/default-game.png'
        };
        
        const notification = new Notification(title, options);
        notification.onclick = function() {
            window.focus();
            if (targetPriceModal && targetPriceModal.style.display !== 'flex') {
                openTargetPriceModal(game.id, game.name, game.gameAva);
            }
        };
    }
    
    // 下单购买CDK
    async function placeCdkOrder(gameName, saleId) {
        try {
            // 直接从localStorage获取下单配置
            const isMainAccount = localStorage.getItem('cdkUseMainAccount') !== 'false'; // 默认主账号
            const payType = localStorage.getItem('cdkPayType') || 'AU';
            const walletFlag = localStorage.getItem('cdkUseBalance');

            // 准备下单参数
            const params = new URLSearchParams({
                saleId: saleId,
                payType: payType,
                promoCodeId: '',
                walletFlag: walletFlag,
                version: 'v1',
                is_main_account: isMainAccount.toString(),
                orderType: 'cdk',
                gameName: gameName
            });

            // 显示下单中提示
            addCdkLog(`正在处理游戏 ${gameName} 的下单请求...`, false, true);

            // 发起下单请求
            const orderResponse = await fetch(`/api/xboot/steamKeyOrder/payOrder?${params.toString()}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!orderResponse.ok) {
                const errorData = await orderResponse.json();
                throw new Error(errorData.detail || '下单失败');
            }

            const orderResult = await orderResponse.json();

            if (orderResult.success) {
                // 获取支付链接
                const alipayUrl = orderResult.result?.alipay || '';
                const payPrice = orderResult.result?.payPrice || '';

                addCdkLog(`游戏 ${gameName} 下单成功，价格: ¥${payPrice}`, false, true);
                addCdkLog(`支付链接已发送至邮箱，请查收`, false, true);

                // 下单成功后继续监控，不再从监控列表中移除
                addCdkLog(`游戏 ${gameName} 下单成功，继续监控中...`, false, true);
            } else {
                addCdkLog(`游戏 ${gameName} 下单失败: ${orderResult.message || '未知错误'}`, true);
            }
        } catch (error) {
            addCdkLog(`游戏 ${gameName} 下单过程中出错: ${error.message}`, true);
        }
    }
    
    // 添加价格变动记录 - 简化版，只更新UI不记录历史
    function addPriceChange(game, newPrice) {
        // 直接获取价格变化
        let oldPrice = 0;
        const gameIndex = monitoredGames.findIndex(g => g.id === game.id);
        if (gameIndex !== -1 && monitoredGames[gameIndex].current_price) {
            oldPrice = monitoredGames[gameIndex].current_price;
        }
        
        // 创建新的价格变动记录对象
        const timestamp = new Date().toLocaleString('zh-CN');
        const newChange = {
            gameId: game.id,
            gameName: game.name,
            oldPrice: oldPrice,
            newPrice: newPrice,
            timestamp: timestamp
        };
        
        // 添加到价格变化表格的顶部
        addPriceChangeToUI(newChange);
    }
    
    // 加载价格变动记录
    function loadCdkPriceChanges() {
        const storedChanges = localStorage.getItem('cdkPriceChanges');
        if (storedChanges) {
            try {
                const priceChanges = JSON.parse(storedChanges);
                renderPriceChanges(priceChanges);
            } catch (e) {
                console.error('解析价格变动记录失败', e);
            }
        }
    }
    
    // 渲染价格变动记录
    function renderPriceChanges(changes) {
        if (!changes || changes.length === 0) {
            cdkPriceChangesContainer.innerHTML = '<tr class="text-center text-muted"><td colspan="5">暂无价格变动记录...</td></tr>';
            return;
        }
        
        let html = '';
        changes.forEach((change, index) => {
            const priceChange = change.newPrice - change.oldPrice;
            const priceChangeClass = priceChange > 0 ? 'text-danger' : (priceChange < 0 ? 'text-success' : '');
            
            html += `
            <tr>
                <td>${change.gameName}</td>
                <td>${change.oldPrice.toFixed(2)}</td>
                <td class="${priceChangeClass}">${change.newPrice.toFixed(2)}</td>
                <td>${change.timestamp}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary view-details-btn" data-game-id="${change.gameId}" title="查看详情">
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-eye"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                    </button>
                </td>
            </tr>`;
        });
        
        cdkPriceChangesContainer.innerHTML = html;
        
        // 绑定查看详情按钮事件
        document.querySelectorAll('.view-details-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const gameId = this.getAttribute('data-game-id');
                viewGameDetails(gameId);
            });
        });
    }
    
    // 添加单条价格变化记录到UI顶部
    function addPriceChangeToUI(change) {
        // 如果表格为空，清除"暂无记录"消息
        const emptyMsg = cdkPriceChangesContainer.querySelector('.text-muted');
        if (emptyMsg && emptyMsg.parentElement.tagName === 'TR' && emptyMsg.parentElement.cells.length === 1) {
            cdkPriceChangesContainer.innerHTML = '';
        }
        
        // 创建新的表格行
        const row = document.createElement('tr');
        
        // 计算价格变化
        const priceChange = change.newPrice - change.oldPrice;
        const priceChangeClass = priceChange > 0 ? 'text-danger' : (priceChange < 0 ? 'text-success' : '');
        
        // 设置行内容
        row.innerHTML = `
            <td>${change.gameName}</td>
            <td>${change.oldPrice.toFixed(2)}</td>
            <td class="${priceChangeClass}">${change.newPrice.toFixed(2)}</td>
            <td>${change.timestamp}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary view-details-btn" data-game-id="${change.gameId}" title="查看详情">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-eye"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                </button>
            </td>
        `;
        
        // 添加到表格顶部
        if (cdkPriceChangesContainer.firstChild) {
            cdkPriceChangesContainer.insertBefore(row, cdkPriceChangesContainer.firstChild);
        } else {
            cdkPriceChangesContainer.appendChild(row);
        }
        
        // 绑定查看详情按钮事件
        const viewBtn = row.querySelector('.view-details-btn');
        if (viewBtn) {
            viewBtn.addEventListener('click', function() {
                viewGameDetails(change.gameId);
            });
        }
        
        // 限制显示的行数，最多显示50条
        while (cdkPriceChangesContainer.children.length > 50) {
            cdkPriceChangesContainer.removeChild(cdkPriceChangesContainer.lastChild);
        }
    }
    
    // 查看游戏详情
    function viewGameDetails(gameId) {
        // 打开游戏详情页
        window.open(`/game/${gameId}`, '_blank');
    }
    
    // 打开添加游戏模态框
    function openAddGameModal() {
        if (addGameModal) {
            addGameModal.style.display = 'block';
            gameSearchInput.value = '';
            gameSearchResults.innerHTML = '<div class="text-muted text-center py-4">请输入游戏名称进行搜索...</div>';
            
            // 自动聚焦搜索框
            setTimeout(() => {
                gameSearchInput.focus();
            }, 100);
        }
    }
    
    // 关闭添加游戏模态框
    function closeAddGameModal() {
        if (addGameModal) {
            addGameModal.style.display = 'none';
        }
    }
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target === addGameModal) {
            closeAddGameModal();
        }
    });
    
    // 添加右键菜单上下文支持
    document.addEventListener('contextmenu', function(e) {
        // 检查是否在游戏卡片上右键
        let target = e.target;
        while (target && !target.classList.contains('game-card')) {
            target = target.parentElement;
        }
        
        if (target && target.classList.contains('game-card')) {
            e.preventDefault();
            
            const gameId = target.getAttribute('data-game-id');
            const gameName = target.getAttribute('data-game-name');
            const gameAva = target.getAttribute('data-game-ava');
            
            if (gameId && gameName) {
                // 创建右键菜单
                const contextMenu = document.createElement('div');
                contextMenu.className = 'context-menu';
                contextMenu.style.position = 'absolute';
                contextMenu.style.left = e.pageX + 'px';
                contextMenu.style.top = e.pageY + 'px';
                contextMenu.style.background = 'white';
                contextMenu.style.border = '1px solid #ccc';
                contextMenu.style.boxShadow = '2px 2px 5px rgba(0,0,0,0.2)';
                contextMenu.style.zIndex = '1000';
                contextMenu.style.padding = '5px 0';
                contextMenu.innerHTML = `
                    <div class="context-menu-item" id="add-to-monitor">添加到CDK监控</div>
                `;
                
                document.body.appendChild(contextMenu);
                
                // 添加菜单项点击事件
                document.getElementById('add-to-monitor').addEventListener('click', function() {
                    addGameToMonitor(gameId, gameName, gameAva);
                    document.body.removeChild(contextMenu);
                });
                
                // 点击其他地方关闭菜单
                document.addEventListener('click', function closeMenu() {
                    if (document.body.contains(contextMenu)) {
                        document.body.removeChild(contextMenu);
                    }
                    document.removeEventListener('click', closeMenu);
                });
            }
        }
    });
    
    // 打开目标价格设置模态框
    function openTargetPriceModal(gameId, gameName, gameAva) {
        if (targetPriceModal) {
            targetPriceModal.style.display = 'flex';
            
            // 设置当前游戏ID
            currentGameId = gameId;
            
            // 设置游戏信息
            if (targetGameImg) {
                targetGameImg.src = gameAva || '/static/images/default-game.png';
            }
            if (targetGameName) {
                targetGameName.textContent = gameName;
            }
            
            // 获取当前游戏的设置
            const game = monitoredGames.find(g => g.id === gameId);
            if (game) {
                // 设置目标价格输入框
                if (targetPriceInput && game.target_price) {
                    targetPriceInput.value = game.target_price;
                } else if (targetPriceInput) {
                    targetPriceInput.value = '';
                }
                
                // 设置通知类型
                if (priceNotificationType && game.price_notification_type) {
                    priceNotificationType.value = game.price_notification_type;
                } else if (priceNotificationType) {
                    priceNotificationType.value = 'highlight';
                }
            } else {
                // 清空表单
                if (targetPriceInput) {
                    targetPriceInput.value = '';
                }
                if (priceNotificationType) {
                    priceNotificationType.value = 'highlight';
                }
            }
            
            // 自动聚焦价格输入框
            setTimeout(() => {
                if (targetPriceInput) {
                    targetPriceInput.focus();
                }
            }, 100);
        }
    }
    
    // 关闭目标价格设置模态框
    function closeTargetPriceModal() {
        if (targetPriceModal) {
            targetPriceModal.style.display = 'none';
            // 不再将currentGameId设为null，保持选中游戏的状态
        }
    }
    
    // 保存目标价格设置
    function saveTargetPrice() {
        if (!currentGameId) {
            addCdkLog('无法保存：游戏ID为空', true);
            return;
        }
        
        const targetPrice = parseFloat(targetPriceInput.value);
        if (isNaN(targetPrice) || targetPrice <= 0) {
            alert('请输入有效的目标价格');
            targetPriceInput.focus();
            return;
        }
        
        const notificationType = priceNotificationType.value;
        
        // 保存到服务器
        fetch(`/api/games/monitored/${currentGameId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                target_price: targetPrice,
                price_notification_type: notificationType
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('更新目标价格失败');
            }
            return response.json();
        })
        .then(result => {
            // 更新本地缓存
            const gameIndex = monitoredGames.findIndex(game => game.id === currentGameId);
            if (gameIndex !== -1) {
                monitoredGames[gameIndex].target_price = targetPrice;
                monitoredGames[gameIndex].price_notification_type = notificationType;
            }
            
            // 重新渲染游戏列表
            renderMonitoredGames(monitoredGames);
            
            // 关闭模态框
            closeTargetPriceModal();
            
            // 添加日志
            addCdkLog(`游戏ID ${currentGameId} 的目标价格已设置为 ${targetPrice}`, false, true);
        })
        .catch(error => {
            addCdkLog('设置目标价格失败: ' + error.message, true);
        });
    }
    
    // 清除目标价格设置
    function clearTargetPrice() {
        if (!currentGameId) {
            addCdkLog('无法清除：游戏ID为空', true);
            return;
        }
        
        // 保存到服务器
        fetch(`/api/games/monitored/${currentGameId}`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                target_price: null,
                price_notification_type: null
                // 不再处理auto_order字段
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('清除目标价格失败');
            }
            return response.json();
        })
        .then(result => {
            // 更新本地缓存
            const gameIndex = monitoredGames.findIndex(game => game.id === currentGameId);
            if (gameIndex !== -1) {
                delete monitoredGames[gameIndex].target_price;
                delete monitoredGames[gameIndex].price_notification_type;
                // 不再删除auto_order属性
            }
            
            // 重新渲染游戏列表
            renderMonitoredGames(monitoredGames);
            
            // 关闭模态框
            closeTargetPriceModal();
            
            // 添加日志
            addCdkLog(`游戏ID ${currentGameId} 的目标价格已清除`, false);
        })
        .catch(error => {
            addCdkLog('清除目标价格失败: ' + error.message, true);
        });
    }
    
    // 加载CDK监控设置
    function loadCdkSettings() {
        // 从localStorage加载设置
        const minInterval = localStorage.getItem('cdkMinInterval') || 5000;
        const maxInterval = localStorage.getItem('cdkMaxInterval') || 15000;
        // 将自动下单默认为打开状态
        const autoOrder = localStorage.getItem('cdkAutoOrder') !== 'false'; // 默认为 true
        
        // 设置表单值
        if (cdkMinIntervalInput) cdkMinIntervalInput.value = minInterval;
        if (cdkMaxIntervalInput) cdkMaxIntervalInput.value = maxInterval;
        if (cdkAutoOrderCheckbox) cdkAutoOrderCheckbox.checked = autoOrder;
    }
    
    // 保存CDK监控设置
    function saveCdkSettings() {
        // 获取表单值
        const minInterval = cdkMinIntervalInput ? cdkMinIntervalInput.value : 5000;
        const maxInterval = cdkMaxIntervalInput ? cdkMaxIntervalInput.value : 15000;
        const autoOrder = cdkAutoOrderCheckbox ? cdkAutoOrderCheckbox.checked : false;
        
        // 验证设置
        if (parseInt(minInterval) > parseInt(maxInterval)) {
            addCdkLog('设置错误: 最小间隔不能大于最大间隔', true);
            return;
        }
        
        // 保存到localStorage
        localStorage.setItem('cdkMinInterval', minInterval);
        localStorage.setItem('cdkMaxInterval', maxInterval);
        localStorage.setItem('cdkAutoOrder', autoOrder);
        
        // 显示保存成功消息
        addCdkLog('监控设置已保存', false, true);
        if (autoOrder) {
            addCdkLog('自动下单已启用，当游戏价格达到目标时将自动下单', false, true);
        } else {
            addCdkLog('自动下单已禁用', false);
        }
    }

    // 打开导出模态框
    function openExportModal() {
        // 准备导出数据
        if (!monitoredGames || monitoredGames.length === 0) {
            alert('当前没有监控游戏可导出');
            return;
        }
        
        // 格式化导出的数据，只包含id, name 和 threshold (target_price)
        const exportData = monitoredGames.map(game => {
            return {
                name: game.name,
                id: game.id,
                threshold: game.target_price || 0
            };
        });
        
        // 格式化为带缩进的JSON字符串，使其易于阅读
        const exportText = JSON.stringify(exportData, null, 2);
        
        // 设置文本区域内容
        exportContentTextarea.value = exportText;
        
        // 显示模态框
        exportModal.style.display = 'block';
    }
    
    // 关闭导出模态框
    function closeExportModal() {
        exportModal.style.display = 'none';
    }
    
    // 复制导出内容到剪贴板
    function copyExportContent() {
        exportContentTextarea.select();
        document.execCommand('copy');
        
        // 显示复制成功提示
        const originalText = copyExportBtn.textContent;
        copyExportBtn.textContent = '已复制!';
        copyExportBtn.classList.add('btn-success');
        copyExportBtn.classList.remove('btn-primary');
        
        // 2秒后恢复按钮状态
        setTimeout(() => {
            copyExportBtn.textContent = originalText;
            copyExportBtn.classList.remove('btn-success');
            copyExportBtn.classList.add('btn-primary');
        }, 2000);
    }
}); 

// 添加防抖函数，用于优化频繁触发的事件
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// 优化DOM元素缓存
const domElements = {};
function getElement(selector) {
    if (!domElements[selector]) {
        domElements[selector] = document.querySelector(selector);
    }
    return domElements[selector];
}

// 优化监控游戏信息函数 - 保留原有逻辑，只添加性能优化
async function monitorGameInfo(game) {
    try {
        // 发起第一个请求
        const infoPromise = fetch(`/api/games/${game.id}/info`);
        
        // 第一次监控时，同时发起第二个请求
        let sellersPromise = null;
        if (!game.last_update_time) {
            sellersPromise = fetch(`/api/games/${game.id}/sellers?pageSize=1`);
        }
        
        // 等待第一个请求完成
        const response = await infoPromise;
        if (!response.ok) {
            throw new Error(`获取游戏信息失败: ${response.status}`);
        }
        
        const gameInfo = await response.json();
        const currentUpdateTime = gameInfo.updateTime;
        
        // 原有逻辑保持不变...
        if (game.last_update_time) {
            // 检查updateTime是否有变化
            if (currentUpdateTime !== game.last_update_time) {
                addCdkLog(`检测到游戏 ${game.name} 信息更新，正在获取最新价格...`, false, true);
                
                // 获取最新价格
                const sellersResponse = await fetch(`/api/games/${game.id}/sellers?pageSize=1`);
                if (!sellersResponse.ok) {
                    throw new Error(`获取游戏卖家列表失败: ${sellersResponse.status}`);
                }
                
                const sellersData = await sellersResponse.json();
                if (sellersData && sellersData.length > 0) {
                    // 找出最低价格
                    const lowestPrice = Math.min(...sellersData
                        .filter(seller => seller.keyPrice && typeof seller.keyPrice === 'number')
                        .map(seller => seller.keyPrice));
                        
                    // 记录价格更新到日志
                    addCdkLog(`游戏 ${game.name} 价格已更新: ¥${lowestPrice}`, false, true);
                    
                    // 使用批量更新减少网络请求
                    await updateGameData(game.id, {
                        last_update_time: currentUpdateTime,
                        current_price: lowestPrice
                    });
                    
                    // 更新本地缓存
                    const gameIndex = monitoredGames.findIndex(g => g.id === game.id);
                    if (gameIndex !== -1) {
                        monitoredGames[gameIndex].last_update_time = currentUpdateTime;
                        monitoredGames[gameIndex].current_price = lowestPrice;
                        
                        // 检查是否达到了目标价格
                        if (monitoredGames[gameIndex].target_price && 
                            lowestPrice <= monitoredGames[gameIndex].target_price) {
                            // 达到目标价格
                            addCdkLog(`游戏 ${game.name} 达到目标价格！当前价格: ${lowestPrice}, 目标价格: ${monitoredGames[gameIndex].target_price}`, false, true);
                            
                            // 如果设置了通知，则发送通知
                            if (monitoredGames[gameIndex].price_notification_type === 'browser') {
                                sendPriceNotification(monitoredGames[gameIndex], lowestPrice);
                            }
                            
                            // 如果启用了自动下单，执行下单
                            if (localStorage.getItem('cdkAutoOrder') !== 'false') {
                                
                                // 确保有卖家数据且包含saleId
                                if (sellersData && sellersData.length > 0 && sellersData[0].saleId) {
                                    const saleId = sellersData[0].saleId;
                                    
                                    // 检查是否已对此卖家尝试下单
                                    const orderedSaleIds = JSON.parse(localStorage.getItem(`ordered_${game.id}`) || '[]');
                                    
                                    if (!orderedSaleIds.includes(saleId)) {
                                        addCdkLog(`游戏 ${game.name} 已启用自动下单，正在下单...`, false, true);
                                        
                                        // 记录此卖家ID已尝试下单
                                        orderedSaleIds.push(saleId);
                                        localStorage.setItem(`ordered_${game.id}`, JSON.stringify(orderedSaleIds));
                                        
                                        await placeCdkOrder(game.name, saleId);
                                    } else {
                                        addCdkLog(`游戏 ${game.name} 已经尝试对卖家ID ${saleId} 下单，不再重复下单`, false);
                                    }
                                } else {
                                    addCdkLog(`游戏 ${game.name} 自动下单失败：无有效的卖家ID`, true);
                                }
                            }
                            
                            // 使用requestAnimationFrame优化渲染
                            requestAnimationFrame(() => {
                                renderMonitoredGames(monitoredGames);
                            });
                        }
                    }
                } else {
                    addCdkLog(`游戏 ${game.name} 暂无卖家信息`, false);
                }
            }
        } else {
            // 首次监控，记录updateTime并获取当前价格
            addCdkLog(`首次监控游戏 ${game.name}...`, false, true);
            
            // 已经并行发起了请求，现在等待结果
            let sellersResponse, sellersData;
            if (sellersPromise) {
                sellersResponse = await sellersPromise;
                if (!sellersResponse.ok) {
                    throw new Error(`获取游戏卖家列表失败: ${sellersResponse.status}`);
                }
                sellersData = await sellersResponse.json();
            }
            let lowestPrice = 0;
            
            if (sellersData && sellersData.length > 0) {
                // 找出最低价格
                lowestPrice = Math.min(...sellersData
                    .filter(seller => seller.keyPrice && typeof seller.keyPrice === 'number')
                    .map(seller => seller.keyPrice));
                    
                addCdkLog(`游戏 ${game.name} 初始价格: ¥${lowestPrice}`, false, true);
            }
            
            // 保存初始信息
            await updateGameData(game.id, {
                last_update_time: currentUpdateTime,
                current_price: lowestPrice
            });
            
            // 更新本地缓存
            const gameIndex = monitoredGames.findIndex(g => g.id === game.id);
            if (gameIndex !== -1) {
                monitoredGames[gameIndex].last_update_time = currentUpdateTime;
                monitoredGames[gameIndex].current_price = lowestPrice;
                
                // 检查是否达到了目标价格
                if (monitoredGames[gameIndex].target_price && 
                    lowestPrice <= monitoredGames[gameIndex].target_price) {
                    // 达到目标价格
                    addCdkLog(`游戏 ${game.name} 达到目标价格！当前价格: ${lowestPrice}, 目标价格: ${monitoredGames[gameIndex].target_price}`, false, true);
                    
                    // 如果设置了通知，则发送通知
                    if (monitoredGames[gameIndex].price_notification_type === 'browser') {
                        sendPriceNotification(monitoredGames[gameIndex], lowestPrice);
                    }
                    
                    // 如果启用了自动下单，执行下单
                    if (localStorage.getItem('cdkAutoOrder') !== 'false') {
                        // 确保有卖家数据且包含saleId
                        if (sellersData && sellersData.length > 0 && sellersData[0].saleId) {
                            const saleId = sellersData[0].saleId;
                            
                            // 检查是否已对此卖家尝试下单
                            const orderedSaleIds = JSON.parse(localStorage.getItem(`ordered_${game.id}`) || '[]');
                            
                            if (!orderedSaleIds.includes(saleId)) {
                                addCdkLog(`游戏 ${game.name} 已启用自动下单，正在下单...`, false, true);
                                
                                // 记录此卖家ID已尝试下单
                                orderedSaleIds.push(saleId);
                                localStorage.setItem(`ordered_${game.id}`, JSON.stringify(orderedSaleIds));
                                
                                await placeCdkOrder(game.name, saleId);
                            } else {
                                addCdkLog(`游戏 ${game.name} 已经尝试对卖家ID ${saleId} 下单，不再重复下单`, false);
                            }
                        } else {
                            addCdkLog(`游戏 ${game.name} 自动下单失败：无有效的卖家ID`, true);
                        }
                    }
                    
                    // 使用requestAnimationFrame优化渲染
                    requestAnimationFrame(() => {
                        renderMonitoredGames(monitoredGames);
                    });
                }
            }
        }
    } catch (error) {
        addCdkLog(`监控游戏 ${game.name} 时出错: ${error.message}`, true);
        console.error('游戏监控错误:', error);
    }
}

// 批量更新游戏数据，减少API调用
async function updateGameData(gameId, data) {
    return fetch(`/api/games/monitored/${gameId}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    });
}

// 优化渲染函数，使用DocumentFragment减少DOM操作
function renderMonitoredGames(games) {
    if (!cdkGamesList) return;
    
    // 使用DocumentFragment减少DOM重绘
    const fragment = document.createDocumentFragment();
    
    if (!games || games.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'text-center text-muted my-5';
        emptyMessage.textContent = '监控列表为空，请添加游戏...';
        fragment.appendChild(emptyMessage);
    } else {
        let html = '';
        games.forEach(game => {
            const hasTarget = game.target_price && game.target_price > 0;
            const priceReached = hasTarget && game.current_price && game.current_price <= game.target_price;
            
            const cardClass = `game-monitor-card ${hasTarget ? 'has-target' : ''} ${priceReached ? 'price-reached' : ''}`;
            
            html += `
            <div class="${cardClass}" data-game-id="${game.id}" data-game-name="${game.name}" data-game-ava="${game.gameAva || game.picUrl || ''}">
                <img src="${game.gameAva || '/static/images/default-game.png'}" class="game-avatar-lg" loading="lazy" />
                ${hasTarget ? `<span class="target-price-badge ${priceReached ? 'reached' : ''}">¥${game.target_price}</span>` : ''}
                <div class="game-info-block">
                  <div class="game-title">${game.name}</div>
                  <div class="game-actions-row">
                    <button class="remove-game" data-game-id="${game.id}" title="移除">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                    </button>
                    <button class="set-target-price" data-game-id="${game.id}" data-game-name="${game.name}" data-game-ava="${game.gameAva || game.picUrl || ''}" title="设置目标价格">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-tag"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line></svg>
                    </button>
                  </div>
                  <div class="game-price-info">
                    ${game.current_price ? `<span class="current-price">当前: ¥${game.current_price}</span>` : '<span class="text-muted">等待价格数据...</span>'}
                    ${hasTarget ? `<span class="target-price">目标: ¥${game.target_price}</span>` : ''}
                  </div>
                </div>
            </div>`;
        });
        
        // 使用innerHTML一次性设置内容
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // 将所有子节点移动到DocumentFragment
        while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
        }
    }
    
    // 一次性更新DOM
    cdkGamesList.innerHTML = '';
    cdkGamesList.appendChild(fragment);
    
    // 绑定事件
    bindGameCardEvents();
}

// 优化事件绑定，使用事件委托减少事件监听器数量
function bindGameCardEvents() {
    // 使用事件委托绑定移除按钮事件
    cdkGamesList.addEventListener('click', function(e) {
        // 移除游戏按钮
        if (e.target.closest('.remove-game')) {
            const button = e.target.closest('.remove-game');
            const gameId = button.dataset.gameId;
            if (confirm('确定要移除此游戏吗？')) {
                removeMonitoredGame(gameId);
            }
            return;
        }
        
        // 设置目标价格按钮
        if (e.target.closest('.set-target-price')) {
            const button = e.target.closest('.set-target-price');
            const gameId = button.dataset.gameId;
            const gameName = button.dataset.gameName;
            const gameAva = button.dataset.gameAva;
            openTargetPriceModal(gameId, gameName, gameAva);
            return;
        }
    });
}

// 优化日志添加函数，使用虚拟滚动减少DOM操作
const logBuffer = [];
const LOG_BUFFER_SIZE = 10;
let logUpdateScheduled = false;

function addCdkLog(message, isError = false, important = false) {
    const timestamp = new Date().toLocaleTimeString();
    
    // 添加到缓冲区
    logBuffer.push({
        message,
        timestamp,
        isError,
        important
    });
    
    // 如果缓冲区满了或者是重要消息，立即刷新
    if (logBuffer.length >= LOG_BUFFER_SIZE || important) {
        flushLogBuffer();
    } else if (!logUpdateScheduled) {
        // 否则安排一个延迟刷新
        logUpdateScheduled = true;
        setTimeout(flushLogBuffer, 500);
    }
}

function flushLogBuffer() {
    if (!cdkLogContainer || logBuffer.length === 0) return;
    
    const fragment = document.createDocumentFragment();
    
    logBuffer.forEach(log => {
        const logItem = document.createElement('div');
        logItem.className = `log-item ${log.isError ? 'error' : ''} ${log.important ? 'important' : ''}`;
        logItem.innerHTML = `<span class="log-time">[${log.timestamp}]</span> ${log.message}`;
        fragment.appendChild(logItem);
    });
    
    // 添加到日志容器顶部
    if (cdkLogContainer.firstChild) {
        cdkLogContainer.insertBefore(fragment, cdkLogContainer.firstChild);
    } else {
        cdkLogContainer.appendChild(fragment);
    }
    
    // 限制日志数量
    while (cdkLogContainer.children.length > 100) {
        cdkLogContainer.removeChild(cdkLogContainer.lastChild);
    }
    
    // 清空缓冲区
    logBuffer.length = 0;
    logUpdateScheduled = false;
    
    // 自动滚动到顶部
    cdkLogContainer.scrollTop = 0;
}

// 优化搜索游戏函数，添加防抖
const debouncedSearchGames = debounce(function(query) {
    if (!query || query.length < 2) {
        cdkSearchResults.innerHTML = '<div class="text-center text-muted">请输入至少2个字符</div>';
        return;
    }
    
    // 显示加载中
    cdkSearchResults.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> 搜索中...</div>';
    
    fetch(`/api/games/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(games => {
            renderSearchResults(games);
        })
        .catch(error => {
            cdkSearchResults.innerHTML = `<div class="text-center text-danger">搜索失败: ${error.message}</div>`;
        });
}, 300);

// 添加内存管理优化
function cleanupMemory() {
    // 清理不再需要的大对象引用
    const oldLogs = Array.from(cdkLogContainer.children).slice(100);
    oldLogs.forEach(log => log.remove());
    
    // 清理过期的localStorage数据
    const now = Date.now();
    Object.keys(localStorage).forEach(key => {
        if (key.startsWith('ordered_')) {
            try {
                const gameId = key.replace('ordered_', '');
                const game = monitoredGames.find(g => g.id === gameId);
                if (!game) {
                    // 如果游戏不在监控列表中，删除相关数据
                    localStorage.removeItem(key);
                }
            } catch (e) {
                console.error('清理localStorage出错:', e);
            }
        }
    });
}

// 定期清理内存
setInterval(cleanupMemory, 30 * 60 * 1000); // 每30分钟执行一次

// 添加清除已下单记录的按钮
const clearOrderedCdksBtn = document.getElementById('clear-ordered-cdks');
if (clearOrderedCdksBtn) {
    clearOrderedCdksBtn.addEventListener('click', function() {
        if (confirm('确定要清除所有已下单CDK记录吗？这可能导致重复下单！')) {
            // 清除所有以ordered_开头的localStorage项
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('ordered_')) {
                    localStorage.removeItem(key);
                }
            });
            addCdkLog('已清除所有已下单CDK记录', false, true);
        }
    });
}

// 添加热门游戏按钮和模态框相关元素
const viewHotGamesBtn = document.getElementById('view-hot-games');
const hotGamesModal = document.getElementById('hot-games-modal');
const closeHotGamesModalBtn = document.getElementById('close-hot-games-modal');
const closeHotGamesModalXBtn = document.getElementById('close-hot-games-modal-x');
const hotGamesContainer = document.getElementById('hot-games-container');

// 绑定热门游戏按钮事件
if (viewHotGamesBtn) {
    viewHotGamesBtn.addEventListener('click', function() {
        openHotGamesModal();
    });
}

if (closeHotGamesModalBtn) {
    closeHotGamesModalBtn.addEventListener('click', function() {
        closeHotGamesModal();
    });
}

if (closeHotGamesModalXBtn) {
    closeHotGamesModalXBtn.addEventListener('click', function() {
        closeHotGamesModal();
    });
}

// 打开热门游戏模态框
function openHotGamesModal() {
    if (hotGamesModal) {
        hotGamesModal.style.display = 'block';
        // 重置为第一页
        hotGamesCurrentPage = 1;
        loadHotGames(hotGamesCurrentPage);
    }
}

// 关闭热门游戏模态框
function closeHotGamesModal() {
    if (hotGamesModal) {
        hotGamesModal.style.display = 'none';
    }
}

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    if (event.target === hotGamesModal) {
        closeHotGamesModal();
    }
});

// 添加热门游戏分页变量
let hotGamesCurrentPage = 1;
let hotGamesTotalPages = 1;
const hotGamesPageSize = 50;

// 加载热门游戏列表
function loadHotGames(page = 1) {
    if (!hotGamesContainer) return;
    
    hotGamesCurrentPage = page;
    hotGamesContainer.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p class="mt-2">加载中...</p></div>';
    
    fetch(`/api/games/hot?pageNumber=${page}&pageSize=${hotGamesPageSize}&sort=cdkCount&order=asc`)
        .then(response => {
            if (!response.ok) {
                throw new Error('获取热门游戏列表失败');
            }
            return response.json();
        })
        .then(data => {
            console.log('Hot games response:', data);
            // 检查响应结构，适应不同的数据格式
            let gamesList = [];
            let totalPages = 1;
            
            if (data && data.success) {
                // 直接从API返回的结构
                if (data.content && Array.isArray(data.content)) {
                    gamesList = data.content;
                    totalPages = data.totalPages || Math.ceil(data.totalElements / hotGamesPageSize) || 1;
                }
                // 嵌套在result中的结构
                else if (data.result && data.result.content && Array.isArray(data.result.content)) {
                    gamesList = data.result.content;
                    totalPages = data.result.totalPages || Math.ceil(data.result.totalElements / hotGamesPageSize) || 1;
                }
            }
            
            hotGamesTotalPages = totalPages;
            renderHotGames(gamesList);
            updateHotGamesPagination();
        })
        .catch(error => {
            console.error('Error loading hot games:', error);
            hotGamesContainer.innerHTML = `<div class="alert alert-danger">获取热门游戏列表失败: ${error.message}</div>`;
        });
}

// 更新热门游戏分页控件
function updateHotGamesPagination() {
    const paginationElement = document.getElementById('hot-games-pagination');
    if (!paginationElement) return;
    
    const prevBtn = paginationElement.querySelector('.prev-page');
    const nextBtn = paginationElement.querySelector('.next-page');
    const pageIndicator = paginationElement.querySelector('.page-indicator');
    
    if (prevBtn) {
        prevBtn.disabled = hotGamesCurrentPage <= 1;
    }
    
    if (nextBtn) {
        nextBtn.disabled = hotGamesCurrentPage >= hotGamesTotalPages;
    }
    
    if (pageIndicator) {
        pageIndicator.textContent = `第 ${hotGamesCurrentPage} 页 / 共 ${hotGamesTotalPages} 页`;
    }
}

// 前往上一页
function goToHotGamesPrevPage() {
    if (hotGamesCurrentPage > 1) {
        loadHotGames(hotGamesCurrentPage - 1);
    }
}

// 前往下一页
function goToHotGamesNextPage() {
    if (hotGamesCurrentPage < hotGamesTotalPages) {
        loadHotGames(hotGamesCurrentPage + 1);
    }
}

// 渲染热门游戏列表
function renderHotGames(games) {
    if (!games || games.length === 0) {
        hotGamesContainer.innerHTML = '<div class="text-muted text-center py-4">暂无热门游戏数据</div>';
        return;
    }
    
    console.log('Rendering games:', games);
    
    let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>' +
               '<th>游戏名称</th><th>价格</th><th>更新时间</th><th>操作</th>' +
               '</tr></thead><tbody>';
               
    games.forEach(game => {
        // 根据实际返回的字段名称调整
        const gameName = game.gameName || game.name || '未知';
        const keyPrice = game.keyPrice || game.price || 0;
        const gameId = game.gameId || game.id || '';
        const updateTime = game.updateTime ? new Date(game.updateTime).toLocaleString() : '未知';
        
        // 检查游戏是否已在监控列表中
        const isAlreadyMonitored = monitoredGames.some(mg => mg.id === gameId);
        
        html += `
        <tr>
            <td>${gameName}</td>
            <td>¥${keyPrice}</td>
            <td>${updateTime}</td>
            <td>
                <button class="btn btn-sm btn-${isAlreadyMonitored ? 'secondary' : 'primary'} add-to-monitor" 
                        data-game-id="${gameId}" 
                        data-game-name="${gameName}" 
                        data-game-price="${keyPrice}"
                        ${isAlreadyMonitored ? 'disabled' : ''}>
                    ${isAlreadyMonitored ? '已添加' : '添加监控'}
                </button>
            </td>
        </tr>`;
    });
    
    html += '</tbody></table></div>';
    
    // 添加分页控件
    html += `
    <div id="hot-games-pagination" class="d-flex justify-content-between align-items-center mt-3">
        <button class="btn btn-sm btn-outline-secondary prev-page" ${hotGamesCurrentPage <= 1 ? 'disabled' : ''}>
            <i class="icon-chevron-left"></i> 上一页
        </button>
        <span class="page-indicator">第 ${hotGamesCurrentPage} 页 / 共 ${hotGamesTotalPages} 页</span>
        <button class="btn btn-sm btn-outline-secondary next-page" ${hotGamesCurrentPage >= hotGamesTotalPages ? 'disabled' : ''}>
            下一页 <i class="icon-chevron-right"></i>
        </button>
    </div>`;
    
    hotGamesContainer.innerHTML = html;
    
    // 绑定添加监控按钮事件
    document.querySelectorAll('.add-to-monitor').forEach(btn => {
        btn.addEventListener('click', function() {
            const gameId = this.getAttribute('data-game-id');
            const gameName = this.getAttribute('data-game-name');
            const gameAva = this.getAttribute('data-game-ava') || '';
            addGameToMonitor(gameId, gameName, gameAva);
            this.textContent = '已添加';
            this.classList.remove('btn-primary');
            this.classList.add('btn-secondary');
            this.disabled = true;
        });
    });
    
    // 绑定分页按钮事件
    const prevPageBtn = document.querySelector('#hot-games-pagination .prev-page');
    const nextPageBtn = document.querySelector('#hot-games-pagination .next-page');
    
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', goToHotGamesPrevPage);
    }
    
    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', goToHotGamesNextPage);
    }
}

// 打开热门游戏模态框
function openHotGamesModal() {
    if (hotGamesModal) {
        hotGamesModal.style.display = 'block';
        // 重置为第一页
        hotGamesCurrentPage = 1;
        loadHotGames(hotGamesCurrentPage);
    }
}








