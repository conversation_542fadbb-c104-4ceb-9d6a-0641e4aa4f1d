from pydantic import BaseModel, Field
from typing import List, Optional

class Game(BaseModel):
    """代表一个游戏的基本信息"""
    id: str
    gameName: str = Field(..., alias='gameName')
    picUrl: str = Field(..., alias='gameAva')
    price: Optional[str] = None # 从API返回的数据中，此字段可能不存在
    keyPrice: Optional[float] = Field(None, alias='keyPrice')
    keyAveAmt: Optional[float] = Field(None, alias='keyAveAmt')

    class Config:
        populate_by_name = True

class GameFavoriteCreate(Game):
    """用于创建收藏游戏时，前端传入的数据模型"""
    pass

class GameFavoriteReorder(BaseModel):
    """用于更新收藏游戏顺序时，前端传入的数据模型"""
    ordered_game_ids: List[str]
