// 肥皂盒页面功能
document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const sortField = document.getElementById('sort-field');
    const sortDirection = document.getElementById('sort-direction');
    const pageNumber = document.getElementById('page-number');
    const chestsContainer = document.getElementById('chests-container');
    const chestItemTemplate = document.getElementById('chest-item-template');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const pageIndicator = document.getElementById('page-indicator');
    
    // 每页数量
    const PAGE_SIZE = 10;
    let currentPage = 1;
    let totalPages = 1;
    
    // 初始化加载
    loadChests();
    
    // 排序和分页控制事件监听
    if (sortField) sortField.addEventListener('change', loadChests);
    if (sortDirection) sortDirection.addEventListener('change', loadChests);
    if (pageNumber) pageNumber.addEventListener('change', handlePageChange);
    if (prevPageBtn) prevPageBtn.addEventListener('click', goToPrevPage);
    if (nextPageBtn) nextPageBtn.addEventListener('click', goToNextPage);
    
    /**
     * 加载肥皂盒数据
     */
    async function loadChests() {
        if (!chestsContainer) return;
        
        // 获取排序和分页参数
        const sort = sortField ? sortField.value : 'sortOrder';
        const order = sortDirection ? sortDirection.value : 'asc';
        const page = pageNumber ? parseInt(pageNumber.value) || 1 : 1;
        
        // 更新当前页
        currentPage = page;
        updatePageIndicator();
        
        // 显示加载中
        chestsContainer.innerHTML = `
            <div class="loading-indicator">
                <div class="spin"></div>
                <span>正在加载肥皂盒数据...</span>
            </div>
        `;
        
        try {
            // 构建API请求URL
            const url = `/api/chests?page_number=${page}&page_size=${PAGE_SIZE}&sort=${sort}&order=${order}`;
            
            // 发送请求
            const response = await fetch(url);
            
            // 检查响应状态
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
                throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
            }
            
            // 解析响应数据
            const data = await response.json();
            
            // 检查API返回状态
            if (data.code !== 200) {
                throw new Error(data.message || '获取数据失败');
            }
            
            // 获取内容
            const result = data.result || {};
            const content = result.content || [];
            
            console.log("获取到的肥皂盒数据:", content); // 调试日志
            
            // 计算总页数
            if (result.totalElements && result.size) {
                totalPages = Math.ceil(result.totalElements / result.size);
            } else {
                totalPages = 1;
            }
            
            // 更新分页控件
            updatePaginationControls();
            
            // 渲染肥皂盒列表
            renderChests(content);
        } catch (error) {
            console.error('获取肥皂盒数据失败:', error);
            chestsContainer.innerHTML = `
                <div class="error-message">
                    <p>获取肥皂盒数据失败: ${error.message}</p>
                    <p>请检查登录状态或稍后再试。</p>
                </div>
            `;
        }
    }
    
    /**
     * 渲染肥皂盒列表
     * @param {Array} chests - 肥皂盒数据数组
     */
    function renderChests(chests) {
        if (!chestsContainer || !chestItemTemplate) return;
        
        // 如果没有数据
        if (!chests || chests.length === 0) {
            chestsContainer.innerHTML = '<p>没有找到肥皂盒数据。</p>';
            return;
        }
        
        // 清空容器
        chestsContainer.innerHTML = '';
        
        // 创建肥皂盒列表容器
        const chestsListElement = document.createElement('div');
        chestsListElement.className = 'chests-list';
        
        // 遍历肥皂盒数据
        chests.forEach(chest => {
            // 克隆模板
            const chestItem = chestItemTemplate.content.cloneNode(true).firstElementChild;
            
            // 调试输出
            console.log("处理肥皂盒数据:", chest);
            
            // 设置内容
            chestItem.querySelector('.chest-name').textContent = chest.name || '未知肥皂盒';
            
            // 设置ID - 新结构
            const chestIdSpan = chestItem.querySelector('.chest-id');
            if (chestIdSpan) {
                chestIdSpan.textContent = chest.id || 'N/A';
            }
            
            chestItem.querySelector('.chest-price').textContent = chest.oncePrice ? `¥${chest.oncePrice.toFixed(2)}` : 'N/A';
            
            // 添加卖家信息
            if (chestItem.querySelector('.chest-seller')) {
                chestItem.querySelector('.chest-seller').textContent = chest.nameSeller || '未知卖家';
            }
            
            // 当前未抽 = 总数 - 已抽
            const totalDraw = chest.totalDraw || 0;
            const curDraw = chest.curDraw || 0;
            const remainDraws = totalDraw - curDraw;
            
            chestItem.querySelector('.chest-remain').textContent = remainDraws;
            chestItem.querySelector('.chest-total').textContent = totalDraw;
            
            // 使用rate1作为概率 - 将小数转换为百分比
            const rate1 = chest.rate1 || chest.probability1 || 0;
            // 如果rate1是小数(0-1之间)，则乘以100转为百分比
            const probabilityDisplay = rate1 <= 1 ? 
                `${(rate1 * 100).toFixed(2)}%` : 
                `${parseFloat(rate1).toFixed(2)}%`;
            
            chestItem.querySelector('.chest-probability').textContent = probabilityDisplay;
            
            // 移除徽章显示
            const badge = chestItem.querySelector('.chest-badge');
            if (badge) {
                badge.style.display = 'none';
            }
            
            // 添加到容器
            chestsListElement.appendChild(chestItem);
        });
        
        // 将列表添加到容器
        chestsContainer.appendChild(chestsListElement);
    }
    
    /**
     * 处理页码变更
     */
    function handlePageChange() {
        if (!pageNumber) return;
        
        let page = parseInt(pageNumber.value) || 1;
        
        // 确保页码在有效范围
        if (page < 1) page = 1;
        if (page > totalPages) page = totalPages;
        
        // 更新输入框值
        pageNumber.value = page;
        
        // 加载新页数据
        loadChests();
    }
    
    /**
     * 前往上一页
     */
    function goToPrevPage() {
        if (currentPage > 1) {
            currentPage--;
            if (pageNumber) pageNumber.value = currentPage;
            loadChests();
        }
    }
    
    /**
     * 前往下一页
     */
    function goToNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            if (pageNumber) pageNumber.value = currentPage;
            loadChests();
        }
    }
    
    /**
     * 更新分页控件状态
     */
    function updatePaginationControls() {
        if (prevPageBtn) prevPageBtn.disabled = currentPage <= 1;
        if (nextPageBtn) nextPageBtn.disabled = currentPage >= totalPages;
        updatePageIndicator();
    }
    
    /**
     * 更新页码指示器
     */
    function updatePageIndicator() {
        if (pageIndicator) {
            pageIndicator.textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
        }
    }
}); 