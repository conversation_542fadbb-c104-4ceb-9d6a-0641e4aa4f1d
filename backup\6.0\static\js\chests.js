// 肥皂盒页面功能
document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const sortField = document.getElementById('sort-field');
    const sortDirection = document.getElementById('sort-direction');
    const pageNumber = document.getElementById('page-number');
    const chestsContainer = document.getElementById('chests-container');
    const chestItemTemplate = document.getElementById('chest-item-template');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const pageIndicator = document.getElementById('page-indicator');
    
    // 详情模态框元素
    const detailModal = document.getElementById('chest-detail-modal');
    const detailChestTitle = document.getElementById('detail-chest-title');
    const detailChestId = document.getElementById('detail-chest-id');
    const detailSeller = document.getElementById('detail-seller');
    const detailPrice = document.getElementById('detail-price');
    const detailRemain = document.getElementById('detail-remain');
    const detailTotal = document.getElementById('detail-total');
    const detailProbability = document.getElementById('detail-probability');
    const detailLoading = document.getElementById('detail-loading');
    const detailError = document.getElementById('detail-error');
    const chestBasicInfo = document.getElementById('chest-basic-info');
    const chestGamesList = document.getElementById('chest-games-list');
    const gameCardTemplate = document.getElementById('game-card-template');
    const poolTemplate = document.getElementById('pool-template');
    const closeBtn = detailModal ? detailModal.querySelector('.close-btn') : null;
    
    // 刷新按钮
    const refreshChestBtn = document.getElementById('refresh-chest-btn');
    
    // 当前查看的肥皂盒ID
    let currentChestId = null;
    
    // 当前肥皂盒价格
    let currentChestPrice = 90;
    
    // 每页数量
    const PAGE_SIZE = 10;
    let currentPage = 1;
    let totalPages = 1;
    
    // 初始化加载
    loadChests();
    
    // 排序和分页控制事件监听
    if (sortField) sortField.addEventListener('change', loadChests);
    if (sortDirection) sortDirection.addEventListener('change', loadChests);
    if (pageNumber) pageNumber.addEventListener('change', handlePageChange);
    if (prevPageBtn) prevPageBtn.addEventListener('click', goToPrevPage);
    if (nextPageBtn) nextPageBtn.addEventListener('click', goToNextPage);
    
    // 关闭模态框按钮事件
    if (closeBtn) {
        closeBtn.addEventListener('click', function() {
            if (detailModal) {
                detailModal.style.display = 'none';
                currentChestId = null; // 重置当前查看的肥皂盒ID
            }
        });
    }
    
    // 刷新按钮事件
    if (refreshChestBtn) {
        refreshChestBtn.addEventListener('click', function() {
            if (currentChestId) {
                // 给按钮添加旋转动画效果
                refreshChestBtn.classList.add('rotating');
                
                // 重新加载当前肥皂盒详情
                openChestDetail({ id: currentChestId }).finally(() => {
                    // 动画结束后移除旋转效果
                    setTimeout(() => {
                        refreshChestBtn.classList.remove('rotating');
                    }, 1000);
                });
            }
        });
    }
    
    // 点击其他区域关闭模态框
    window.addEventListener('click', function(event) {
        if (event.target === detailModal) {
            detailModal.style.display = 'none';
        }
    });
    
    /**
     * 加载肥皂盒数据
     */
    async function loadChests() {
        if (!chestsContainer) return;
        
        // 获取排序和分页参数
        const sort = sortField ? sortField.value : 'sortOrder';
        const order = sortDirection ? sortDirection.value : 'asc';
        const page = pageNumber ? parseInt(pageNumber.value) || 1 : 1;
        
        // 更新当前页
        currentPage = page;
        updatePageIndicator();
        
        // 显示加载中
        chestsContainer.innerHTML = `
            <div class="loading-indicator">
                <div class="spin"></div>
                <span>正在加载肥皂盒数据...</span>
            </div>
        `;
        
        try {
            // 构建API请求URL
            const url = `/api/chests?page_number=${page}&page_size=${PAGE_SIZE}&sort=${sort}&order=${order}`;
            
            // 发送请求
            const response = await fetch(url);
            
            // 检查响应状态
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
                throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
            }
            
            // 解析响应数据
            const data = await response.json();
            
            // 检查API返回状态
            if (data.code !== 200) {
                throw new Error(data.message || '获取数据失败');
            }
            
            // 获取内容
            const result = data.result || {};
            const content = result.content || [];
            
            console.log("获取到的肥皂盒数据:", content); // 调试日志
            
            // 计算总页数
            if (result.totalElements && result.size) {
                totalPages = Math.ceil(result.totalElements / result.size);
            } else {
                totalPages = 1;
            }
            
            // 更新分页控件
            updatePaginationControls();
            
            // 渲染肥皂盒列表
            renderChests(content);
        } catch (error) {
            console.error('获取肥皂盒数据失败:', error);
            chestsContainer.innerHTML = `
                <div class="error-message">
                    <p>获取肥皂盒数据失败: ${error.message}</p>
                    <p>请检查登录状态或稍后再试。</p>
                </div>
            `;
        }
    }
    
    /**
     * 渲染肥皂盒列表
     * @param {Array} chests - 肥皂盒数据数组
     */
    function renderChests(chests) {
        if (!chestsContainer || !chestItemTemplate) return;
        
        // 如果没有数据
        if (!chests || chests.length === 0) {
            chestsContainer.innerHTML = '<p>没有找到肥皂盒数据。</p>';
            return;
        }
        
        // 清空容器
        chestsContainer.innerHTML = '';
        
        // 创建肥皂盒列表容器
        const chestsListElement = document.createElement('div');
        chestsListElement.className = 'chests-list';
        
        // 遍历肥皂盒数据
        chests.forEach(chest => {
            // 克隆模板
            const chestItem = chestItemTemplate.content.cloneNode(true).firstElementChild;
            
            // 调试输出
            console.log("处理肥皂盒数据:", chest);
            
            // 设置肥皂盒ID属性
            const chestId = chest.id || '';
            chestItem.dataset.chestId = chestId;
            
            // 设置内容
            chestItem.querySelector('.chest-name').textContent = chest.name || '未知肥皂盒';
            
            // 设置ID - 新结构
            const chestIdSpan = chestItem.querySelector('.chest-id');
            if (chestIdSpan) {
                chestIdSpan.textContent = chestId;
            }
            
            chestItem.querySelector('.chest-price').textContent = chest.oncePrice ? `¥${chest.oncePrice.toFixed(2)}` : 'N/A';
            
            // 添加卖家信息
            if (chestItem.querySelector('.chest-seller')) {
                chestItem.querySelector('.chest-seller').textContent = chest.nameSeller || '未知卖家';
            }
            
            // 当前未抽 = 总数 - 已抽
            const totalDraw = chest.totalDraw || 0;
            const curDraw = chest.curDraw || 0;
            const remainDraws = totalDraw - curDraw;
            
            chestItem.querySelector('.chest-remain').textContent = remainDraws;
            chestItem.querySelector('.chest-total').textContent = totalDraw;
            
            // 使用rate1作为概率 - 将小数转换为百分比
            const rate1 = chest.rate1 || chest.probability1 || 0;
            // 如果rate1是小数(0-1之间)，则乘以100转为百分比
            const probabilityDisplay = rate1 <= 1 ? 
                `${(rate1 * 100).toFixed(2)}%` : 
                `${parseFloat(rate1).toFixed(2)}%`;
            
            chestItem.querySelector('.chest-probability').textContent = probabilityDisplay;
            
            // 移除徽章显示
            const badge = chestItem.querySelector('.chest-badge');
            if (badge) {
                badge.style.display = 'none';
            }
            
            // 添加点击事件，打开详情模态框
            chestItem.addEventListener('click', function() {
                openChestDetail(chest);
            });
            
            // 添加到容器
            chestsListElement.appendChild(chestItem);
        });
        
        // 将列表添加到容器
        chestsContainer.appendChild(chestsListElement);
    }
    
    /**
     * 打开肥皂盒详情模态框
     * @param {Object} chest - 肥皂盒数据对象
     */
    async function openChestDetail(chest) {
        if (!detailModal) {
            console.error('找不到详情模态框元素');
            return;
        }
        
        // 保存当前肥皂盒ID
        currentChestId = chest.id;
        
        // 重置模态框状态
        if (detailLoading) detailLoading.style.display = 'flex';
        if (detailError) detailError.style.display = 'none';
        if (chestBasicInfo) chestBasicInfo.style.display = 'none';
        if (chestGamesList) chestGamesList.style.display = 'none';
        
        // 显示模态框
        detailModal.style.display = 'block';
        
        // 获取肥皂盒ID
        const chestId = chest.id;
        if (!chestId) {
            showDetailError('无效的肥皂盒ID');
            return;
        }
        
        try {
            // 设置基本信息
            const chestName = chest.name || '未知肥皂盒';
            if (detailChestTitle) detailChestTitle.textContent = chestName;
            
            // 获取肥皂盒详情
            const detailData = await fetchChestDetail(chestId);
            if (!detailData) {
                throw new Error('获取详情失败');
            }
            
            // 填充基本信息
            fillChestBasicInfo(detailData);
            
            // 获取肥皂盒中的游戏
            const gamesData = await fetchChestGames(chestId);
            
            // 从详情数据中提取概率信息
            const rateInfo = {
                rate1: detailData.rate1,
                rate2: detailData.rate2,
                rate3: detailData.rate3,
                rate4: detailData.rate4
            };
            
            console.log("肥皂盒概率信息:", rateInfo);
            
            // 填充游戏列表
            fillChestGames(gamesData, rateInfo);
            
            // 显示基本信息和游戏列表
            if (detailLoading) detailLoading.style.display = 'none';
            if (chestBasicInfo) chestBasicInfo.style.display = 'block';
            if (chestGamesList) chestGamesList.style.display = 'block';
            
        } catch (error) {
            console.error('获取肥皂盒详情失败:', error);
            showDetailError(error.message || '获取详情失败');
        }
    }
    
    /**
     * 获取肥皂盒详情
     * @param {string} chestId - 肥皂盒ID
     */
    async function fetchChestDetail(chestId) {
        const url = `/api/xboot/chest/showOne?chestId=${chestId}`;
        
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
            throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
        }
        
        const data = await response.json();
        if (data.code !== 200) {
            throw new Error(data.message || '获取详情失败');
        }
        
        return data.result;
    }
    
    /**
     * 获取肥皂盒中的游戏
     * @param {string} chestId - 肥皂盒ID
     */
    async function fetchChestGames(chestId) {
        const url = `/api/xboot/chest/showGame?chestId=${chestId}&pageNumber=1&pageSize=100&sort=lv&order=asc`;
        
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
            throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
        }
        
        const data = await response.json();
        if (data.code !== 200) {
            throw new Error(data.message || '获取游戏列表失败');
        }
        
        return data.result.content || [];
    }
    
    /**
     * 填充肥皂盒游戏列表
     * @param {Array} games - 肥皂盒中的游戏数据
     * @param {Object} rateInfo - 概率信息对象
     */
    function fillChestGames(games, rateInfo) {
        if (!games || !games.length || !chestGamesList) return;
        
        // 清空容器
        chestGamesList.innerHTML = '';
        
        // 转换概率函数 - 确保概率正确显示为百分比
        const formatRate = (rate) => {
            if (rate === null || rate === undefined) return "未知";
            
            // 将字符串转换为数字
            let rateNum = parseFloat(rate);
            if (isNaN(rateNum)) return "未知";
            
            // 如果概率已经是百分比形式（大于1），则直接显示
            if (rateNum > 1) {
                return `${rateNum.toFixed(8)}%`.replace(/0+$/, '0');
            } 
            // 如果概率是小数形式（0-1之间），则转换为百分比
            else {
                return `${(rateNum * 100).toFixed(8)}%`.replace(/0+$/, '0');
            }
        };
        
        // 按等级分组游戏 (将等级视为池子类型)
        const poolMap = {
            "1": { name: "传说池", class: "legendary-pool", probability: formatRate(rateInfo.rate1) },
            "2": { name: "史诗池", class: "epic-pool", probability: formatRate(rateInfo.rate2) },
            "3": { name: "稀有池", class: "rare-pool", probability: formatRate(rateInfo.rate3) },
            "4": { name: "普通池", class: "common-pool", probability: formatRate(rateInfo.rate4) }
        };
        
        // 按等级分组游戏
        const gamesByLevel = {};
        games.forEach(game => {
            // 跳过没有名称的游戏
            if (!game.gameNameCn) return;
            
            const lv = game.lv || '1';
            if (!gamesByLevel[lv]) {
                gamesByLevel[lv] = [];
            }
            gamesByLevel[lv].push(game);
        });
        
        // 按等级顺序渲染游戏池
        const sortedLevels = Object.keys(gamesByLevel).sort((a, b) => parseInt(a) - parseInt(b));
        
        sortedLevels.forEach(level => {
            const poolInfo = poolMap[level] || { name: `等级 ${level}`, class: "default-pool", probability: "未知" };
            
            // 克隆池子模板
            const poolElement = poolTemplate.content.cloneNode(true).firstElementChild;
            
            // 设置池子名称和等级标签
            const poolNameEl = poolElement.querySelector('.pool-name');
            poolNameEl.textContent = poolInfo.name;
            
            // 创建并添加等级标签
            const levelTag = document.createElement('span');
            levelTag.className = `pool-level lv${level}`;
            levelTag.textContent = `Lv.${level}`;
            poolNameEl.appendChild(levelTag);
            
            // 添加池子样式类
            poolElement.classList.add(poolInfo.class);
            
            // 设置概率
            const probabilityEl = poolElement.querySelector('.pool-probability');
            if (probabilityEl) {
                probabilityEl.textContent = poolInfo.probability;
            }
            
            // 获取游戏容器
            const poolGamesContainer = poolElement.querySelector('.pool-games');
            
            // 渲染该等级的所有游戏
            gamesByLevel[level].forEach(game => {
                // 克隆模板
                const gameCard = gameCardTemplate.content.cloneNode(true).firstElementChild;
                
                // 设置游戏标题
                gameCard.querySelector('.game-title').textContent = game.gameNameCn;
                
                // 设置库存
                gameCard.querySelector('.game-stock').textContent = `库存: ${game.stock || '0'}`;
                
                // 添加到池子容器
                poolGamesContainer.appendChild(gameCard);
            });
            
            // 添加到总容器
            chestGamesList.appendChild(poolElement);
        });
    }
    
    /**
     * 填充肥皂盒基本信息
     * @param {Object} detail - 肥皂盒详情数据
     */
    function fillChestBasicInfo(detail) {
        if (!detail) return;
        
        // 设置ID
        if (detailChestId) detailChestId.textContent = detail.id || 'N/A';
        
        // 设置卖家
        if (detailSeller) detailSeller.textContent = detail.nameSeller || '未知卖家';
        
        // 设置单抽价格
        const oncePrice = document.getElementById('detail-once-price');
        if (oncePrice) {
            oncePrice.textContent = detail.oncePrice ? `¥${detail.oncePrice.toFixed(2)}` : '¥90.00';
        }
        
        // 设置多抽次数和价格
        const multiDraw = document.getElementById('detail-multi-draw');
        const multiPrice = document.getElementById('detail-multi-price');
        
        // 设置多抽次数 - 使用正确的小写字段名
        if (multiDraw && detail.multidraw !== undefined) {
            multiDraw.textContent = detail.multidraw;
        }
        
        // 设置多抽价格 - 使用正确的字段名
        if (multiPrice && detail.multiPrice !== undefined) {
            multiPrice.textContent = `¥${detail.multiPrice.toFixed(2)}`;
        }
        
        // 设置抽取进度
        const totalDraw = detail.totalDraw || 0;
        const curDraw = detail.curDraw || 0;
        const remainDraws = totalDraw - curDraw;
        
        if (detailRemain) detailRemain.textContent = remainDraws;
        if (detailTotal) detailTotal.textContent = totalDraw;
    }
    
    /**
     * 显示详情错误信息
     * @param {string} message - 错误信息
     */
    function showDetailError(message) {
        if (detailLoading) detailLoading.style.display = 'none';
        if (chestBasicInfo) chestBasicInfo.style.display = 'none';
        if (chestGamesList) chestGamesList.style.display = 'none';
        
        if (detailError) {
            const errorElement = detailError.querySelector('p');
            if (errorElement) {
                errorElement.textContent = message || '获取详情失败，请稍后再试。';
            }
            
            detailError.style.display = 'block';
        }
    }
    
    /**
     * 处理页码变更
     */
    function handlePageChange() {
        if (!pageNumber) return;
        
        let page = parseInt(pageNumber.value) || 1;
        
        // 确保页码在有效范围
        if (page < 1) page = 1;
        if (page > totalPages) page = totalPages;
        
        // 更新输入框值
        pageNumber.value = page;
        
        // 加载新页数据
        loadChests();
    }
    
    /**
     * 前往上一页
     */
    function goToPrevPage() {
        if (currentPage > 1) {
            currentPage--;
            if (pageNumber) pageNumber.value = currentPage;
            loadChests();
        }
    }
    
    /**
     * 前往下一页
     */
    function goToNextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            if (pageNumber) pageNumber.value = currentPage;
            loadChests();
        }
    }
    
    /**
     * 更新分页控件状态
     */
    function updatePaginationControls() {
        if (prevPageBtn) prevPageBtn.disabled = currentPage <= 1;
        if (nextPageBtn) nextPageBtn.disabled = currentPage >= totalPages;
        updatePageIndicator();
    }
    
    /**
     * 更新页码指示器
     */
    function updatePageIndicator() {
        if (pageIndicator) {
            pageIndicator.textContent = `第 ${currentPage} 页 / 共 ${totalPages} 页`;
        }
    }
}); 