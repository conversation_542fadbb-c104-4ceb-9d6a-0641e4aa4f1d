import json
from base64 import b64decode
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field

# 定义账号类型
class AccountType(str, Enum):
    QUERY = "query"           # 查询账户，负责查询接口
    ORDER_PRIMARY = "order_primary"  # 下单主账号
    ORDER_SECONDARY = "order_secondary"  # 下单副账号
    BACKUP = "backup"         # 备用账号

# Models for Mobile Cookie Pool
class MobileAccount(BaseModel):
    id: str
    name: str
    access_token: Optional[str] = Field(None, description="访问令牌")
    account_type: AccountType = Field(default=AccountType.BACKUP, description="账户类型")
    added_at: Optional[str] = None
    login_method: str = Field(default="sms", description="登录方式")

    # 确保能够处理账户和主账户标志
    account: Optional[str] = None
    password: Optional[str] = None
    token_expire_time: Optional[str] = None
    status: Optional[int] = 1
    remark: Optional[str] = None

    class Config:
        populate_by_name = True
        use_enum_values = True


class MobileCookiePool(BaseModel):
    accounts: List[MobileAccount] 