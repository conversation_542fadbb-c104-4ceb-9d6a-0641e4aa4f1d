from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class OrderRecord(BaseModel):
    """订单记录模型"""
    id: str                    # 订单ID
    game_id: str               # 游戏ID
    game_name: str             # 游戏名称
    price: float               # 价格
    account_id: str            # 下单账号ID
    account_name: str          # 下单账号名称
    status: str                # 订单状态: "待支付"或"已支付"
    order_time: datetime       # 下单时间
    access_token: str          # 下单时使用的token(用于兑换操作)
    sale_id: str               # 销售ID
    pay_url: Optional[str] = None  # 支付URL(可选)
    expire_time: Optional[datetime] = None  # 订单过期时间
    
    class Config:
        json_encoders = {
            datetime: lambda dt: dt.isoformat()
        } 