document.addEventListener('DOMContentLoaded', function() {
    // 控制元素
    const toggleButton = document.getElementById('toggleMonitor');
    const minIntervalInput = document.getElementById('minInterval');
    const maxIntervalInput = document.getElementById('maxInterval');
    const autoOrderCheckbox = document.getElementById('autoOrder');
    const monitorModeCheckboxes = document.querySelectorAll('input[name="monitorMode"]');
    const monitorLogsContainer = document.getElementById('monitorLogs');
    const clearLogsButton = document.getElementById('clearLogs');
    const monitorHitsContainer = document.getElementById('monitorHits');
    const clearHitsButton = document.getElementById('clearHits');
    
    // 收藏相关元素
    const specificMonitorPanel = document.getElementById('specific-monitor-panel');
    const favoriteChestsList = document.getElementById('favorite-chests-list');
    const favoritesLink = document.getElementById('favorites-link');
    const refreshFavoritesBtn = document.getElementById('refresh-favorites');
    const manageFavoritesBtn = document.getElementById('manage-favorites');
    const favoritesModal = document.getElementById('favorites-modal');
    const closeModalBtn = document.querySelector('.favorites-modal-close');
    const closeModalFooterBtn = document.getElementById('close-favorites-modal');
    const addChestIdInput = document.getElementById('add-chest-id');
    const addChestBtn = document.getElementById('add-chest-btn');
    const favoritesList = document.getElementById('favorites-list');

    // SVG图标定义
    const svgIcons = {
        play: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-play mr-2"><polygon points="5 3 19 12 5 21 5 3"></polygon></svg>',
        stop: '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-stop mr-2"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg>'
    };

    // 监控状态
    let isMonitoring = false;
    let monitorTimer = null;
    let monitorIntervalId = null;
    
    // 存储特定监控的数据
    let specificMonitorData = {};
    
    // 初始化加载已保存的命中记录
    loadMonitorHits();
    
    // 初始化加载收藏的肥皂盒
    loadFavoriteChests();

    // 清空日志的事件处理
    if (clearLogsButton) {
        clearLogsButton.addEventListener('click', function() {
            monitorLogsContainer.innerHTML = '<div class="text-muted text-center py-4">监控记录已清空...</div>';
        });
    }

    // 清空命中记录的事件处理
    if (clearHitsButton) {
        clearHitsButton.addEventListener('click', function() {
            monitorHitsContainer.innerHTML = '<tr class="text-center text-muted"><td colspan="5">暂无命中记录...</td></tr>';
            // 清空本地存储中的命中记录
            localStorage.removeItem('monitorHits');
        });
    }
    
    // 添加重置特定监控数据按钮
    const resetSpecificDataBtn = document.createElement('button');
    resetSpecificDataBtn.textContent = '清空特定监控数据';
    resetSpecificDataBtn.className = 'btn btn-warning mb-2 ml-2';
    resetSpecificDataBtn.addEventListener('click', function() {
        if (confirm('确定要清空所有特定监控数据吗？这将重置所有肥皂盒的监控状态。')) {
            resetSpecificMonitorData();
            updateDisabledChestsList();
        }
    });

    // 添加清除已下单记录按钮
    const clearOrderedChestsBtn = document.createElement('button');
    clearOrderedChestsBtn.textContent = '清除已下单记录';
    clearOrderedChestsBtn.className = 'btn btn-danger mb-2 ml-2';
    clearOrderedChestsBtn.addEventListener('click', function() {
        if (confirm('确定要清除所有已下单肥皂盒记录吗？这可能导致重复下单！')) {
            localStorage.removeItem('ordered_chests');
            addMonitorLog('已清除所有已下单肥皂盒记录', false, true);
        }
    });

    // 添加到控制区域
    if (clearHitsButton && clearHitsButton.parentNode) {
        clearHitsButton.parentNode.appendChild(resetSpecificDataBtn);
        clearHitsButton.parentNode.appendChild(clearOrderedChestsBtn);
    }
    
    // 添加已禁用的肥皂盒列表区域
    const disabledChestsSection = document.createElement('div');
    disabledChestsSection.id = 'disabledChestsSection';
    disabledChestsSection.className = 'mt-4';
    
    const heading = document.createElement('h4');
    heading.textContent = '已禁用的肥皂盒';
    disabledChestsSection.appendChild(heading);
    
    const disabledList = document.createElement('ul');
    disabledList.id = 'disabledChestsList';
    disabledList.className = 'list-group';
    disabledChestsSection.appendChild(disabledList);
    
    // 添加到页面
    monitorLogsContainer.appendChild(disabledChestsSection);
    
    // 更新已禁用肥皂盒列表
    updateDisabledChestsList();
    
    // 每30秒更新一次已禁用肥皂盒列表
    setInterval(updateDisabledChestsList, 30000);

    // 收藏肥皂盒相关事件绑定
    if (favoritesLink) {
        favoritesLink.addEventListener('click', function(e) {
            e.preventDefault();
            window.open('/favorite-chests', '_blank');
        });
    }
    
    if (refreshFavoritesBtn) {
        refreshFavoritesBtn.addEventListener('click', function() {
            loadFavoriteChests(true);
        });
    }
    
    if (manageFavoritesBtn) {
        manageFavoritesBtn.addEventListener('click', function() {
            openFavoritesModal();
        });
    }
    
    if (closeModalBtn) {
        closeModalBtn.addEventListener('click', function() {
            closeFavoritesModal();
        });
    }
    
    if (closeModalFooterBtn) {
        closeModalFooterBtn.addEventListener('click', function() {
            closeFavoritesModal();
        });
    }
    
    if (addChestBtn) {
        addChestBtn.addEventListener('click', function() {
            addFavoriteChest();
        });
    }
    
    if (addChestIdInput) {
        addChestIdInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addFavoriteChest();
            }
        });
    }
    
    // 监控模式变更事件
    monitorModeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            // 如果选中了特定监控，显示特定监控面板
            if (checkbox.value === 'specific' && checkbox.checked) {
                if (specificMonitorPanel) {
                    specificMonitorPanel.style.display = 'block';
                    // 加载收藏的肥皂盒
                    loadFavoriteChests();
                }
            } else if (checkbox.value === 'specific' && !checkbox.checked) {
                // 如果取消选中特定监控，隐藏特定监控面板
                if (specificMonitorPanel) {
                    specificMonitorPanel.style.display = 'none';
                }
            }
        });
    });

    // 启动/停止监控的事件处理
    toggleButton.addEventListener('click', function() {
        if (isMonitoring) {
            stopMonitoring();
        } else {
            startMonitoring();
        }
    });

    // 添加监控日志
    function addMonitorLog(message, isError = false, isSuccess = false) {
        const logEntry = document.createElement('div');
        logEntry.className = isError ? 'log-error' : (isSuccess ? 'log-success' : 'log-normal');
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        logEntry.innerHTML = `<span class="log-time">[${timestamp}]</span> ${message}`;
        
        monitorLogsContainer.appendChild(logEntry);
        monitorLogsContainer.scrollTop = monitorLogsContainer.scrollHeight;
    }

    // 启动监控
    function startMonitoring() {
        // 获取监控模式
        const selectedModes = [];
        monitorModeCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                selectedModes.push(checkbox.value);
            }
        });

        if (selectedModes.length === 0) {
            addMonitorLog('请至少选择一种监控模式', true);
            return;
        }

        const minInterval = parseInt(minIntervalInput.value) || 5000;
        const maxInterval = parseInt(maxIntervalInput.value) || 8000;
        
        if (minInterval < 1000) {
            addMonitorLog('最小监控间隔不应小于1000ms，已自动调整', true);
            minIntervalInput.value = 1000;
        }
        
        if (maxInterval <= minInterval) {
            addMonitorLog('最大监控间隔应大于最小间隔，已自动调整', true);
            maxIntervalInput.value = minInterval + 1000;
        }
        
        isMonitoring = true;
        toggleButton.innerHTML = svgIcons.stop + '停止监控';
        toggleButton.classList.add('monitoring');
        
        addMonitorLog('监控已启动', false, true);
        
        // 执行首次监控
        runMonitorCycle();
        
        // 设置随机时间间隔循环
        scheduleNextMonitor();
    }
    
    // 停止监控
    function stopMonitoring() {
        if (monitorIntervalId) {
            clearTimeout(monitorIntervalId);
            monitorIntervalId = null;
        }
        
        isMonitoring = false;
        toggleButton.innerHTML = svgIcons.play + '开始监控';
        toggleButton.classList.remove('monitoring');
        
        addMonitorLog('监控已停止');
    }

    // 安排下次监控
    function scheduleNextMonitor() {
        if (!isMonitoring) return;
        
        const minInterval = parseInt(minIntervalInput.value);
        const maxInterval = parseInt(maxIntervalInput.value);
        const randomInterval = Math.floor(Math.random() * (maxInterval - minInterval + 1)) + minInterval;
        
        addMonitorLog(`下次监控将在 ${(randomInterval / 1000).toFixed(1)} 秒后执行`);
        
        monitorIntervalId = setTimeout(() => {
            runMonitorCycle();
            scheduleNextMonitor();
        }, randomInterval);
    }

    // 执行单次监控循环
    async function runMonitorCycle() {
        try {
            const selectedModes = Array.from(monitorModeCheckboxes)
                .filter(checkbox => checkbox.checked)
                .map(checkbox => checkbox.value);
                
            // 获取所选监控模式
            const monitorExpected = selectedModes.includes('expected');
            const monitorProbability = selectedModes.includes('probability');
            const monitorWrongPrice = selectedModes.includes('wrongPrice');
            const monitorSpecific = selectedModes.includes('specific');

            // 根据不同模式执行监控
            if (monitorExpected || monitorProbability) {
                await monitorSoapboxesForValueAndProbability();
            }
            
            if (monitorWrongPrice) {
                await monitorForWrongPrices();
            }
            
            if (monitorSpecific) {
                await monitorSpecificChests();
            }

        } catch (error) {
            addMonitorLog(`监控执行出错: ${error.message}`, true);
        }
    }

    // 期望与概率监控
    async function monitorSoapboxesForValueAndProbability() {
        try {
            // 获取肥皂盒列表 (最多3个)
            const chestListResponse = await fetch('/api/chests?page_number=1&page_size=3&sort=sortOrder&order=asc');
            if (!chestListResponse.ok) {
                throw new Error('获取肥皂盒列表失败');
            }
            
            const chestListData = await chestListResponse.json();
            if (!chestListData.result || !Array.isArray(chestListData.result.content)) {
                throw new Error('肥皂盒列表数据格式不正确');
            }
            
            const chests = chestListData.result.content;
            addMonitorLog(`获取到 ${chests.length} 个肥皂盒`);
            
            // 为每个肥皂盒执行检查
            for (const chest of chests) {
                await checkSingleChest(chest);
            }
        } catch (error) {
            addMonitorLog(`肥皂盒列表监控异常: ${error.message}`, true);
        }
    }

    // 检查单个肥皂盒
    async function checkSingleChest(chest) {
        try {
            const chestId = chest.id;
            const chestName = chest.name || '未命名肥皂盒';
            const singleDrawPrice = Number(chest.oncePrice);
            
            addMonitorLog(`检查肥皂盒: ${chestName} (${chestId})`);
            
            try {
                // 获取肥皂盒游戏列表
                const gamesResponse = await fetch(`/api/xboot/chest/showGame?chestId=${chestId}&pageNumber=1&pageSize=100&sort=lv&order=asc`);
                if (!gamesResponse.ok) {
                    throw new Error(`获取肥皂盒 ${chestId} 游戏列表失败`);
                }
                
                const gamesData = await gamesResponse.json();
                let games = [];
                
                // 尝试不同的数据结构格式
                if (gamesData.result && Array.isArray(gamesData.result.records)) {
                    games = gamesData.result.records;
                } else if (gamesData.result && Array.isArray(gamesData.result.content)) {
                    games = gamesData.result.content;
                } else {
                    console.error('肥皂盒游戏列表数据格式:', gamesData);
                    throw new Error('肥皂盒游戏列表数据格式不正确');
                }
                
                addMonitorLog(`肥皂盒 ${chestName} 包含 ${games.length} 款游戏`);
                
                // 获取肥皂盒详情（用于概率等信息）
                const chestDetailResponse = await fetch(`/api/xboot/chest/showOne?chestId=${chestId}`);
                if (!chestDetailResponse.ok) {
                    throw new Error(`获取肥皂盒 ${chestId} 详情失败`);
                }
                
                const chestDetail = await chestDetailResponse.json();
                if (!chestDetail.result) {
                    throw new Error('肥皂盒详情数据格式不正确');
                }
                
                const chestDetailData = chestDetail.result || {};
                
                // 检查期望价值
                const selectedModes = Array.from(monitorModeCheckboxes)
                    .filter(checkbox => checkbox.checked)
                    .map(checkbox => checkbox.value);
                    
                if (selectedModes.includes('expected')) {
                    // 计算期望值
                    const expectationResult = await calculateChestExpectation(chestDetailData, games);
                    
                    // 确保期望值计算成功并且ratio属性存在
                    if (expectationResult.success && (expectationResult.ratio !== undefined || expectationResult.expectedRatio !== undefined)) {
                        // 使用ratio或fallback到expectedRatio
                        const ratio = expectationResult.ratio !== undefined ? expectationResult.ratio : expectationResult.expectedRatio;
                        
                        addMonitorLog(`肥皂盒 ${chestName} 期望倍率: ${ratio.toFixed(3)}`);
                        
                        // 如果期望值倍率大于1.1，可能是好价
                        if (ratio > 1.1) {
                            addMonitorLog(`肥皂盒 ${chestName} 期望倍率大于1.1，可能是好价`, false, true);
                            
                            // 记录到命中记录
                            addMonitorHit(chestId, 'expected', singleDrawPrice, chest.multiPrice || singleDrawPrice * 10);
                            
                            // 发送通知邮件
                            const subject = `【期望好价肥皂盒】${chestName}`;
                            let body = `
发现期望倍率较高的肥皂盒：
ID: ${chestId}
名称: ${chestName}
单抽价格: ¥${singleDrawPrice.toFixed(2)}
期望倍率: ${ratio.toFixed(3)}
命中时间: ${new Date().toLocaleString('zh-CN')}
                            `;
                            
                            await sendNotificationEmail(subject, body);
                            
                            // 如果开启了自动下单，执行下单
                            // if (autoOrderCheckbox.checked) { // 移除自动下单
                            //     addMonitorLog(`自动下单已启用，正在下单肥皂盒 ${chestId}`);
                            //     await placeChestOrder(chestId);
                            // }
                        }
                    } else {
                        // 期望值计算失败
                        const errorMsg = expectationResult.message || '未知原因';
                        addMonitorLog(`肥皂盒 ${chestName} 期望值计算失败: ${errorMsg}`, true);
                    }
                }
                
                // 检查概率特殊情况（lv=1 且 stock>0 的游戏价格全部大于单抽价格+10）
                if (selectedModes.includes('probability')) {
                    // 检查rate1是否等于100%
                    const rate1 = Number(chestDetailData.rate1) || 0;
                    const rate1Percent = (rate1 * 100) || 0;
                    const rate1PercentFormatted = rate1Percent.toFixed(2);
                    
                    if (rate1 !== 1) {
                        addMonitorLog(`肥皂盒 ${chestName} 的rate1概率为${rate1PercentFormatted}%，不等于100%，不满足条件`);
                        return;
                    }
                    
                    const level1Games = games.filter(game => game.lv === 1 && Number(game.stock || 0) > 0);
                    
                    if (level1Games.length > 0) {
                        // 收集所有lv1游戏ID
                        const level1GameIds = level1Games.map(game => game.gameId);
                        
                        // 批量获取缓存价格
                        let cachedPricesMap = await fetchCachedPrices(level1GameIds);
                        
                        // 检查是否所有lv1游戏价格都大于单抽价格+10
                        let allPricesGreater = true;
                        let missedPrices = 0;
                        
                        for (const game of level1Games) {
                            if (cachedPricesMap[game.gameId]) {
                                if (cachedPricesMap[game.gameId] <= (singleDrawPrice + 10)) {
                                    allPricesGreater = false;
                                    break;
                                }
                            } else {
                                missedPrices++;
                            }
                        }
                        
                        if (allPricesGreater && missedPrices === 0) {
                            addMonitorLog(`肥皂盒 ${chestName} 的rate1概率为100%，所有有库存LV1游戏价格都大于单抽价格+10，可能是好价`, false, true);
                            
                            // 记录到命中记录
                            addMonitorHit(chestId, 'probability', singleDrawPrice, chest.multiPrice || singleDrawPrice * 10);
                            
                            // 发送通知邮件
                            const subject = `【概率好价肥皂盒】${chestName}`;
                            let body = `
发现概率优秀的肥皂盒：
ID: ${chestId}
名称: ${chestName}
单抽价格: ¥${singleDrawPrice.toFixed(2)}
rate1概率: ${rate1PercentFormatted}%
命中时间: ${new Date().toLocaleString('zh-CN')}
                            `;
                            
                            await sendNotificationEmail(subject, body);
                            
                            // 如果开启了自动下单，执行下单
                            if (autoOrderCheckbox.checked) {
                                addMonitorLog(`自动下单已启用，正在下单肥皂盒 ${chestId}`);
                                await placeChestOrder(chestId);
                            }
                        }
                    }
                }
                
            } catch (error) {
                addMonitorLog(`检查肥皂盒失败: ${error.message}`, true);
            }
        } catch (error) {
            addMonitorLog(`检查肥皂盒失败: ${error.message}`, true);
        }
    }

    // 错价监控
    async function monitorForWrongPrices() {
        try {
            // 获取最新的肥皂盒列表（最多10个）
            const chestListResponse = await fetch('/api/chests?page_number=1&page_size=10&sort=chestNo&order=desc');
            if (!chestListResponse.ok) {
                throw new Error('获取肥皂盒列表失败');
            }
            
            const chestListData = await chestListResponse.json();
            if (!chestListData.result || !Array.isArray(chestListData.result.content)) {
                throw new Error('肥皂盒列表数据格式不正确');
            }
            
            const chests = chestListData.result.content;
            addMonitorLog(`错价检查: 获取到 ${chests.length} 个最新肥皂盒`);
            
            // 检查每个肥皂盒是否有错价
            for (const chest of chests) {
                const chestId = chest.id;
                const chestName = chest.name || '未命名肥皂盒';
                const multiDraw = Number(chest.multiDraw || 0);
                const oncePrice = Number(chest.oncePrice || 0);
                const multiPrice = Number(chest.multiPrice || 0);
                
                // 如果多抽价格与单抽价格差异大于8元，可能是错价
                if (multiDraw > 1 && oncePrice > 0 && multiPrice > 0) {
                    const expectedMultiPrice = multiDraw * oncePrice;
                    const priceDifference = expectedMultiPrice - multiPrice;
                    
                    // 如果价格差异大于10%，可能是错价
                    if (priceDifference > 0 && priceDifference > multiPrice * 0.1) {
                        // 计算折扣百分比并格式化（保护toFixed调用）
                        const discountPercent = ((priceDifference / expectedMultiPrice) * 100) || 0;
                        const discountPercentFormatted = discountPercent.toFixed(1);
                        
                        addMonitorLog(`肥皂盒 ${chestName} 多抽折扣 ${discountPercentFormatted}%，可能是错价!`, false, true);
                        
                        // 记录到命中记录
                        addMonitorHit(chestId, 'wrongPrice', oncePrice, multiPrice);
                        
                        // 发送通知邮件
                        const subject = `【错价肥皂盒】${chestName} 折扣${discountPercentFormatted}%`;
                        const body = `
发现错价肥皂盒：
ID: ${chestId}
名称: ${chestName}
单抽价格: ¥${oncePrice.toFixed(2)}
${multiDraw}抽价格: ¥${multiPrice.toFixed(2)} (正常应为 ¥${expectedMultiPrice.toFixed(2)})
折扣: ${discountPercentFormatted}%
命中时间: ${new Date().toLocaleString('zh-CN')}
                        `;
                        
                        await sendNotificationEmail(subject, body);
                        
                        // 如果开启了自动下单，执行下单
                        // if (autoOrderCheckbox.checked) { // 移除自动下单
                        //     addMonitorLog(`自动下单已启用，正在下单肥皂盒 ${chestId}`);
                        //     await placeChestOrder(chestId);
                        // }
                    }
                }
            }
        } catch (error) {
            addMonitorLog(`错价监控异常: ${error.message}`, true);
        }
    }

    // 从localStorage读取特定监控数据
    function loadSpecificMonitorData() {
        try {
            const savedData = localStorage.getItem('specificMonitorData');
            if (savedData) {
                specificMonitorData = JSON.parse(savedData);
            }
        } catch (e) {
            console.error('读取特定监控数据失败', e);
            specificMonitorData = {};
        }
    }
    
    // 保存特定监控数据到localStorage
    function saveSpecificMonitorData() {
        try {
            localStorage.setItem('specificMonitorData', JSON.stringify(specificMonitorData));
        } catch (e) {
            console.error('保存特定监控数据失败', e);
        }
    }
    
    // 初始化特定监控数据
    loadSpecificMonitorData();
    
    // 特定肥皂盒监控
    async function monitorSpecificChests() {
        try {
            // 获取收藏的肥皂盒列表
            const favoriteChests = getFavoriteChests();
            
            if (!favoriteChests || favoriteChests.length === 0) {
                addMonitorLog('没有收藏的肥皂盒可供监控', true);
                return;
            }
            
            addMonitorLog(`特定监控: 找到 ${favoriteChests.length} 个收藏的肥皂盒`);
            
            // 对每个收藏的肥皂盒进行检查
            for (const favorite of favoriteChests) {
                const chestId = favorite.id;
                if (!chestId) continue;
                
                // 检查是否被标记为失效
                if (specificMonitorData[chestId] && specificMonitorData[chestId].disabled) {
                    addMonitorLog(`肥皂盒 ${favorite.name || chestId} 已标记为失效，跳过监控`);
                    continue;
                }
                
                try {
                    // 使用正确的API路径获取肥皂盒详情
                    const chestResponse = await fetch(`/api/xboot/chest/showOne?chestId=${chestId}`);
                    if (!chestResponse.ok) {
                        addMonitorLog(`获取收藏肥皂盒 ${chestId} 详情失败: ${chestResponse.status}`, true);
                        continue;
                    }
                    
                    const chestData = await chestResponse.json();
                    if (!chestData.result) {
                        addMonitorLog(`肥皂盒 ${chestId} 数据格式不正确`, true);
                        continue;
                    }
                    
                    // 转换为一致的格式
                    const chest = {
                        id: chestId,
                        name: chestData.result.name || favorite.name || '未知肥皂盒',
                        oncePrice: chestData.result.oncePrice,
                        multiPrice: chestData.result.multiPrice,
                        multidraw: chestData.result.multidraw,
                        totalDraw: chestData.result.totalDraw || 0,
                        curDraw: chestData.result.curDraw || 0
                    };
                    
                    // 确保所有必要的属性都已初始化
                    if (!specificMonitorData[chestId]) {
                        specificMonitorData[chestId] = {
                            name: chest.name,
                            lastCurDraw: chest.curDraw,
                            drawIncreases: [],
                            cycleCurDraw: chest.curDraw,
                            cycleIncreasesCount: 0,
                            disabled: false
                        };
                        addMonitorLog(`首次监控肥皂盒 ${chest.name}，初始抽取数量：${chest.curDraw}`);
                    } else {
                        // 确保所有属性都存在
                        specificMonitorData[chestId].name = specificMonitorData[chestId].name || chest.name;
                        specificMonitorData[chestId].lastCurDraw = specificMonitorData[chestId].lastCurDraw || chest.curDraw;
                        specificMonitorData[chestId].drawIncreases = specificMonitorData[chestId].drawIncreases || [];
                        specificMonitorData[chestId].cycleCurDraw = specificMonitorData[chestId].cycleCurDraw || chest.curDraw;
                        specificMonitorData[chestId].cycleIncreasesCount = (specificMonitorData[chestId].cycleIncreasesCount || 0) + 1;
                        specificMonitorData[chestId].disabled = specificMonitorData[chestId].disabled || false;
                    }
                    
                    // 获取上次的curDraw
                    const lastCurDraw = specificMonitorData[chestId].lastCurDraw || 0;
                    
                    // 检查curDraw是否变化
                    if (chest.curDraw > lastCurDraw) {
                        // 计算增加量
                        const increase = chest.curDraw - lastCurDraw;
                        addMonitorLog(`肥皂盒 ${chest.name} 抽取数量上升: ${lastCurDraw} -> ${chest.curDraw} (增加${increase})`, false, true);
                        
                        // 记录本次增加量
                        specificMonitorData[chestId].drawIncreases = specificMonitorData[chestId].drawIncreases || [];
                        specificMonitorData[chestId].drawIncreases.push({
                            timestamp: Date.now(),
                            amount: increase,
                            total: chest.curDraw
                        });
                        
                        // 限制历史记录长度
                        if (specificMonitorData[chestId].drawIncreases.length > 20) {
                            specificMonitorData[chestId].drawIncreases.shift();
                        }
                        
                        // 检查当前循环中的增加次数是否达到阈值
                        if (specificMonitorData[chestId].cycleIncreasesCount >= 4) {
                            // 当前循环中抽取次数增加了4次或以上
                            addMonitorLog(`肥皂盒 ${chest.name} 在当前监控周期内抽取数量增加了${specificMonitorData[chestId].cycleIncreasesCount}次，可能热门`, false, true);
                            
                            // 计算总抽取量
                            const totalIncrease = chest.curDraw - specificMonitorData[chestId].cycleCurDraw;
                            
                            // 发送通知邮件
                            const subject = `【特定监控肥皂盒】${chest.name} 抽取频繁`;
                            let body = `
收藏肥皂盒抽取频繁：
ID: ${chestId}
名称: ${chest.name}
单抽价格: ¥${chest.oncePrice.toFixed(2)}
当前抽取数量: ${chest.curDraw}
监控开始时数量: ${specificMonitorData[chestId].cycleCurDraw}
总抽取量: ${totalIncrease}
本周期增加次数: ${specificMonitorData[chestId].cycleIncreasesCount}
总数量: ${chest.totalDraw}
剩余数量: ${chest.totalDraw - chest.curDraw}
命中时间: ${new Date().toLocaleString('zh-CN')}
                            `;
                            
                            await sendNotificationEmail(subject, body);
                            
                            // 重置循环
                            specificMonitorData[chestId].cycleCurDraw = chest.curDraw;
                            specificMonitorData[chestId].cycleIncreasesCount = 0;
                            

                        
                        }
                        
                        // 如果单次增加量达到8个或以上，也发送通知
                        if (increase >= 8) {
                            addMonitorLog(`肥皂盒 ${chest.name} 单次抽取数量增加了${increase}个，发送通知`, false, true);
                            
                            // 发送通知邮件
                            const subject = `【特定监控肥皂盒】${chest.name} 大量抽取`;
                            let body = `
收藏肥皂盒单次大量抽取：
ID: ${chestId}
名称: ${chest.name}
单抽价格: ¥${chest.oncePrice.toFixed(2)}
抽取前数量: ${lastCurDraw}
当前抽取数量: ${chest.curDraw}
单次抽取量: ${increase}
总数量: ${chest.totalDraw}
剩余数量: ${chest.totalDraw - chest.curDraw}
命中时间: ${new Date().toLocaleString('zh-CN')}
                            `;
                            
                            await sendNotificationEmail(subject, body);
                            
                            // 如果开启了自动下单，执行下单

                        }
                    } else if (chest.curDraw < lastCurDraw) {
                        // curDraw减少了，可能是肥皂盒被重置
                        addMonitorLog(`肥皂盒 ${chest.name} 抽取数量异常: ${lastCurDraw} -> ${chest.curDraw}，可能是重置`, true);
                        
                        // 重置循环
                        specificMonitorData[chestId].cycleCurDraw = chest.curDraw;
                        specificMonitorData[chestId].cycleIncreasesCount = 0;
                    }
                    
                    // 更新lastCurDraw
                    specificMonitorData[chestId].lastCurDraw = chest.curDraw;
                    
                    // 保存特定监控数据
                    saveSpecificMonitorData();
                    
                    // 计算剩余数量
                    const remainingDraws = chest.totalDraw - chest.curDraw;
                    
                    // 单独处理游戏列表
                    try {
                        // 获取肥皂盒游戏列表
                        const gamesResponse = await fetch(`/api/xboot/chest/showGame?chestId=${chestId}&pageNumber=1&pageSize=100&sort=lv&order=asc`);
                        if (!gamesResponse.ok) {
                            addMonitorLog(`获取肥皂盒 ${chest.name} (${chestId}) 游戏列表失败`, true);
                            continue;
                        }
                        
                        const gamesData = await gamesResponse.json();
                        let games = [];
                        
                        // 尝试不同的数据结构格式
                        if (gamesData.result && Array.isArray(gamesData.result.records)) {
                            games = gamesData.result.records;
                        } else if (gamesData.result && Array.isArray(gamesData.result.content)) {
                            games = gamesData.result.content;
                        } else {
                            addMonitorLog(`肥皂盒 ${chest.name} (${chestId}) 游戏列表数据格式不正确`, true);
                            continue;
                        }
                        
                        addMonitorLog(`肥皂盒 ${chest.name} 包含 ${games.length} 款游戏，剩余数量: ${remainingDraws}`);
                        
                        // 检查标记规则
                        const rate1 = Number(chestData.result.rate1) || 0;
                        const level1Games = games.filter(game => game.lv === 1 && Number(game.stock || 0) > 0);
                        
                        // 检查是否需要标记为失效
                        if (remainingDraws <= 0) {
                            addMonitorLog(`肥皂盒 ${chest.name} 剩余数量为0，标记为失效`, true);
                            specificMonitorData[chestId].disabled = true;
                            saveSpecificMonitorData();
                            continue;
                        }
                        
                        if (rate1 === 0) {
                            addMonitorLog(`肥皂盒 ${chest.name} lv1概率为0，标记为失效`, true);
                            specificMonitorData[chestId].disabled = true;
                            saveSpecificMonitorData();
                            continue;
                        }
                        
                        // 检查lv1游戏价格
                        if (level1Games.length > 0) {
                            // 收集所有lv1游戏ID
                            const level1GameIds = level1Games.map(game => game.gameId);
                            
                            // 批量获取缓存价格
                            let cachedPricesMap = await fetchCachedPrices(level1GameIds);
                            
                            // 检查是否所有lv1游戏价格都小于单抽价格
                            let allPricesLower = true;
                            let missedPrices = 0;
                            
                            for (const game of level1Games) {
                                if (cachedPricesMap[game.gameId]) {
                                    if (cachedPricesMap[game.gameId] >= chest.oncePrice) {
                                        allPricesLower = false;
                                        break;
                                    }
                                } else {
                                    missedPrices++;
                                }
                            }
                            
                            if (allPricesLower && missedPrices === 0) {
                                addMonitorLog(`肥皂盒 ${chest.name} 所有lv1游戏价格都小于单抽价格，标记为失效`, true);
                                specificMonitorData[chestId].disabled = true;
                                saveSpecificMonitorData();
                                continue;
                            }
                        }
                        
                        // 当前剩余数量小于等于10时发送通知
                        if (remainingDraws <= 10 && remainingDraws > 0) {
                            addMonitorLog(`肥皂盒 ${chest.name} 剩余数量仅为${remainingDraws}，即将售罄`, false, true);
                            
                            // 发送通知邮件
                            const subject = `【特定监控肥皂盒】${chest.name} 即将售罄`;
                            let body = `
收藏肥皂盒即将售罄：
ID: ${chestId}
名称: ${chest.name}
单抽价格: ¥${chest.oncePrice.toFixed(2)}
已抽数量: ${chest.curDraw}
总数量: ${chest.totalDraw}
剩余数量: ${remainingDraws}
命中时间: ${new Date().toLocaleString('zh-CN')}
                            `;
                            
                            await sendNotificationEmail(subject, body);
                            
                        }
                        
                        // 检查监控条件（概率监控）
                        await monitorChestConditions(chest, games, chestData.result);
                    } catch (error) {
                        addMonitorLog(`处理肥皂盒 ${chest.name} (${chestId}) 游戏列表出错: ${error.message}`, true);
                    }
                } catch (error) {
                    addMonitorLog(`获取肥皂盒 ${chestId} 信息时出错: ${error.message}`, true);
                }
            }
        } catch (error) {
            addMonitorLog(`特定肥皂盒监控异常: ${error.message}`, true);
        }
    }
    
    // 监控肥皂盒条件
    async function monitorChestConditions(chest, games, chestDetailData) {
        const chestId = chest.id;
        const chestName = chest.name;
        const singleDrawPrice = Number(chest.oncePrice);
        
        // 检查期望价值
        const selectedModes = Array.from(monitorModeCheckboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => checkbox.value);
            
        if (selectedModes.includes('expected')) {
            // 计算期望值
            const expectationResult = await calculateChestExpectation(chestDetailData, games);
            
            // 确保期望值计算成功并且ratio属性存在
            if (expectationResult.success && (expectationResult.ratio !== undefined || expectationResult.expectedRatio !== undefined)) {
                // 使用ratio或fallback到expectedRatio
                const ratio = expectationResult.ratio !== undefined ? expectationResult.ratio : expectationResult.expectedRatio;
                
                addMonitorLog(`肥皂盒 ${chestName} 期望倍率: ${ratio.toFixed(3)}`);
                
                // 如果期望值倍率大于1.1，可能是好价
                if (ratio > 1.1) {
                    addMonitorLog(`肥皂盒 ${chestName} 期望倍率大于1.1，可能是好价`, false, true);
                    
                    // 记录到命中记录
                    addMonitorHit(chestId, 'expected', singleDrawPrice, chest.multiPrice || singleDrawPrice * 10);
                    
                    // 发送通知邮件
                    const subject = `【特定监控肥皂盒】${chestName}`;
                    let body = `
收藏肥皂盒期望值较高：
ID: ${chestId}
名称: ${chestName}
单抽价格: ¥${singleDrawPrice.toFixed(2)}
期望倍率: ${ratio.toFixed(3)}
命中时间: ${new Date().toLocaleString('zh-CN')}
                    `;
                    
                    await sendNotificationEmail(subject, body);
                    
                    // 如果开启了自动下单，执行下单
                    // if (autoOrderCheckbox.checked) { // 移除自动下单
                    //     addMonitorLog(`自动下单已启用，正在下单肥皂盒 ${chestId}`);
                    //     await placeChestOrder(chestId);
                    // }
                }
            } else {
                // 期望值计算失败
                const errorMsg = expectationResult.message || '未知原因';
                addMonitorLog(`肥皂盒 ${chestName} 期望值计算失败: ${errorMsg}`, true);
            }
        }
        
        // 检查概率特殊情况
        if (selectedModes.includes('probability')) {
            // 检查rate1是否等于1（100%）
            const rate1 = Number(chestDetailData.rate1) || 0;
            const rate1Percent = (rate1 * 100) || 0;
            const rate1PercentFormatted = rate1Percent.toFixed(2);

            if (rate1 !== 1) {
                addMonitorLog(`肥皂盒 ${chestName} 的rate1概率为${rate1PercentFormatted}%，不等于100%，不满足条件`);
                return;
            }

            // 只检查有库存的LV1游戏
            const level1Games = games.filter(game => game.lv === 1 && Number(game.stock || 0) > 0);

            if (level1Games.length > 0) {
                // 收集所有lv1游戏ID
                const level1GameIds = level1Games.map(game => game.gameId);

                // 批量获取缓存价格
                let cachedPricesMap = await fetchCachedPrices(level1GameIds);

                // 检查是否所有有库存的lv1游戏价格都大于单抽价格+10
                let allPricesGreater = true;
                let missedPrices = 0;

                for (const game of level1Games) {
                    if (cachedPricesMap[game.gameId]) {
                        if (cachedPricesMap[game.gameId] <= (singleDrawPrice + 10)) {
                            allPricesGreater = false;
                            break;
                        }
                    } else {
                        missedPrices++;
                    }
                }

                if (allPricesGreater && missedPrices === 0) {
                    addMonitorLog(`肥皂盒 ${chestName} 的rate1概率为100%，所有有库存LV1游戏价格都大于单抽价格+10，可能是好价`, false, true);
                    addMonitorHit(chestId, 'probability', singleDrawPrice, chest.multiPrice || singleDrawPrice * 10);
                    
                    // 发送通知邮件
                    const subject = `【特定监控肥皂盒】${chestName}`;
                    let body = `
收藏肥皂盒概率优秀：
ID: ${chestId}
名称: ${chestName}
单抽价格: ¥${singleDrawPrice.toFixed(2)}
rate1概率: ${rate1PercentFormatted}%
命中时间: ${new Date().toLocaleString('zh-CN')}
                    `;
                    
                    await sendNotificationEmail(subject, body);
                    
                    // 如果开启了自动下单，执行下单
                    if (autoOrderCheckbox.checked) {
                        addMonitorLog(`自动下单已启用，正在下单肥皂盒 ${chestId}`);
                        await placeChestOrder(chestId);
                    }
                }
            }
        }
    }

    // 计算肥皂盒期望值
    async function calculateChestExpectation(chestDetail, games) {
        if (!chestDetail || !games || !games.length) {
            return { success: false, message: '数据不完整，无法计算期望' };
        }
        
        try {
            let totalExpectedValue = 0;
            
            // 获取单抽成本
            const singleDrawCost = Number(chestDetail.oncePrice);
            if (isNaN(singleDrawCost)) {
                return { success: false, message: '未能获取到有效的单抽成本' };
            }
            
            // 收集所有有效游戏ID
            const allGameIdsInChest = [...new Set(games
                .filter(game => game.gameId && Number(game.stock ?? 0) > 0)
                .map(game => game.gameId))];
            
            // 如果没有有效游戏ID
            if (allGameIdsInChest.length === 0) {
                return { success: false, message: '肥皂盒中没有库存大于0的游戏，无法计算期望' };
            }
            
            // 批量获取缓存价格
            let cachedPricesMap = await fetchCachedPrices(allGameIdsInChest);
            
            // 对于缓存中没有的价格，单独获取
            const missingIds = allGameIdsInChest.filter(id => cachedPricesMap[id] === undefined);
            if (missingIds.length > 0) {
                addMonitorLog(`正在获取 ${missingIds.length} 个游戏的价格...`);
                
                // 使用Promise.all批量处理所有缺失的价格请求
                const pricePromises = missingIds.map(async gameId => {
                    try {
                        // 查找游戏对象以获取游戏名称
                        const gameObj = games.find(game => game.gameId === gameId);
                        const gameName = gameObj ? gameObj.gameNameCn || gameObj.name : null;
                        
                        const price = await getGamePrice(gameId, gameName);
                        if (price !== undefined) {
                            return { gameId, price };
                        }
                    } catch (error) {
                        console.error(`无法获取游戏 ${gameId} 的价格:`, error);
                    }
                    return null;
                });
                
                // 等待所有价格请求完成
                const results = await Promise.all(pricePromises);
                
                // 将获取到的价格添加到价格映射中
                results.forEach(result => {
                    if (result && result.gameId && result.price !== undefined) {
                        cachedPricesMap[result.gameId] = result.price;
                    }
                });
            }
            
            // 按等级分组游戏
            const gamesByLevel = games.reduce((acc, game) => {
                const level = game.lv ?? '未知';
                if (!acc[level]) acc[level] = [];
                acc[level].push(game);
                return acc;
            }, {});
            
            // 处理每个等级
            for (const level of Object.keys(gamesByLevel)) {
                if (level === '未知') continue;
                
                // 获取当前等级的概率
                const levelRate = Number(chestDetail[`rate${level}`]);
                
                if (isNaN(levelRate) || levelRate === 0) continue;
                
                const gamesInLevel = gamesByLevel[level];
                let weightedSumValueInLevel = 0;
                
                // 计算该等级中所有有效游戏的总库存
                let totalStockInLevel = gamesInLevel.reduce(
                    (sum, game) => sum + (Number(game.stock ?? 0) > 0 ? Number(game.stock) : 0),
                    0
                );
                
                if (totalStockInLevel <= 0) continue;
                
                // 处理该等级中的每个游戏
                for (const game of gamesInLevel) {
                    const gameId = game.gameId;
                    const gameStock = Number(game.stock ?? 0);
                    
                    if (!gameId || gameStock <= 0) continue;
                    
                    // 获取游戏价格
                    const itemPrice = cachedPricesMap[gameId] || 0;
                    
                    if (itemPrice > 0) {
                        // 计算该游戏在等级内的概率（基于库存比例）
                        const probabilityOfThisItemInLevel = gameStock / totalStockInLevel;
                        
                        // 累加到该等级的总期望值
                        weightedSumValueInLevel += probabilityOfThisItemInLevel * itemPrice;
                    }
                }
                
                // 计算该等级对总期望的贡献
                totalExpectedValue += levelRate * weightedSumValueInLevel;
            }
            
            // 计算期望比率
            const expectedRatio = totalExpectedValue / singleDrawCost;
            
            return {
                success: true,
                expectedValue: totalExpectedValue,
                singleDrawCost: singleDrawCost,
                expectedRatio: expectedRatio,
                ratio: expectedRatio // 添加ratio作为expectedRatio的别名，确保兼容性
            };
        } catch (error) {
            console.error('计算肥皂盒期望时出错:', error);
            return { success: false, message: `计算出错: ${error.message}` };
        }
    }
    
    // 从缓存获取游戏价格
    async function fetchCachedPrices(gameIds) {
        if (!gameIds || !gameIds.length) return {};
        
        try {
            // 构建查询参数，只获取需要的游戏ID的缓存价格
            const queryParams = gameIds.map(id => `gameIds=${encodeURIComponent(id)}`).join('&');
            
            // 请求缓存价格，只获取指定ID的价格
            const response = await fetch(`/api/games/cached-prices?${queryParams}`);
            
            if (!response.ok) {
                console.warn('从缓存获取价格失败:', await response.text());
                return {};
            }
            
            const data = await response.json();
            
            // 转换为 gameId -> price 的映射
            const priceMap = {};
            if (Array.isArray(data)) {
                data.forEach(item => {
                    if (item && item.game_id && item.price !== undefined) {
                        priceMap[item.game_id] = Number(item.price);
                    }
                });
            }
            
            return priceMap;
        } catch (error) {
            console.error('获取缓存价格出错:', error);
            return {};
        }
    }
    
    // 获取游戏价格，直接从API获取
    async function getGamePrice(gameId, gameName = null) {
        try {
            // 使用sellers API获取价格，与chest_detail.js保持一致
            const url = `/api/games/${gameId}/sellers?pageSize=1`;
            const response = await fetch(url);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }));
                console.error(`获取游戏价格失败: ${errorData.detail || '请求失败'}`);
                return 0;
            }
            
            const sellersData = await response.json();
            
            // 检查返回的卖家数据
            if (Array.isArray(sellersData) && sellersData.length > 0) {
                const firstSeller = sellersData[0];
                if (firstSeller && typeof firstSeller.keyPrice === 'number') {
                    const price = firstSeller.keyPrice;
                    
                    // 尝试获取游戏名称（如果firstSeller包含游戏名）
                    if (firstSeller.gameName && !gameName) {
                        gameName = firstSeller.gameName;
                    }
                    
                    // 确保不将ID写入到游戏名称字段，但如果有有效的名称，则写入数据库
                    if (gameName && gameName !== gameId) {
                        await updatePriceInBackendCache(gameId, gameName, price);
                    }
                    
                    console.log(`获取到游戏 ${gameName || gameId} 的价格: ${price}`);
                    return price;
                }
            }
            
            console.warn(`没有找到游戏 ${gameName || gameId} 的有效价格`);
            return 0;
        } catch (error) {
            console.error(`获取游戏 ${gameId} 价格失败:`, error);
            return 0; // 出错时返回0，避免监控中断
        }
    }
    
    // 更新价格到数据库缓存
    async function updatePriceInBackendCache(gameId, gameName, price) {
        if (!gameId || price === undefined) return false;
        
        // 确保游戏名不是ID本身，以防止ID被错误地写入名称字段
        if (!gameName || gameName === gameId) {
            gameName = ''; // 使用空字符串而不是ID
        }
        
        try {
            const response = await fetch('/api/games/cache-price', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    game_id: gameId,
                    name: gameName, // 确保使用有效的游戏名或空字符串
                    price: price
                })
            });
            
            if (!response.ok) {
                console.warn(`更新价格缓存失败: ${gameId}`, await response.text());
                return false;
            }
            
            return true;
        } catch (error) {
            console.error(`更新价格缓存出错: ${gameId}`, error);
            return false;
        }
    }
    
    // 下单肥皂盒
    async function placeChestOrder(chestId) {
        try {
            // 检查是否已经下过单
            const orderedChests = JSON.parse(localStorage.getItem('ordered_chests') || '[]');
            if (orderedChests.includes(chestId)) {
                addMonitorLog(`肥皂盒 ${chestId} 已经尝试过下单，不再重复下单`, false);
                return false;
            }

            // 获取当前有效的下单配置（自动根据时间切换）
            const orderSettings = getChestOrderSettings();
            
            // 如果是夜间模式，添加标识
            if (orderSettings.isNightMode) {
                addMonitorLog(`🌙 当前为夜间模式，使用夜间配置进行下单`, false);
            }

            // 构建API请求参数
            const data = {
                chestId: chestId,
                draws: 1, // 只进行单抽
                payType: orderSettings.chest.payType,
                useMainAccount: orderSettings.chest.useMainAccount,
                useBalance: orderSettings.chest.useBalance,
                orderType: "chest" // 标识这是肥皂盒下单
            };
            
            // 发送下单请求
            const response = await fetch('/api/chest/place-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 下单成功，记录到已下单列表
                orderedChests.push(chestId);
                localStorage.setItem('ordered_chests', JSON.stringify(orderedChests));
                addMonitorLog(`肥皂盒 ${chestId} 自动下单成功！支付链接已发送到邮箱`, false);
                return true;
            } else {
                // 记录失败也添加到已尝试列表，避免重复尝试失败的下单
                orderedChests.push(chestId);
                localStorage.setItem('ordered_chests', JSON.stringify(orderedChests));
                addMonitorLog(`肥皂盒 ${chestId} 下单失败: ${result.message}，已记录不再尝试`, true);
                return false;
            }
        } catch (error) {
            addMonitorLog(`肥皂盒 ${chestId} 下单异常: ${error.message}`, true);
            return false;
        }
    }
    
    // 发送通知邮件
    async function sendNotificationEmail(subject, body) {
        try {
            const response = await fetch('/api/send-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ subject, body })
            });
            
            if (!response.ok) {
                throw new Error('邮件发送请求失败');
            }
            
            const result = await response.json();
            
            if (result.success) {
                addMonitorLog(`通知邮件已发送: ${subject}`);
                return true;
            } else {
                addMonitorLog(`邮件发送失败: ${result.message}`, true);
                return false;
            }
        } catch (error) {
            addMonitorLog(`发送邮件时出错: ${error.message}`, true);
            return false;
        }
    }

    // 初始化加载已保存的命中记录
    function loadMonitorHits() {
        try {
            const savedHits = localStorage.getItem('monitorHits');
            if (savedHits) {
                const hits = JSON.parse(savedHits);
                if (hits && Array.isArray(hits) && hits.length > 0) {
                    // 清空默认的"无记录"提示
                    monitorHitsContainer.innerHTML = '';
                    
                    // 按时间倒序排序
                    hits.sort((a, b) => new Date(b.hitTime) - new Date(a.hitTime));
                    
                    // 显示最近的20条记录
                    const recentHits = hits.slice(0, 20);
                    for (const hit of recentHits) {
                        appendHitToTable(hit);
                    }
                }
            }
        } catch (error) {
            console.error('加载命中记录失败:', error);
        }
    }
    
    // 添加命中记录到表格
    function appendHitToTable(hit) {
        // 创建新行
        const row = document.createElement('tr');
        
        // 肥皂盒ID列（带链接）
        const idCell = document.createElement('td');
        const idLink = document.createElement('a');
        idLink.href = `/chest-detail?id=${hit.chestId}`;
        idLink.className = 'chest-id-link';
        idLink.textContent = hit.chestId;
        idCell.appendChild(idLink);
        
        // 监控类型列
        const typeCell = document.createElement('td');
        const typeBadge = document.createElement('span');
        typeBadge.className = `monitor-type-badge monitor-type-${hit.monitorType}`;
        
        // 根据监控类型设置显示文本
        let typeText = '未知';
        if (hit.monitorType === 'expected') {
            typeText = '期望';
        } else if (hit.monitorType === 'probability') {
            typeText = '概率';
        } else if (hit.monitorType === 'wrongPrice') {
            typeText = '错价';
        }
        typeBadge.textContent = typeText;
        typeCell.appendChild(typeBadge);
        
        // 单抽价格列
        const oncePriceCell = document.createElement('td');
        oncePriceCell.textContent = `¥${hit.oncePrice.toFixed(2)}`;
        
        // 多抽价格列
        const tenPriceCell = document.createElement('td');
        tenPriceCell.textContent = `¥${hit.tenPrice.toFixed(2)}`;
        
        // 命中时间列
        const timeCell = document.createElement('td');
        const hitDate = new Date(hit.hitTime);
        timeCell.textContent = hitDate.toLocaleString('zh-CN');
        
        // 将所有单元格添加到行
        row.appendChild(idCell);
        row.appendChild(typeCell);
        row.appendChild(oncePriceCell);
        row.appendChild(tenPriceCell);
        row.appendChild(timeCell);
        
        // 如果这是第一个元素，并且表格中只有"无记录"的提示行，则清空表格
        if (monitorHitsContainer.querySelector('tr.text-center.text-muted')) {
            monitorHitsContainer.innerHTML = '';
        }
        
        // 添加行到表格
        monitorHitsContainer.prepend(row); // 使用prepend添加到顶部
    }
    
    // 添加新的命中记录
    function addMonitorHit(chestId, monitorType, oncePrice, tenPrice) {
        try {
            const hitData = {
                chestId: chestId,
                monitorType: monitorType,
                oncePrice: oncePrice,
                tenPrice: tenPrice,
                hitTime: new Date().toISOString()
            };
            
            // 保存到表格UI
            appendHitToTable(hitData);
            
            // 保存到本地存储
            let hits = [];
            const savedHits = localStorage.getItem('monitorHits');
            if (savedHits) {
                hits = JSON.parse(savedHits);
                if (!Array.isArray(hits)) {
                    hits = [];
                }
            }
            
            // 添加新记录到数组开头
            hits.unshift(hitData);
            
            // 只保留最近100条记录
            if (hits.length > 100) {
                hits = hits.slice(0, 100);
            }
            
            // 保存回本地存储
            localStorage.setItem('monitorHits', JSON.stringify(hits));
        } catch (error) {
            console.error('保存命中记录失败:', error);
        }
    }

    // 加载收藏的肥皂盒
    function loadFavoriteChests(forceRefresh = false) {
        if (!favoriteChestsList) return;
        
        try {
            // 获取收藏的肥皂盒列表
            const favorites = getFavoriteChests();
            
            // 清空现有内容
            favoriteChestsList.innerHTML = '';
            
            if (!favorites || favorites.length === 0) {
                favoriteChestsList.innerHTML = '<div class="text-muted text-center py-4">暂无收藏的肥皂盒...</div>';
                return;
            }
            
            // 按添加时间排序 (最新的在前面)
            favorites.sort((a, b) => {
                return new Date(b.addTime || 0) - new Date(a.addTime || 0);
            });
            
            // 渲染收藏内容
            favorites.forEach(chest => {
                const chestCard = createFavoriteChestCard(chest);
                favoriteChestsList.appendChild(chestCard);
            });
            
            addMonitorLog(`已加载 ${favorites.length} 个收藏的肥皂盒`);
        } catch (error) {
            console.error('加载收藏肥皂盒失败:', error);
            favoriteChestsList.innerHTML = '<div class="text-danger text-center py-4">加载收藏肥皂盒失败</div>';
        }
    }
    
    // 创建收藏肥皂盒卡片
    function createFavoriteChestCard(chest) {
        const card = document.createElement('div');
        card.className = 'favorite-chest-item';
        card.dataset.chestId = chest.id;
        
        const nameEl = document.createElement('div');
        nameEl.className = 'favorite-chest-name';
        nameEl.textContent = chest.name || '未知肥皂盒';
        
        const idEl = document.createElement('div');
        idEl.className = 'favorite-chest-id';
        idEl.textContent = `ID: ${chest.id}`;
        
        const actionsEl = document.createElement('div');
        actionsEl.className = 'favorite-chest-actions';
        
        // 查看详情按钮
        const viewBtn = document.createElement('button');
        viewBtn.className = 'favorite-action-btn view';
        viewBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>';
        viewBtn.title = '查看详情';
        viewBtn.addEventListener('click', function() {
            window.open(`/chest-detail?id=${chest.id}`, '_blank');
        });
        
        actionsEl.appendChild(viewBtn);
        
        card.appendChild(nameEl);
        card.appendChild(idEl);
        card.appendChild(actionsEl);
        
        return card;
    }
    
    // 获取收藏的肥皂盒列表
    function getFavoriteChests() {
        const FAVORITE_CHESTS_KEY = 'favorite_chests';
        const favoritesStr = localStorage.getItem(FAVORITE_CHESTS_KEY);
        return favoritesStr ? JSON.parse(favoritesStr) : [];
    }

    // 清理特定监控数据
    function resetSpecificMonitorData() {
        try {
            specificMonitorData = {};
            localStorage.removeItem('specificMonitorData');
            addMonitorLog('已清空特定监控数据', false, true);
        } catch (e) {
            console.error('清空特定监控数据失败', e);
            addMonitorLog('清空特定监控数据失败: ' + e.message, true);
        }
    }
    
    // 重置特定肥皂盒的监控状态
    function resetChestMonitorStatus(chestId) {
        if (specificMonitorData[chestId]) {
            const chestName = specificMonitorData[chestId].name || chestId;
            
            // 重置所有监控属性
            specificMonitorData[chestId] = {
                name: chestName,
                lastCurDraw: 0,
                drawIncreases: [],
                cycleCurDraw: 0,
                cycleIncreasesCount: 0,
                disabled: false
            };
            
            saveSpecificMonitorData();
            addMonitorLog(`已重置肥皂盒 ${chestName} 的监控状态`, false, true);
        } else {
            addMonitorLog(`找不到肥皂盒 ${chestId} 的监控数据`, true);
        }
    }
    
    // 更新已禁用的肥皂盒列表
    function updateDisabledChestsList() {
        const disabledList = document.getElementById('disabledChestsList');
        if (!disabledList) return;
        
        // 清空当前列表
        disabledList.innerHTML = '';
        
        // 添加已禁用的肥皂盒
        let hasDisabled = false;
        for (const chestId in specificMonitorData) {
            if (specificMonitorData[chestId] && specificMonitorData[chestId].disabled) {
                hasDisabled = true;
                const item = document.createElement('li');
                item.className = 'list-group-item d-flex justify-content-between align-items-center';
                
                const nameSpan = document.createElement('span');
                nameSpan.textContent = specificMonitorData[chestId].name || chestId;
                item.appendChild(nameSpan);
                
                const resetBtn = document.createElement('button');
                resetBtn.textContent = '重置';
                resetBtn.className = 'btn btn-sm btn-info';
                resetBtn.addEventListener('click', function() {
                    resetChestMonitorStatus(chestId);
                    updateDisabledChestsList();
                });
                item.appendChild(resetBtn);
                
                disabledList.appendChild(item);
            }
        }
        
        // 如果没有禁用的肥皂盒，显示提示信息
        if (!hasDisabled) {
            const emptyItem = document.createElement('li');
            emptyItem.className = 'list-group-item text-center';
            emptyItem.textContent = '没有已禁用的肥皂盒';
            disabledList.appendChild(emptyItem);
        }
    }

    /**
     * 检查当前是否为夜间时间（肥皂盒监控版本）
     */
    function isNightTimeChest() {
        const enableNightMode = localStorage.getItem('enableNightMode') === 'true';
        if (!enableNightMode) return false;
        
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        
        const nightStartTime = localStorage.getItem('nightStartTime') || '22:00';
        const nightEndTime = localStorage.getItem('nightEndTime') || '06:30';
        
        const [startHour, startMin] = nightStartTime.split(':').map(Number);
        const [endHour, endMin] = nightEndTime.split(':').map(Number);
        
        const startMinutes = startHour * 60 + startMin;
        const endMinutes = endHour * 60 + endMin;
        
        // 处理跨日期的情况（如22:00到次日06:30）
        if (startMinutes > endMinutes) {
            return currentTime >= startMinutes || currentTime <= endMinutes;
        } else {
            return currentTime >= startMinutes && currentTime <= endMinutes;
        }
    }

    /**
     * 获取肥皂盒下单配置（根据时间自动切换）
     */
    function getChestOrderSettings() {
        const isNight = isNightTimeChest();
        
        const settings = {
            chest: {
                payType: localStorage.getItem(isNight ? 'nightChestPayType' : 'chestPayType') || 'AA',
                useBalance: localStorage.getItem(isNight ? 'nightChestUseBalance' : 'chestUseBalance') || '',
                useMainAccount: localStorage.getItem(isNight ? 'nightChestUseMainAccount' : 'chestUseMainAccount') !== 'false'
            },
            isNightMode: isNight
        };
        
        return settings;
    }

    // 将函数暴露到全局，供其他模块使用
    window.getChestOrderSettings = getChestOrderSettings;
    window.isNightTimeChest = isNightTimeChest;
});