import asyncio
import json
import logging
import os
import random
import time
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urlparse

import httpx  # 用于HTTP请求
from dotenv import load_dotenv

from email_utils import send_email

# --- 新增：来自 3.py 的常量 (部分可能通过 SETTINGS 加载) ---
API_RETRY_COUNT = 2 # 针对 listsale API 的重试次数
MAX_ORDER_RETRIES = 3 # 针对 payorder API 的重试次数
DEFAULT_MAX_CONSECUTIVE_COOKIE_FAILURES = 3
DEFAULT_COOKIE_COOLDOWN_SECONDS = 300
MAX_COOKIE_CALLS_PER_HOUR = 240 # 每个Cookie每小时最大调用次数
COOKIE_HOUR_WINDOW_SECONDS = 3600 # 小时窗口秒数
API_RETRY_BACKOFF_FACTOR = 0.1 # API 重试退避因子
ORDER_RETRY_INITIAL_WAIT_S = 1.5 # 下单重试初始等待时间
# --- 结束新增常量 ---

# --- 全局配置 ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("logs/wish_monitor.log", encoding="utf-8")
    ]
)
logger = logging.getLogger("WishMonitor")
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("httpcore").setLevel(logging.WARNING)

# 加载 .env 文件中的环境变量 (用于邮件配置)
load_dotenv()

# --- 全局变量/状态 ---
# 将在 main_logic 中从配置文件加载
SETTINGS: Dict[str, Any] = {}

# 用于管理查询Cookie的状态
QUERY_COOKIE_STATUSES: List[Dict[str, Any]] = []

# 用于跟踪已处理的 detlog 条目ID
processed_detlog_ids = set()
# 用于跟踪已触发邮件的游戏及sale_id，避免短时间内对同一个满足条件的商品重复下单和发邮件
recently_actioned_sales: Dict[Tuple[str, str], float] = {}

# 新增：用于跟踪每个游戏ID的价格获取窗口状态
# 格式: {game_id: {"status": "active"/"paused", "start_time": timestamp, "duration": seconds}}
active_price_fetching_windows: Dict[str, Dict[str, Any]] = {}

MOBILE_USER_AGENTS = [
    "Mozilla/5.0 (Linux; Android 11; Pixel 5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.91 Mobile Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 10; SM-G980F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; OnePlus 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 11; M2011K2G) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.101 Mobile Safari/537.36",
]

# --- 辅助函数 --- 
def load_settings_and_wishlist() -> bool:
    """加载主配置和愿望单"""
    global SETTINGS
    script_dir = os.path.dirname(__file__)
    settings_path = os.path.abspath(os.path.join(script_dir, "wish_monitor_settings.json"))
    
    if not os.path.exists(settings_path):
        logger.critical(f"主配置文件 wish_monitor_settings.json 未找到于: {settings_path}")
        return False
    try:
        with open(settings_path, "r", encoding="utf-8") as f:
            SETTINGS = json.load(f)
    except Exception as e:
        logger.critical(f"加载主配置文件 wish_monitor_settings.json 失败: {e}", exc_info=True)
        return False
    
    # 验证基本设置是否存在
    required_settings = [
        "web_config_path", "api_monitor_config_path", "wishlist_path",
        "detlog_api_url", "listsale_api_url_template", "payorder_api_url",
        "base_api_url_for_headers", "app_token", 
        "detlog_min_interval_seconds", "detlog_max_interval_seconds", "price_fetch_window_seconds",
        "http_timeout_seconds", "api_retry_count", 
        "max_consecutive_cookie_failures", "cookie_cooldown_seconds"
    ]
    for req_setting in required_settings:
        if req_setting not in SETTINGS:
            logger.critical(f"主配置文件缺少必要设置: {req_setting}")
            return False
    return True

def load_json_config(path: str, is_relative_to_settings: bool = False) -> Optional[Dict]:
    """加载JSON配置文件. 如果 is_relative_to_settings, 则path是相对于主settings文件中的路径."""
    try:
        script_dir = os.path.dirname(__file__)
        if is_relative_to_settings:
            # path itself is the key in SETTINGS dictionary that holds the actual relative path
            actual_path_value = SETTINGS.get(path) 
            if not actual_path_value:
                logger.error(f"在settings中未找到路径键: {path}")
                return None
            # The path from settings is relative to the project root (one level up from script_dir)
            project_root = os.path.abspath(os.path.join(script_dir, ".."))
            absolute_path = os.path.abspath(os.path.join(project_root, actual_path_value))
        else:
            # Path is relative to the script directory (e.g., wishlist.json)
            absolute_path = os.path.abspath(os.path.join(script_dir, path))
            
        if not os.path.exists(absolute_path):
            logger.error(f"配置文件未找到: {absolute_path}")
            return None
        with open(absolute_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载配置文件 {path} (resolved to {absolute_path if 'absolute_path' in locals() else ''}) 失败: {e}", exc_info=True)
        return None

def get_common_headers() -> Dict[str, str]:
    base_url = SETTINGS.get("base_api_url_for_headers", "https://steampy.com")
    parsed_url = urlparse(base_url)
    host = parsed_url.hostname if parsed_url.hostname else "steampy.com"
    
    return {
        'User-Agent': random.choice(MOBILE_USER_AGENTS),
        'Accept': 'application/json, text/plain, */*',
        'Referer': f"{base_url}/cdkSeller?daiFlag=true&qu=%25E5%259B%25BD%25E5%258C%25BA",
        'Origin': base_url,
        'Host': host,
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'APP_TOKEN': SETTINGS.get("app_token", "WAP"),
        'Accept-Encoding': 'gzip, deflate, zstd, br',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
    }

def initialize_query_cookie_statuses(api_monitor_cfg: Dict):
    global QUERY_COOKIE_STATUSES
    raw_pool_main = api_monitor_cfg.get("query_cookies_pool", [])
    raw_pool_away = api_monitor_cfg.get("query_cookies_pool_away", [])
    QUERY_COOKIE_STATUSES = []
    current_time_for_init = time.time() # 用于初始化 window_start_ts

    # 主池
    for i, entry in enumerate(raw_pool_main):
        if not isinstance(entry, dict) or "label" not in entry or "cookie_data" not in entry:
            logger.warning(f"API Monitor Config: query_cookies_pool 条目 {i} 格式不正确，已跳过。")
            continue
        QUERY_COOKIE_STATUSES.append({
            "label": str(entry["label"]),
            "cookie_data": dict(entry["cookie_data"]),
            "consecutive_failures": 0,
            "is_cooling_down": False,
            "cooldown_until_ts": 0.0,
            "is_permanently_removed": False,  # 新增: 是否已永久移除
            "api_calls_in_window": 0,         # 新增: 当前窗口期API调用次数
            "window_start_ts": current_time_for_init, # 新增: 当前窗口期开始时间戳
            "is_hourly_rate_limited": False,   # 新增: 是否因达到小时速率限制而被暂停
            "pool_type": "main"              # 标记主池
        })
    # away池
    for i, entry in enumerate(raw_pool_away):
        if not isinstance(entry, dict) or "label" not in entry or "cookie_data" not in entry:
            logger.warning(f"API Monitor Config: query_cookies_pool_away 条目 {i} 格式不正确，已跳过。")
            continue
        QUERY_COOKIE_STATUSES.append({
            "label": str(entry["label"]),
            "cookie_data": dict(entry["cookie_data"]),
            "consecutive_failures": 0,
            "is_cooling_down": False,
            "cooldown_until_ts": 0.0,
            "is_permanently_removed": False,  # 新增: 是否已永久移除
            "api_calls_in_window": 0,         # 新增: 当前窗口期API调用次数
            "window_start_ts": current_time_for_init, # 新增: 当前窗口期开始时间戳
            "is_hourly_rate_limited": False,   # 新增: 是否因达到小时速率限制而被暂停
            "pool_type": "away"              # 标记away池
        })
    if not QUERY_COOKIE_STATUSES:
        logger.error("查询Cookie池（主池+away池）为空或所有条目无效。无法查询价格。")
        return False
    logger.info(f"已初始化 {len(QUERY_COOKIE_STATUSES)} 个查询Cookie状态（主池+away池）。")
    return True

async def get_active_query_cookie() -> Optional[Dict[str, Any]]:
    current_time = time.time()
    available_cookies = []

    # 检查是否有Cookie的小时速率限制窗口已过，需要重置
    # 注意：更完整的全局小时重置逻辑应在主循环中处理，这里仅为get_active_query_cookie内部的检查
    for status_check in QUERY_COOKIE_STATUSES:
        if status_check.get("is_hourly_rate_limited", False):
            window_start = status_check.get("window_start_ts", 0)
            if current_time - window_start >= SETTINGS.get("cookie_hour_window_seconds", COOKIE_HOUR_WINDOW_SECONDS):
                logger.info(f"Cookie '{status_check["label"]}' 的小时速率限制窗口已过，在选择前重置其状态。")
                status_check["is_hourly_rate_limited"] = False
                status_check["api_calls_in_window"] = 0
                status_check["window_start_ts"] = current_time

    for status in QUERY_COOKIE_STATUSES:
        if status.get("is_permanently_removed", False):
            continue # 跳过永久移除的Cookie
        
        if status.get("is_hourly_rate_limited", False):
            # logger.debug(f"Cookie '{status["label"]}' 当前因小时速率限制而跳过。") # 可能过于频繁
            continue # 跳过因小时速率限制而暂停的Cookie

        if status["is_cooling_down"] and current_time < status["cooldown_until_ts"]:
            continue # 仍处于冷却中
        elif status["is_cooling_down"]: # 冷却时间已过
            status["is_cooling_down"] = False
            # 注意：3.py中是在成功获取价格后才重置 consecutive_failures。这里保持原逻辑，在冷却结束后重置。
            # 如果需要完全对齐3.py，则不应在此处重置 consecutive_failures。
            # 为了更接近3.py，我将注释掉这里的失败次数重置。
            # status["consecutive_failures"] = 0 
            logger.info(f"Cookie '{status["label"]}' 已结束冷却。连续失败次数保持为: {status['consecutive_failures']}。")
        
        available_cookies.append(status)
    
    if not available_cookies:
        logger.warning("当前无可用查询Cookie (可能都在冷却中、被小时限速或已永久移除)。")
        min_cooldown_end = float('inf')
        any_cookie_in_cooldown = False
        for s_check in QUERY_COOKIE_STATUSES:
            if not s_check.get("is_permanently_removed") and not s_check.get("is_hourly_rate_limited") and s_check["is_cooling_down"]:
                min_cooldown_end = min(min_cooldown_end, s_check["cooldown_until_ts"])
                any_cookie_in_cooldown = True
        
        if any_cookie_in_cooldown and min_cooldown_end > current_time:
            wait_time = min_cooldown_end - current_time + 1 # wait 1s past cooldown
            logger.info(f"所有可用Cookie都在冷却中或受限，将等待 {wait_time:.2f}s 直到最近的Cookie冷却结束。")
            await asyncio.sleep(wait_time)
            return await get_active_query_cookie() # 递归调用以重试获取
        return None # 如果没有可等待的冷却中Cookie，则返回None
        
    return random.choice(available_cookies)

# update_cookie_status 函数的逻辑将被整合到 call_listsale_api 中，可以考虑移除或重构
# def update_cookie_status(cookie_status: Dict, success: bool):
#     if success:
#         if cookie_status["consecutive_failures"] > 0:
#              logger.info(f"Cookie '{cookie_status['label']}' 请求成功，重置连续失败次数。")
#         cookie_status["consecutive_failures"] = 0
#         cookie_status["is_cooling_down"] = False
#     else:
#         cookie_status["consecutive_failures"] += 1
#         logger.warning(f"Cookie '{cookie_status['label']}' 请求失败，连续失败次数: {cookie_status['consecutive_failures']}")
#         max_failures = SETTINGS.get("max_consecutive_cookie_failures", DEFAULT_MAX_CONSECUTIVE_COOKIE_FAILURES)
#         if cookie_status["consecutive_failures"] >= max_failures:
#             cooldown_duration = SETTINGS.get("cookie_cooldown_seconds", DEFAULT_COOKIE_COOLDOWN_SECONDS)
#             # 在3.py的逻辑中，达到最大失败次数会永久移除，而不是简单冷却。我们将采用永久移除的逻辑。
#             logger.error(f"Cookie '{cookie_status['label']}' 已达到最大失败次数 ({max_failures})，将被永久移除。")
#             cookie_status["is_permanently_removed"] = True 
#             # cookie_status["is_cooling_down"] = True # 不再只是冷却
#             # cookie_status["cooldown_until_ts"] = time.time() + cooldown_duration

async def call_listsale_api(http_client: httpx.AsyncClient, game_id: str) -> Optional[Tuple[float, str]]:
    api_url_template = SETTINGS.get("listsale_api_url_template")
    if not api_url_template:
        logger.error(f"listSale for {game_id}:未在settings中配置api_url_template")
        return None
    api_url = api_url_template.format(game_id=game_id)
    
    timeout_val = SETTINGS.get("http_timeout_seconds", 10)
    # API_RETRY_COUNT 来自文件顶部的常量
    # API_RETRY_BACKOFF_FACTOR 来自文件顶部的常量
    max_consecutive_failures_limit = SETTINGS.get("max_consecutive_cookie_failures", DEFAULT_MAX_CONSECUTIVE_COOKIE_FAILURES)
    cookie_cooldown_duration_s = SETTINGS.get("cookie_cooldown_seconds", DEFAULT_COOKIE_COOLDOWN_SECONDS)
    max_calls_ph = SETTINGS.get("max_cookie_calls_per_hour", MAX_COOKIE_CALLS_PER_HOUR)
    cookie_hr_window_s = SETTINGS.get("cookie_hour_window_seconds", COOKIE_HOUR_WINDOW_SECONDS)

    selected_cookie_status = await get_active_query_cookie()
    if not selected_cookie_status:
        logger.error(f"listSale for {game_id}: 无可用查询Cookie。")
        return None

    cookie_label = selected_cookie_status["label"]
    current_cookie_data = selected_cookie_status["cookie_data"]
    
    # --- 小时速率限制检查和更新 (来自3.py _get_price_from_api) ---
    current_time_for_cookie_window = time.time()
    if selected_cookie_status.get("window_start_ts") is None: # 理论上已在初始化时设置
        selected_cookie_status["window_start_ts"] = current_time_for_cookie_window
        selected_cookie_status["api_calls_in_window"] = 0
        selected_cookie_status["is_hourly_rate_limited"] = False
        logger.warning(f"Cookie '{cookie_label}' 的 window_start_ts 未初始化，已在首次使用前设置。")

    if current_time_for_cookie_window - selected_cookie_status["window_start_ts"] >= cookie_hr_window_s:
        logger.info(f"Cookie '{cookie_label}' 的小时窗口期已过 ({cookie_hr_window_s}s)，在本次使用前重置其计数。上一窗口调用: {selected_cookie_status['api_calls_in_window']} 次。")
        selected_cookie_status["api_calls_in_window"] = 0
        selected_cookie_status["window_start_ts"] = current_time_for_cookie_window
        selected_cookie_status["is_hourly_rate_limited"] = False
    
    selected_cookie_status["api_calls_in_window"] += 1

    if selected_cookie_status["api_calls_in_window"] >= max_calls_ph and not selected_cookie_status["is_hourly_rate_limited"]:
        selected_cookie_status["is_hourly_rate_limited"] = True
        start_time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(selected_cookie_status['window_start_ts']))
        logger.warning(f"Cookie '{cookie_label}' 已达到窗口期调用上限 ({max_calls_ph})，将在本窗口期剩余时间内暂停。当前窗口调用: {selected_cookie_status['api_calls_in_window']}。窗口开始于: {start_time_str}。")
        # 注意: 此处不立即返回 None，因为 get_active_query_cookie 下次调用时会跳过它。
        # 但如果这是唯一可用的cookie，那么上游的 get_active_query_cookie 会返回 None。
    # --- 结束小时速率限制检查 ---

    headers = get_common_headers()
    headers['accessToken'] = current_cookie_data.get('bbsToken', '')
    cookie_str_parts = [f"{k}={v}" for k, v in current_cookie_data.items()]
    if cookie_str_parts:
        headers['Cookie'] = "; ".join(cookie_str_parts)

    for attempt in range(API_RETRY_COUNT + 1):
        try:
            response = await http_client.get(api_url, headers=headers, timeout=timeout_val)
            response.raise_for_status() # 检查HTTP错误状态 (4xx, 5xx)
            data = response.json()

            if data.get("code") == 200 and "result" in data and "content" in data["result"]:
                content = data["result"]["content"]
                if content:
                    price = content[0].get("keyPrice")
                    sale_id = content[0].get("saleId")
                    if price is not None and sale_id is not None:
                        if selected_cookie_status["consecutive_failures"] > 0:
                             logger.info(f"Cookie '{cookie_label}' 成功获取 {game_id} 价格，重置其连续失败次数 (之前为 {selected_cookie_status['consecutive_failures']})。")
                        selected_cookie_status["consecutive_failures"] = 0
                        selected_cookie_status["is_cooling_down"] = False # 确保冷却状态解除
                        selected_cookie_status["cooldown_until_ts"] = 0.0
                        return float(price), str(sale_id)
                    else:
                        logger.warning(f"listSale for {game_id} (Cookie: {cookie_label}, Attempt {attempt+1}) API成功但缺少price/saleId: P={price}, S={sale_id}")
                        # 这种情况也可能视为一种"失败"，因为它没有得到需要的数据
                else: # content为空数组
                    logger.info(f"listSale for {game_id} (Cookie: {cookie_label}, Attempt {attempt+1}) API成功但未返回商品记录 (content为空)。")
                    # 这种情况下，cookie本身是好的，API调用也成功，只是没数据。
                    # 重置连续失败计数
                    if selected_cookie_status["consecutive_failures"] > 0:
                        logger.info(f"Cookie '{cookie_label}' (listSale content为空) 重置其连续失败次数 (之前为 {selected_cookie_status['consecutive_failures']})。")
                    selected_cookie_status["consecutive_failures"] = 0
                    selected_cookie_status["is_cooling_down"] = False
                    selected_cookie_status["cooldown_until_ts"] = 0.0
                    return None # 返回None表示没有价格信息
                # 如果走到这里，意味着API响应code=200，但数据不符合预期（如price/sale_id为None）
                # 这不一定是cookie的问题，更可能是数据问题，但为了安全，我们可能也需要处理下cookie失败计数

            # 如果API code 不是 200，或者结构不对，则认为是失败 (会进入下面的通用失败处理逻辑)
            logger.warning(f"listSale for {game_id} (Cookie: {cookie_label}, Attempt {attempt+1}) API逻辑失败: Code {data.get('code')}, Success: {data.get('success')}, Msg: {data.get('message')}")
            # 触发下面的通用失败处理
            raise httpx.RequestError(f"API logic failure: Code {data.get('code')}") # 抛出异常以便统一处理失败

        except (httpx.HTTPStatusError, httpx.RequestError, json.JSONDecodeError) as e:
            log_msg_prefix = f"listSale for {game_id} (Cookie: {cookie_label}, Attempt {attempt+1})"
            if isinstance(e, httpx.HTTPStatusError):
                logger.warning(f"{log_msg_prefix} HTTP error: {e.response.status_code} - {e.response.text[:200]}")
            elif isinstance(e, json.JSONDecodeError):
                response_text = e.doc[:200] if hasattr(e, 'doc') else "Unknown response text"
                logger.warning(f"{log_msg_prefix} JSON decode error: {e}. Response text: {response_text}")
            else: # httpx.RequestError (includes timeouts, connection errors, etc.) or the raised one above
                logger.warning(f"{log_msg_prefix} Request error: {type(e).__name__} - {e}")

            if attempt == API_RETRY_COUNT: # 最后一次尝试失败
                selected_cookie_status["consecutive_failures"] += 1
                logger.warning(f"Cookie '{cookie_label}' 获取 {game_id} 价格失败。连续失败次数: {selected_cookie_status['consecutive_failures']}/{max_consecutive_failures_limit}。")
                if selected_cookie_status["consecutive_failures"] >= max_consecutive_failures_limit:
                    selected_cookie_status["is_permanently_removed"] = True
                    logger.error(f"Cookie '{cookie_label}' 已达到最大连续失败次数 ({max_consecutive_failures_limit})，将被永久移除。")
                else:
                    selected_cookie_status["is_cooling_down"] = True
                    selected_cookie_status["cooldown_until_ts"] = time.time() + cookie_cooldown_duration_s
                    logger.info(f"Cookie '{cookie_label}' 进入冷却，时长: {cookie_cooldown_duration_s:.1f} 秒。")
                return None # 此次API调用最终失败
            else: # 还可以重试
                await asyncio.sleep(API_RETRY_BACKOFF_FACTOR * (attempt + 1))
        
        except Exception as e_generic: # 捕获其他意外错误
            logger.error(f"listSale for {game_id} (Cookie: {cookie_label}, Attempt {attempt+1}) 未知错误: {e_generic}", exc_info=True)
            if attempt == API_RETRY_COUNT: # 最后一次尝试因未知错误失败
                selected_cookie_status["consecutive_failures"] += 1 # 也算作一次失败
                logger.warning(f"Cookie '{cookie_label}' (未知错误) 获取 {game_id} 价格失败。连续失败次数: {selected_cookie_status['consecutive_failures']}/{max_consecutive_failures_limit}。")
                if selected_cookie_status["consecutive_failures"] >= max_consecutive_failures_limit:
                    selected_cookie_status["is_permanently_removed"] = True
                    logger.error(f"Cookie '{cookie_label}' 因未知错误达到最大连续失败次数，将被永久移除。")
                else:
                    selected_cookie_status["is_cooling_down"] = True # 也进行冷却
                    selected_cookie_status["cooldown_until_ts"] = time.time() + cookie_cooldown_duration_s
                    logger.info(f"Cookie '{cookie_label}' 因未知错误进入冷却，时长: {cookie_cooldown_duration_s:.1f} 秒。")                    
                return None # 最终失败
            else: # 还可以重试，但对于未知错误，退避时间可以稍长
                await asyncio.sleep(API_RETRY_BACKOFF_FACTOR * (attempt + 1) * 1.5)

    return None # 所有重试均失败

async def call_payorder_api(http_client: httpx.AsyncClient, 
                            sale_id: str, 
                            order_cookies_dict: Dict, 
                            full_order_cookie_str: str 
                           ) -> Optional[str]:
    api_url = SETTINGS.get("payorder_api_url")
    if not api_url:
        logger.error(f"payOrder for {sale_id}: 未在settings中配置payorder_api_url")
        return None
        
    base_api_url_for_hdrs = SETTINGS.get("base_api_url_for_headers", "https://steampy.com")
    timeout_val = SETTINGS.get("http_timeout_seconds", 10) + 5 # 下单超时可以稍长
    # MAX_ORDER_RETRIES 和 ORDER_RETRY_INITIAL_WAIT_S 来自文件顶部常量
    form_data = {"saleId": sale_id, "payType": SETTINGS.get("pay_type", "AU")}

    headers = get_common_headers()
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
    headers['Referer'] = f"{base_api_url_for_hdrs}/createOrder?daiFlag=true&qu=%25E5%259B%25BD%25E5%258C%25BA"

    order_cookie_label_for_log = "N/A"
    if isinstance(order_cookies_dict, dict) and order_cookies_dict.get("label"):
        order_cookie_label_for_log = order_cookies_dict["label"]
    elif full_order_cookie_str:
        order_cookie_label_for_log = "full_order_cookie_str"

    if isinstance(order_cookies_dict, dict):
        headers['accessToken'] = order_cookies_dict.get('bbsToken', '')
    else:
        headers['accessToken'] = '' 

    httpx_cookies_param = None
    if full_order_cookie_str:
        headers['Cookie'] = full_order_cookie_str
    elif isinstance(order_cookies_dict, dict) and order_cookies_dict:
        httpx_cookies_param = order_cookies_dict
    else:
        logger.error(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}): 无有效下单Cookie配置。")
        return None

    for attempt in range(MAX_ORDER_RETRIES + 1):
        try:
            response = await http_client.post(api_url, headers=headers, data=form_data, cookies=httpx_cookies_param, timeout=timeout_val)
            response.raise_for_status()
            data = response.json()

            if data.get("code") == 200 and data.get("success") and "result" in data: # 3.py 使用 success 字段
                res_result = data.get("result", {})
                payment_url = res_result.get("form") # 3.py 使用 form 作为支付链接字段
                order_id = res_result.get("orderId") # 获取 orderId 用于日志
                if payment_url:
                    logger.warning(f"订单创建成功! SaleID: {sale_id}, OrderID: {order_id} (Cookie: {order_cookie_label_for_log}, Attempt {attempt+1}). 支付链接已获取。")
                    return str(payment_url)
                else:
                    logger.warning(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}, Attempt {attempt+1}) 成功但未返回支付链接 (form为空)。OrderID: {order_id}. Response: {res_result}")
                    return None # 无支付链接视为失败
            else:
                error_msg = data.get('message', '未知API内部错误')
                logger.error(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}, Attempt {attempt+1}) API逻辑失败: Code {data.get('code')}, Success: {data.get('success')}, Msg: {error_msg}. Response: {data}")
                if "被抢先了" in error_msg or "已被其他买家抢先" in error_msg or "SOLD_OUT" in error_msg.upper(): # 更广泛地匹配售罄
                    logger.warning(f"SaleID {sale_id} (Cookie: {order_cookie_label_for_log}) 被抢或已售罄。")
                    return "SOLD_OUT"
                # 其他错误不立即返回 SOLD_OUT，将尝试重试
        
        except httpx.HTTPStatusError as e:
            logger.error(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}, Attempt {attempt+1}) HTTP error: {e.response.status_code} - {e.response.text[:200]}")
        except httpx.RequestError as e:
            logger.error(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}, Attempt {attempt+1}) Request error: {type(e).__name__} - {e}")
        except json.JSONDecodeError as e_json:
            response_text = e_json.doc[:200] if hasattr(e_json, 'doc') else "Unknown response text"
            logger.error(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}, Attempt {attempt+1}) JSON decode error: {e_json}. Response text: {response_text}")
        except Exception as e_generic:
            logger.error(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}, Attempt {attempt+1}) 未知错误: {e_generic}", exc_info=True)
        
        # 如果因为特定错误（如SOLD_OUT）已返回，则不会执行到这里
        if attempt < MAX_ORDER_RETRIES:
            retry_wait = ORDER_RETRY_INITIAL_WAIT_S * (attempt + 1) # 线性增加等待时间，也可考虑指数
            logger.info(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}) 尝试 {attempt+1} 失败，将在 {retry_wait:.2f}s 后重试。")
            await asyncio.sleep(retry_wait)
        else: # 所有重试均已用尽
            logger.error(f"payOrder for {sale_id} (Cookie: {order_cookie_label_for_log}) 所有 {MAX_ORDER_RETRIES+1} 次下单尝试均失败。")
            return None # 所有重试失败后返回None

    return None # Fallback, 理论上应在循环内返回

async def call_detlog_api(http_client: httpx.AsyncClient, token: str) -> Optional[List[Dict]]:
    headers = get_common_headers()
    headers['accessToken'] = token # detlog 使用 accessToken
    api_url = SETTINGS.get("detlog_api_url")
    if not api_url:
        logger.error("detLog API URL未在settings中配置。")
        return None
    timeout = SETTINGS.get("http_timeout_seconds", 10)

    try:
        # detlog 通常需要分页参数和排序参数，基于之前的实现添加
        params = {"pageSize": 50, "sort": "createTime", "order": "desc"} 
        response = await http_client.get(api_url, headers=headers, params=params, timeout=timeout)
        response.raise_for_status()
        data = response.json()
        # 检查响应结构是否符合预期，例如 code == 200 且 result.content 存在
        if data.get("code") == 200 and isinstance(data.get("result"), dict) and isinstance(data["result"].get("content"), list):
            return data["result"]["content"]
        else:
            logger.warning(f"detLog API 响应格式不正确或code不为200: Code {data.get('code')}, Msg: {data.get('message')}, Response: {data}")
            return None
    except httpx.HTTPStatusError as e:
        logger.error(f"detLog API HTTP 错误: {e.response.status_code} - {e.response.text[:200]}", exc_info=False) #通常不需要完整堆栈
        if e.response.status_code == 401:
            logger.error("detLog API Token可能已失效或无权限。请检查 WEB/config.json 中的 access_token。")
    except httpx.RequestError as e:
        logger.error(f"detLog API 请求错误: {type(e).__name__} - {e}", exc_info=False)
    except json.JSONDecodeError as e_json:
        response_text = e_json.doc[:200] if hasattr(e_json, 'doc') else "Unknown response text"
        logger.error(f"detLog API JSON解码错误: {e_json}. 响应文本: {response_text}")
    except Exception as e_generic:
        logger.error(f"detLog API 调用时发生未知错误: {e_generic}", exc_info=True)
    return None

async def poll_price_for_game(
    game_id: str,
    game_name_for_log: str,
    wish_item: Dict[str, Any],
    http_client: httpx.AsyncClient,
    active_price_fetching_windows: Dict[str, Dict[str, Any]],
    recently_actioned_sales: Dict[Tuple[str, str], float],
    order_cookies_dict: Dict,
    full_order_cookie_str: str,
    settings_dict: Dict[str, Any], # Renamed SETTINGS to settings_dict to avoid global conflicts
    logger_instance: logging.Logger
):
    """在活动窗口内为特定游戏轮询价格。"""
    logger_instance.info(f"价格轮询任务已启动 for '{game_name_for_log}' (ID: {game_id})。")
    threshold = wish_item["threshold"]

    while True:
        current_time_poll = time.time() # Use a local current_time for this loop
        window_info = active_price_fetching_windows.get(game_id)

        if not window_info or window_info.get("status") != "active":
            logger_instance.info(f"价格轮询窗口 for '{game_name_for_log}' (ID: {game_id}) 不再活动或已移除。停止轮询。")
            if window_info and window_info.get("polling_task") and not window_info["polling_task"].done():
                 # Attempt to cancel if not already done, though it might exit on its own
                try:
                    window_info["polling_task"].cancel()
                except Exception as e_cancel:
                    logger_instance.debug(f"Error cancelling polling task for {game_id}: {e_cancel}")
            break

        if current_time_poll - window_info["start_time"] >= window_info["duration"]:
            logger_instance.info(f"价格轮询窗口 for '{game_name_for_log}' (ID: {game_id}) 已到期。停止轮询。")
            window_info["status"] = "paused" # 标记为暂停
            if window_info.get("polling_task") and not window_info["polling_task"].done():
                try:
                    window_info["polling_task"].cancel()
                except Exception as e_cancel:
                    logger_instance.debug(f"Error cancelling polling task for {game_id} (expired): {e_cancel}")
            break

        logger_instance.debug(f"轮询价格 for '{game_name_for_log}' (ID: {game_id})...")
        # Pass the global SETTINGS to call_listsale_api as it expects it
        price_data = await call_listsale_api(http_client, game_id)


        if price_data:
            current_price, sale_id = price_data
            last_price = window_info.get("last_known_price")
            hist_low = window_info.get("historical_low_price")
            # price_log_message = f"'{game_name_for_log}' (ID: {game_id}, SaleID: {sale_id})" # Original definition

            if last_price is None: # 首次获取
                logger_instance.info(f"{game_name_for_log} [轮询] 初始价格: {current_price} (SaleID: {sale_id})")
                window_info["last_known_price"] = current_price
                window_info["historical_low_price"] = current_price
            elif current_price != last_price:
                is_new_hist_low = (hist_low is None or current_price < hist_low)
                if is_new_hist_low:
                    logger_instance.warning(f"{game_name_for_log} [轮询] 新史低: {current_price} (旧价: {last_price}, 原史低: {hist_low}, SaleID: {sale_id})")
                    window_info["historical_low_price"] = current_price
                else:
                    logger_instance.info(f"{game_name_for_log} [轮询] 现价: {current_price} (旧价: {last_price}, 史低: {hist_low}, SaleID: {sale_id})")
                window_info["last_known_price"] = current_price
            # else: # 价格未变时，不再输出日志，以符合"只在变化时输出"
            #    logger_instance.debug(f"{game_name_for_log} [轮询] 价格未变: {current_price} (SaleID: {sale_id})")

            # 只有在价格是首次获取或者价格发生变化时，才评估并记录是否达标
            if last_price is None or current_price != last_price:
                if current_price <= threshold:
                    logger_instance.warning(f"价格达标! '{game_name_for_log}' (ID: {game_id}, SaleID: {sale_id}) 价格: {current_price} <= 阈值: {threshold}")
                    action_key = (game_id, sale_id)
                    action_cooldown = settings_dict.get("action_cooldown_seconds", 3600)
    
                    # Check against recently_actioned_sales using current_time_poll
                    if action_key in recently_actioned_sales and \
                       current_time_poll - recently_actioned_sales[action_key] < action_cooldown:
                        logger_instance.info(f"SaleID {sale_id} for game {game_id} 最近已处理（轮询时发现），跳过。")
                    else:
                        # Pass the global SETTINGS to call_payorder_api
                        payment_result = await call_payorder_api(http_client, sale_id, order_cookies_dict, full_order_cookie_str)
                        recently_actioned_sales[action_key] = current_time_poll # Use current_time_poll
    
                        if payment_result and payment_result != "SOLD_OUT":
                            logger_instance.warning(f"下单成功! SaleID: {sale_id}. 支付链接: {payment_result}")
                            mail_subject = f"【监控】《{game_name_for_log}》价格达标！"
                            mail_body = f"《{game_name_for_log}》达到价格阈值！\n游戏ID: {game_id}\nSaleID: {sale_id}\n价格: {current_price} (阈值: {threshold})\n"
                            await send_email(mail_subject, mail_body, payment_url=payment_result, timeout=settings_dict.get("http_timeout_seconds", 10))
                        elif payment_result == "SOLD_OUT":
                            logger_instance.info(f"SaleID {sale_id} for '{game_name_for_log}' 已售罄或被抢（轮询时发现）。")
                        else:
                            logger_instance.error(f"下单失败或未获取到支付链接 for SaleID {sale_id}（轮询时发现）。")
                else:
                    logger_instance.info(f"'{game_name_for_log}' (ID:{game_id}, SaleID: {sale_id}) 价格 {current_price} > {threshold}. 未达标（轮询时发现）。")
            elif current_price <= threshold: # 价格未变，但之前可能未处理或状态已变，检查是否达标
                # 此分支处理价格未变但仍需检查是否满足下单条件的情况（例如，之前可能因为冷却没下单）
                # 为了避免重复日志，我们主要依赖上面 (last_price is None or current_price != last_price) 分支中的日志
                # 但如果价格未变且达标，且可以行动，还是需要触发动作
                action_key = (game_id, sale_id)
                action_cooldown = settings_dict.get("action_cooldown_seconds", 3600)
                if not (action_key in recently_actioned_sales and current_time_poll - recently_actioned_sales[action_key] < action_cooldown):
                    logger_instance.warning(f"价格达标(价格未变)! '{game_name_for_log}' (ID: {game_id}, SaleID: {sale_id}) 价格: {current_price} <= 阈值: {threshold}")
                    # ... (重复下单逻辑，可以考虑将其提取为一个辅助函数以避免代码重复)
                    payment_result = await call_payorder_api(http_client, sale_id, order_cookies_dict, full_order_cookie_str)
                    recently_actioned_sales[action_key] = current_time_poll
                    if payment_result and payment_result != "SOLD_OUT":
                        logger_instance.warning(f"下单成功(价格未变)! SaleID: {sale_id}. 支付链接: {payment_result}")
                        mail_subject = f"【监控】《{game_name_for_log}》价格达标(价格未变)！"
                        mail_body = f"《{game_name_for_log}》达到价格阈值(价格未变)！\n游戏ID: {game_id}\nSaleID: {sale_id}\n价格: {current_price} (阈值: {threshold})\n支付: {payment_result}"
                        await send_email(mail_subject, mail_body, payment_url=payment_result, timeout=settings_dict.get("http_timeout_seconds", 10))
                    elif payment_result == "SOLD_OUT":
                        logger_instance.info(f"SaleID {sale_id} for '{game_name_for_log}' 已售罄或被抢(价格未变时发现)。")
                    else:
                        logger_instance.error(f"下单失败(价格未变) for SaleID {sale_id}。")
        else:
            logger_instance.warning(f"轮询:未能获取 '{game_name_for_log}' (ID: {game_id}) 价格。")

        # 轮询间隔
        min_interval = settings_dict.get("listsale_min_interval_seconds", 2)
        max_interval = settings_dict.get("listsale_max_interval_seconds", 15)
        poll_delay = random.uniform(min_interval, max_interval)
        
        # Check window status again before sleeping
        window_info_check_before_sleep = active_price_fetching_windows.get(game_id)
        if not window_info_check_before_sleep or window_info_check_before_sleep.get("status") != "active" or \
            (current_time_poll + poll_delay) - window_info_check_before_sleep["start_time"] >= window_info_check_before_sleep["duration"]:
            logger_instance.info(f"价格轮询 for '{game_name_for_log}' (ID: {game_id}) 即将结束或到期，不再等待下一个轮询间隔。")
            if window_info_check_before_sleep and window_info_check_before_sleep.get("polling_task") and not window_info_check_before_sleep["polling_task"].done():
                try:
                    window_info_check_before_sleep["polling_task"].cancel()
                except Exception as e_cancel:
                    logger_instance.debug(f"Error cancelling polling task for {game_id} (pre-sleep check): {e_cancel}")
            break # Exit loop if window is no longer active or next poll would exceed duration
        
        logger_instance.debug(f"下一次 '{game_name_for_log}' (ID: {game_id}) 价格轮询将在 {poll_delay:.2f} 秒后。")
        try:
            await asyncio.sleep(poll_delay)
        except asyncio.CancelledError:
            logger_instance.info(f"价格轮询任务 for '{game_name_for_log}' (ID: {game_id}) 被取消。")
            break # Exit if task is cancelled during sleep

    logger_instance.info(f"价格轮询任务已结束 for '{game_name_for_log}' (ID: {game_id})。")
    # Ensure task is marked as None or removed from window_info if it's self-terminating
    if game_id in active_price_fetching_windows and active_price_fetching_windows[game_id].get("polling_task"):
        active_price_fetching_windows[game_id]["polling_task"] = None

async def main_logic():
    if not load_settings_and_wishlist():
        return

    web_cfg = load_json_config("web_config_path", is_relative_to_settings=True)
    api_monitor_cfg = load_json_config("api_monitor_config_path", is_relative_to_settings=True)
    wishlist_data = load_json_config(SETTINGS["wishlist_path"]) # wishlist_path is relative to script dir

    if not web_cfg or not web_cfg.get("access_token"):
        logger.error(f"未能从 {SETTINGS['web_config_path']} 加载 access_token。请检查。程序将退出。")
        return
    detlog_access_token = web_cfg["access_token"]

    if not api_monitor_cfg:
        logger.error(f"未能从 {SETTINGS['api_monitor_config_path']} 加载配置。程序将退出。")
        return
    if not initialize_query_cookie_statuses(api_monitor_cfg):
         logger.error("初始化查询Cookie失败。程序将退出。")
         return
         
    order_cookies_dict_from_cfg = api_monitor_cfg.get("order_cookies", {})
    full_order_cookie_str_from_cfg = api_monitor_cfg.get("full_order_cookie", "") # 新增：获取 full_order_cookie

    # 修改：检查 order_cookies_dict_from_cfg 和 full_order_cookie_str_from_cfg 是否都无效
    if not isinstance(order_cookies_dict_from_cfg, dict) and not full_order_cookie_str_from_cfg:
        logger.error(f"下单Cookie配置无效 (order_cookies 不是字典且 full_order_cookie 为空)。来自 {SETTINGS['api_monitor_config_path']}。程序将退出。")
        return
    # 如果 order_cookies_dict_from_cfg 是字典但为空，并且 full_order_cookie_str_from_cfg 也为空，也应视为错误
    if isinstance(order_cookies_dict_from_cfg, dict) and not order_cookies_dict_from_cfg and not full_order_cookie_str_from_cfg:
        logger.error(f"下单Cookie配置均为空 (order_cookies 字典为空且 full_order_cookie 字符串为空)。来自 {SETTINGS['api_monitor_config_path']}。程序将退出。")
        return

    if not wishlist_data:
        logger.error(f"未能从 {SETTINGS['wishlist_path']} 加载WISH清单。程序将退出。")
        return
    
    wishlist = {
        str(game.get("name", "")).lower(): {"id": str(game.get("id")), "threshold": float(game.get("threshold", float('inf')))}
        for game in wishlist_data if game.get("name") and game.get("id")
    }
    if not wishlist: logger.warning("处理后的WISH清单为空。请检查 wishlist.json。")

    logger.info(f"Wish Monitor启动。Detlog token: ...{detlog_access_token[-5:]}. Wishlist items: {len(wishlist)}.")
    
    # 新增：用于全局Cookie小时速率限制重置的时间戳
    last_global_hourly_cookie_reset_ts = time.time()

    # Reverting to default SSL verification
    async with httpx.AsyncClient(timeout=SETTINGS.get("http_timeout_seconds", 10), follow_redirects=True) as http_client:
        last_detlog_fetch_time = 0
        next_detlog_fetch_delay = random.uniform(
            SETTINGS.get("detlog_min_interval_seconds", 5),
            SETTINGS.get("detlog_max_interval_seconds", 30)
        )
        
        while True:
            current_time = time.time()
            action_cooldown = SETTINGS.get("action_cooldown_seconds", 3600)
            price_fetch_window_duration = SETTINGS.get("price_fetch_window_seconds", 300)
            current_cookie_hour_window_s = SETTINGS.get("cookie_hour_window_seconds", COOKIE_HOUR_WINDOW_SECONDS)

            # --- 新增：全局Cookie小时调用统计重置逻辑 (来自3.py) ---
            if current_time - last_global_hourly_cookie_reset_ts >= current_cookie_hour_window_s:
                logger.info(f"全局Cookie调用统计重置已触发 (窗口期: {current_cookie_hour_window_s}秒)。")
                for cookie_status in QUERY_COOKIE_STATUSES:
                    if cookie_status.get("is_permanently_removed"):
                        continue
                    label = cookie_status.get('label', '未知Label')
                    calls = cookie_status.get('api_calls_in_window', 0)
                    window_start_ts_prev = cookie_status.get('window_start_ts')
                    window_start_str_prev = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(window_start_ts_prev)) if window_start_ts_prev else "未知"
                    
                    logger.info(f"Cookie '{label}' 上一窗口期 (开始于 {window_start_str_prev}) 调用次数: {calls}.")
                    cookie_status["api_calls_in_window"] = 0
                    cookie_status["window_start_ts"] = current_time # 使用当前循环的 current_time 作为新窗口的开始
                    cookie_status["is_hourly_rate_limited"] = False
                last_global_hourly_cookie_reset_ts = current_time
                logger.info("所有查询Cookie的每小时调用统计记录和重置完成。")
            # --- 结束全局Cookie小时调用统计重置逻辑 ---

            # Cleanup expired actions
            expired_actions = [k for k, ts in recently_actioned_sales.items() if current_time - ts > action_cooldown]
            for k_expired in expired_actions: del recently_actioned_sales[k_expired]

            # Check and update status of active price fetching windows
            for game_id, window_info in list(active_price_fetching_windows.items()): # list() for safe iteration while modifying
                if window_info["status"] == "active" and \
                   current_time - window_info["start_time"] >= window_info["duration"]:
                    logger.info(f"价格获取窗口已结束 (ID: {game_id})。暂停。")
                    active_price_fetching_windows[game_id]["status"] = "paused"
                    # Attempt to cancel the polling task if it's still running
                    polling_task = window_info.get("polling_task")
                    if polling_task and not polling_task.done():
                        logger.info(f"尝试取消游戏 {game_id} 的价格轮询任务，因为窗口已过期。")
                        try:
                            polling_task.cancel()
                        except Exception as e_cancel:
                            logger.error(f"取消轮询任务 for {game_id} 时出错: {e_cancel}")
                    active_price_fetching_windows[game_id]["polling_task"] = None # Clear the task reference
                    # Optionally, remove from recently_actioned_sales to allow immediate re-trigger by new detlog if needed
                    # For now, let recently_actioned_sales handle its own cooldown for actual ordering actions.

            if current_time - last_detlog_fetch_time >= next_detlog_fetch_delay:
                logger.debug("获取 detLog 数据...")
                detlog_entries = await call_detlog_api(http_client, detlog_access_token)
                last_detlog_fetch_time = current_time
                next_detlog_fetch_delay = random.uniform(
                    SETTINGS.get("detlog_min_interval_seconds", 5),
                    SETTINGS.get("detlog_max_interval_seconds", 30)
                )
                logger.debug(f"下一次 detLog 获取将在约 {next_detlog_fetch_delay:.2f} 秒后。")

                if detlog_entries:
                    logger.debug(f"获取到 {len(detlog_entries)} 条 detLog 记录。")
                    for entry in detlog_entries:
                        log_id, game_name_log = entry.get("id"), entry.get("gameName")
                        if not (log_id and game_name_log) or log_id in processed_detlog_ids:
                            continue
                        processed_detlog_ids.add(log_id)
                        
                        game_name_lower = game_name_log.lower()
                        if game_name_lower in wishlist:
                            wish_item = wishlist[game_name_lower]
                            target_game_id, threshold = wish_item["id"], wish_item["threshold"]
                            logger.info(f"DetLog WISH命中: '{game_name_log}' (ID: {target_game_id}), 阈值: {threshold}")

                            game_window_info = active_price_fetching_windows.get(target_game_id)

                            should_activate_new_window = False
                            can_fetch_price_now = False

                            if not game_window_info or game_window_info["status"] == "paused":
                                should_activate_new_window = True
                                can_fetch_price_now = True # Start fetching immediately on new/re-activation
                            elif game_window_info["status"] == "active":
                                if current_time - game_window_info["start_time"] < game_window_info["duration"]:
                                    can_fetch_price_now = True # Window is active, polling task should be running or started
                                else:
                                    # Window just expired, mark as paused (already handled above, but as a safeguard here)
                                    active_price_fetching_windows[target_game_id]["status"] = "paused"
                                    polling_task = active_price_fetching_windows[target_game_id].get("polling_task")
                                    if polling_task and not polling_task.done():
                                        try:
                                            polling_task.cancel()
                                        except Exception as e_cancel:
                                            logger.error(f"取消轮询任务 for {target_game_id} 时出错 (safeguard): {e_cancel}")
                                    active_price_fetching_windows[target_game_id]["polling_task"] = None
                                    logger.info(f"价格获取窗口 (ID: {target_game_id}) 刚刚在此轮次中到期。")
                                    can_fetch_price_now = False
                            
                            if should_activate_new_window:
                                logger.info(f"为游戏 '{game_name_log}' (ID: {target_game_id}) 启动或重新激活 {price_fetch_window_duration}秒 价格获取/轮询窗口。")
                                if target_game_id not in active_price_fetching_windows or \
                                   active_price_fetching_windows[target_game_id]["status"] == "paused" or \
                                   active_price_fetching_windows[target_game_id].get("polling_task") is None or \
                                   active_price_fetching_windows[target_game_id]["polling_task"].done():
                                    
                                    active_price_fetching_windows[target_game_id] = {
                                        "status": "active",
                                        "start_time": current_time,
                                        "duration": price_fetch_window_duration,
                                        "last_known_price": None,
                                        "historical_low_price": None,
                                        "polling_task": None # Will be set below
                                    }
                                    # Pass the global SETTINGS, not a local dict
                                    task = asyncio.create_task(poll_price_for_game(
                                        target_game_id, game_name_log, wish_item, http_client,
                                        active_price_fetching_windows, recently_actioned_sales,
                                        order_cookies_dict_from_cfg, full_order_cookie_str_from_cfg,
                                        SETTINGS, logger # Pass SETTINGS and logger
                                    ))
                                    active_price_fetching_windows[target_game_id]["polling_task"] = task
                                else:
                                    logger.info(f"游戏 '{game_name_log}' (ID: {target_game_id}) 已有活动的轮询任务。")

            
            # If not time to fetch detlog, sleep for a bit before re-checking main loop timing
            active_polling_tasks_count = sum(1 for win_info in active_price_fetching_windows.values() if win_info.get("polling_task") and not win_info["polling_task"].done())
            
            if not (current_time - last_detlog_fetch_time >= next_detlog_fetch_delay):
                # If there are active polling tasks, main loop can sleep shorter to remain responsive for window management
                # If no polling tasks, it can sleep longer (e.g., 1s as before)
                sleep_interval = 0.1 if active_polling_tasks_count > 0 else 1.0
                await asyncio.sleep(sleep_interval) # Short sleep to prevent tight loop if detlog interval is long
            # The main decision to sleep longer until next detlog fetch is implicitly handled by the condition above.

if __name__ == "__main__":
    try:
        asyncio.run(main_logic())
    except KeyboardInterrupt:
        logger.info("Wish Monitor 已被用户中断。")
    except Exception as e:
        logger.error(f"Wish Monitor 遇到未处理的异常: {e}", exc_info=True) 