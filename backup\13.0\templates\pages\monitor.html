{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}监控中心 - PyWatcher{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', path='css/monitor.css') }}">
<link rel="stylesheet" href="{{ url_for('static', path='css/cdk-monitor.css') }}">
{% endblock %}

{% block content %}
<div class="container main-container">
    <h1 class="page-title">监控中心</h1>
    
    <!-- CDK低价监控部分 -->
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="card-title mb-0">CDK低价监控</h2>
            <div>
                <button id="add-monitor-game" class="btn btn-sm btn-outline-primary">
                    {{ icon('plus', 'mr-1', 14) }}添加游戏
                </button>
                <button id="toggle-cdk-monitor" class="btn btn-sm btn-outline-success">
                    {{ icon('play', 'mr-1', 14) }}开始监控
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 监控控制面板 -->
            <div class="cdk-monitor-controls mb-4">
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <label class="font-weight-bold">默认监控间隔 (毫秒):</label>
                        <div class="input-group">
                            <input type="number" id="cdk-min-interval" class="form-control" value="5000" min="1000" step="500">
                            <div class="input-group-prepend input-group-append">
                                <span class="input-group-text">-</span>
                            </div>
                            <input type="number" id="cdk-max-interval" class="form-control" value="15000" min="1000" step="500">
                            <div class="input-group-append">
                                <span class="input-group-text">ms</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <!-- 自动下单开关 -->
                        <div class="auto-order-switch d-flex align-items-center justify-content-center justify-content-md-end">
                            <label class="mr-2 font-weight-bold">自动下单:</label>
                            <label class="switch-control">
                                <input id="cdk-auto-order" type="checkbox">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-12 text-right">
                        <button id="save-cdk-settings" class="btn btn-sm btn-outline-primary">
                            {{ icon('save', 'mr-1', 14) }}保存设置
                        </button>
                    </div>
                </div>
                
                <!-- 下单后行为设置 -->
                <div class="row mt-3">
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center">
                            <label class="mr-2 font-weight-bold">下单成功后:</label>
                            <label class="switch-control">
                                <input id="cdk-stop-after-order" type="checkbox">
                                <span class="slider round"></span>
                            </label>
                            <small class="text-muted ml-2">停止监控该游戏</small>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <small class="text-muted">
                            开启后，游戏下单成功会自动从监控列表中移除；关闭则继续监控价格变化
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- 监控游戏列表 -->
            <div class="cdk-monitored-games mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="section-title border-left border-primary pl-2 mb-0">监控游戏列表</h5>
                    <div>
                        <button id="view-hot-games" class="btn btn-sm btn-outline-primary mr-2">
                            {{ icon('trending-up', 'mr-1', 14) }}热门游戏
                        </button>
                        <button id="export-cdk-games" class="btn btn-sm btn-outline-info">
                            {{ icon('download', 'mr-1', 14) }}导出列表
                        </button>
                    </div>
                </div>
                <div id="cdk-monitored-games-container" class="games-container">
                    <div class="text-muted text-center py-4">暂无监控游戏，请从游戏搜索或肥皂盒详情页添加...</div>
                </div>
            </div>
            
            <!-- CDK监控记录 -->
            <div class="cdk-monitor-logs mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="section-title border-left border-primary pl-2 mb-0">监控记录</h5>
                    <button id="clear-cdk-logs" class="btn btn-sm btn-outline-secondary">
                        {{ icon('trash', 'mr-1', 14) }}清空记录
                    </button>
                </div>
                <div id="cdk-monitor-logs" class="monitor-logs-container bg-light border rounded p-2">
                    <div class="text-muted text-center py-4">监控记录将显示在这里...</div>
                </div>
            </div>
            
            <!-- 变动记录表格 -->
            <div class="cdk-changes-panel">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="section-title border-left border-primary pl-2 mb-0">价格变动记录</h5>
                    <div>
                        <button id="clear-cdk-changes" class="btn btn-sm btn-outline-secondary mr-2">
                            {{ icon('trash', 'mr-1', 14) }}清空记录
                        </button>
                        <button id="clear-ordered-cdks" class="btn btn-sm btn-outline-danger">
                            {{ icon('x-circle', 'mr-1', 14) }}清除已下单记录
                        </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover table-striped table-compact">
                        <thead class="thead-light">
                            <tr>
                                <th>游戏名称</th>
                                <th>原价格</th>
                                <th>新价格</th>
                                <th>变动时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="cdk-price-changes">
                            <tr class="text-center text-muted">
                                <td colspan="5">暂无价格变动记录...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 肥皂盒监控部分 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 class="card-title mb-0">肥皂盒监控</h2>
            <a href="#" id="favorites-link" class="btn btn-sm btn-outline-primary">
                {{ icon('star', 'mr-1', 14) }}收藏列表
            </a>
        </div>
        <div class="card-body">
            <!-- 控制面板 -->
            <div class="monitor-control-panel mb-4">
                <div class="row align-items-center">
                    <div class="col-md-4 mb-3 mb-md-0">
                        <!-- 开始/停止按钮 -->
                        <button id="toggleMonitor" class="btn btn-primary btn-lg w-100">
                            {{ icon('play', 'mr-2', 16) }}开始监控
                        </button>
                    </div>
                    <div class="col-md-5 mb-3 mb-md-0">
                        <!-- 监控间隔设置 -->
                        <div class="interval-controls d-flex align-items-center">
                            <label class="mr-2 font-weight-bold">监控间隔:</label>
                            <div class="input-group">
                                <input type="number" id="minInterval" value="5000" min="1000" step="500" class="form-control">
                                <div class="input-group-prepend input-group-append">
                                    <span class="input-group-text">-</span>
                                </div>
                                <input type="number" id="maxInterval" value="18000" min="1000" step="500" class="form-control">
                                <div class="input-group-append">
                                    <span class="input-group-text">ms</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <!-- 自动下单开关 -->
                        <div class="auto-order-switch d-flex align-items-center justify-content-center justify-content-md-end">
                            <label class="mr-2 font-weight-bold">自动下单:</label>
                            <label class="switch-control">
                                <input id="autoOrder" type="checkbox">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 监控模式选择 -->
            <div class="monitor-modes-panel mb-4">
                <h5 class="section-title border-left border-primary pl-2 mb-3">监控模式:</h5>
                <div class="monitor-mode-options">
                    <label class="mode-option">
                        <input type="checkbox" value="expected" name="monitorMode" checked>
                        <span class="mode-check"></span>
                        <span class="mode-label">期望</span>
                        <span class="mode-description">监控肥皂盒期望价值</span>
                    </label>
                    
                    <label class="mode-option">
                        <input type="checkbox" value="probability" name="monitorMode" checked>
                        <span class="mode-check"></span>
                        <span class="mode-label">概率</span>
                        <span class="mode-description">监控肥皂盒抽取概率</span>
                    </label>
                    
                    <label class="mode-option">
                        <input type="checkbox" value="wrongPrice" name="monitorMode" checked>
                        <span class="mode-check"></span>
                        <span class="mode-label">错价</span>
                        <span class="mode-description">监控肥皂盒错误定价</span>
                    </label>
                    
                    <label class="mode-option">
                        <input type="checkbox" value="specific" name="monitorMode">
                        <span class="mode-check"></span>
                        <span class="mode-label">特定</span>
                        <span class="mode-description">监控特定肥皂盒</span>
                    </label>
                </div>
            </div>
            
            <!-- 特定监控配置面板 -->
            <div id="specific-monitor-panel" class="monitor-specific-panel mb-4" style="display: none;">
                <div class="specific-header d-flex justify-content-between align-items-center mb-3">
                    <h5 class="section-title border-left border-primary pl-2 mb-0">收藏肥皂盒监控:</h5>
                    <div>
                        <button id="refresh-favorites" class="btn btn-sm btn-outline-secondary mr-2">
                            {{ icon('refresh-cw', 'mr-1', 14) }}刷新列表
                        </button>
                        <button id="manage-favorites" class="btn btn-sm btn-outline-primary">
                            {{ icon('edit', 'mr-1', 14) }}管理收藏
                        </button>
                    </div>
                </div>
                <div class="specific-chests-container">
                    <div id="favorite-chests-list" class="favorite-chests-list">
                        <div class="text-muted text-center py-4">暂无收藏的肥皂盒...</div>
                    </div>
                </div>
            </div>
            
            <!-- 监控记录 -->
            <div class="monitor-logs-panel">
                <div class="logs-header d-flex justify-content-between align-items-center mb-3">
                    <h5 class="section-title border-left border-primary pl-2 mb-0">监控记录:</h5>
                    <button id="clearLogs" class="btn btn-sm btn-outline-secondary">
                        {{ icon('trash', 'mr-1', 14) }}清空记录
                    </button>
                </div>
                <div id="monitorLogs" class="monitor-logs-container bg-light border rounded p-2">
                    <div class="text-muted text-center py-4">监控记录将显示在这里...</div>
                </div>
            </div>
            
            <!-- 命中记录表格 -->
            <div class="monitor-hits-panel mt-4">
                <div class="hits-header d-flex justify-content-between align-items-center mb-3">
                    <h5 class="section-title border-left border-primary pl-2 mb-0">命中记录:</h5>
                    <button id="clearHits" class="btn btn-sm btn-outline-secondary">
                        {{ icon('trash', 'mr-1', 14) }}清空记录
                    </button>
                </div>
                <div class="monitor-hits-container">
                    <table class="table table-hover table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>肥皂盒ID</th>
                                <th>监控类型</th>
                                <th>单抽价格</th>
                                <th>多抽价格</th>
                                <th>命中时间</th>
                            </tr>
                        </thead>
                        <tbody id="monitorHits">
                            <tr class="text-center text-muted">
                                <td colspan="5">暂无命中记录...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 收藏管理模态框 -->
<div id="favorites-modal" class="favorites-modal">
    <div class="favorites-modal-content">
        <div class="favorites-modal-header">
            <h3 class="favorites-modal-title">管理收藏肥皂盒</h3>
            <button class="favorites-modal-close">&times;</button>
        </div>
        <div class="favorites-modal-body">
            <div class="form-group mb-3">
                <label for="add-chest-id">添加肥皂盒ID:</label>
                <div class="input-group">
                    <input type="text" id="add-chest-id" class="form-control" placeholder="输入肥皂盒ID">
                    <div class="input-group-append">
                        <button id="add-chest-btn" class="btn btn-primary">添加</button>
                    </div>
                </div>
                <small class="form-text text-muted">输入肥皂盒ID添加到收藏列表</small>
            </div>
            <div class="mb-3">
                <h4 class="mb-3">当前收藏:</h4>
                <div id="favorites-list" class="favorites-list">
                    <div class="text-muted text-center py-4">暂无收藏的肥皂盒...</div>
                </div>
            </div>
        </div>
        <div class="favorites-modal-footer">
            <button id="close-favorites-modal" class="btn btn-secondary">关闭</button>
        </div>
    </div>
</div>

<!-- 添加游戏监控模态框 -->
<div id="add-game-modal" class="favorites-modal">
    <div class="favorites-modal-content">
        <div class="favorites-modal-header">
            <h3 class="favorites-modal-title">添加CDK监控游戏</h3>
            <button class="favorites-modal-close" id="close-add-game-modal-x">&times;</button>
        </div>
        <div class="favorites-modal-body">
            <div class="form-group mb-3">
                <label for="game-search-input">搜索游戏:</label>
                <div class="input-group">
                    <input type="text" id="game-search-input" class="form-control" placeholder="输入游戏名称">
                    <div class="input-group-append">
                        <button id="search-game-btn" class="btn btn-primary">搜索</button>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <h4 class="mb-3">搜索结果:</h4>
                <div id="game-search-results" class="game-search-results">
                    <div class="text-muted text-center py-4">请输入游戏名称进行搜索...</div>
                </div>
            </div>
        </div>
        <div class="favorites-modal-footer">
            <button id="close-add-game-modal" class="btn btn-secondary">关闭</button>
        </div>
    </div>
</div>

<!-- 设置目标价格模态框 -->
<div id="set-target-price-modal" class="favorites-modal">
    <div class="favorites-modal-content">
        <div class="favorites-modal-header">
            <h3 class="favorites-modal-title">设置目标监控价格</h3>
            <button class="favorites-modal-close" id="close-target-price-modal-x">&times;</button>
        </div>
        <div class="favorites-modal-body">
            <div id="target-game-info" class="d-flex align-items-center mb-3">
                <img id="target-game-img" src="" class="game-avatar-sm mr-2" style="width:40px;height:40px;border-radius:4px;">
                <h5 id="target-game-name" class="mb-0">游戏名称</h5>
            </div>
            <div class="form-group mb-3">
                <label for="target-price-input">目标价格 (￥):</label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text">￥</span>
                    </div>
                    <input type="number" id="target-price-input" class="form-control" placeholder="输入目标价格" min="0" step="0.01">
                </div>
                <small class="form-text text-muted">当价格低于此值时，将会高亮提示</small>
            </div>
            <div class="form-group">
                <label for="price-notification-type">提醒方式:</label>
                <select id="price-notification-type" class="form-control">
                    <option value="highlight">仅高亮显示</option>
                    <option value="browser">浏览器通知</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="game-group-input">游戏分组 (可选):</label>
                <div class="input-group">
                    <input type="text" id="game-group-input" class="form-control" placeholder="输入分组名称，如：黑神话悟空系列">
                    <div class="input-group-append">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                            选择已有分组
                        </button>
                        <div class="dropdown-menu" id="existing-groups-dropdown">
                            <!-- 现有分组将在这里动态加载 -->
                        </div>
                    </div>
                </div>
                <small class="form-text text-muted">同一分组的游戏，只要有一个下单成功，其他都会自动停止监控</small>
            </div>
        </div>
        <div class="favorites-modal-footer">
            <button id="clear-target-price-btn" class="btn btn-outline-danger mr-2">清除设置</button>
            <button id="save-target-price-btn" class="btn btn-primary">保存</button>
            <button id="close-target-price-modal" class="btn btn-secondary ml-2">取消</button>
        </div>
    </div>
</div>

<!-- 导出CDK监控列表模态框 -->
<div id="export-modal" class="favorites-modal">
    <div class="favorites-modal-content">
        <div class="favorites-modal-header">
            <h3 class="favorites-modal-title">导出监控列表</h3>
            <button class="favorites-modal-close" id="close-export-modal-x">&times;</button>
        </div>
        <div class="favorites-modal-body">
            <div class="alert alert-info">以下是当前的CDK监控列表，请复制需要的内容：</div>
            <div class="form-group">
                <textarea id="export-content" class="form-control" style="height: 300px; font-family: monospace;" readonly></textarea>
            </div>
        </div>
        <div class="favorites-modal-footer">
            <button id="copy-export-btn" class="btn btn-primary">复制</button>
            <button id="close-export-modal" class="btn btn-secondary ml-2">关闭</button>
        </div>
    </div>
</div>

<!-- 热门游戏模态框 -->
<div id="hot-games-modal" class="modal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">热门游戏列表</h5>
                <button type="button" id="close-hot-games-modal-x" class="close">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="hot-games-container">
                    <!-- 游戏列表将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="close-hot-games-modal" class="btn btn-secondary">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='js/monitor.js') }}"></script>
<script src="{{ url_for('static', path='js/cdk-monitor-core.js') }}"></script>
<script src="{{ url_for('static', path='js/cdk-monitor.js') }}"></script>
{% endblock %} 




