import json
from base64 import b64decode
from typing import List, Optional, Dict, Any
from datetime import datetime

from pydantic import BaseModel, Field

# Models for Mobile Cookie Pool
class MobileAccount(BaseModel):
    id: str
    name: str
    access_token: Optional[str] = Field(None, description="访问令牌")
    is_primary: bool = Field(default=False, description="是否为主账户")
    account_type: str = Field(default="primary", description="账户类型")
    added_at: Optional[str] = None
    login_method: str = Field(default="sms", description="登录方式")

    # 确保能够处理账户和主账户标志
    account: Optional[str] = None
    password: Optional[str] = None
    token_expire_time: Optional[str] = None
    status: Optional[int] = 1
    remark: Optional[str] = None

    class Config:
        populate_by_name = True


class MobileCookiePool(BaseModel):
    accounts: List[MobileAccount] 