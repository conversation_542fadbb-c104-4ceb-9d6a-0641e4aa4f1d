import json
from base64 import b64decode
from typing import List, Optional, Dict, Any
from datetime import datetime

from pydantic import BaseModel, Field

# Models for Mobile Cookie Pool
class MobileAccount(BaseModel):
    id: str
    name: str
    access_token: Optional[str] = Field(None, alias="access_token")
    is_primary: bool = Field(default=False, description="是否为主账户")
    account_type: str = Field(default="primary", description="账户类型")
    added_at: Optional[str] = None
    login_method: str = Field(default="sms", description="登录方式")

    class Config:
        populate_by_name = True


class MobileCookiePool(BaseModel):
    accounts: List[MobileAccount] 