// This script is intended for the games page.
document.addEventListener('DOMContentLoaded', () => {
    // DOM Element References
    const searchInput = document.getElementById('game-search-input');
    const searchForm = document.getElementById('game-search-form');
    const resultsContainer = document.getElementById('game-results-container');
    const gameCardTemplate = document.getElementById('game-card-template');

    // Modal elements
    const modal = document.getElementById('sellers-modal');
    const closeModalButton = modal?.querySelector('.close-btn');
    const refreshModalButton = modal?.querySelector('.refresh-btn');
    const modalGameTitle = document.getElementById('modal-game-title');
    const modalSellersList = document.getElementById('modal-sellers-list');
    const modalLoading = document.getElementById('modal-loading');
    
    // 当前查看的游戏信息 - 用于刷新功能
    let currentGameInfo = null;

    // 收藏功能
    let favorites = JSON.parse(localStorage.getItem('gamesFavorites') || '[]');

    /**
     * 检查游戏是否被收藏
     * @param {string} gameId - 游戏ID
     * @returns {boolean}
     */
    function isFavorite(gameId) {
        return favorites.some(fav => fav.id === gameId);
    }

    /**
     * 切换游戏收藏状态
     * @param {object} game - 游戏数据
     * @param {HTMLElement} button - 收藏按钮元素
     */
    function toggleFavorite(game, button) {
        if (isFavorite(game.id)) {
            // 从收藏中移除
            favorites = favorites.filter(fav => fav.id !== game.id);
            button.textContent = '☆';
            button.classList.remove('active');
        } else {
            // 添加到收藏
            favorites.push({
                id: game.id,
                gameName: game.gameName,
                picUrl: game.picUrl,
                keyPrice: game.keyPrice,
                keyAveAmt: game.keyAveAmt
            });
            button.textContent = '★';
            button.classList.add('active');
        }
        
        // 保存到本地存储
        localStorage.setItem('gamesFavorites', JSON.stringify(favorites));
        
        // 如果在收藏页面，需要刷新列表
        if (window.location.pathname === '/favorites') {
            renderFavorites();
        }
    }

    /**
     * Create a game card element from game data
     * @param {object} game - Game data
     * @returns {HTMLElement}
     */
    function createGameCard(game) {
        const card = gameCardTemplate.content.cloneNode(true).firstElementChild;
        card.querySelector('.result-card-image').src = game.picUrl;
        card.querySelector('.result-card-name').textContent = game.gameName;
        card.querySelector('.result-card-id').textContent = `ID: ${game.id}`;

        // Populate prices
        const keyPriceEl = card.querySelector('.key-price');
        if (game.keyPrice !== null && game.keyPrice !== undefined) {
            keyPriceEl.textContent = `¥${game.keyPrice.toFixed(2)}`;
        }

        const keyAveAmtEl = card.querySelector('.key-ave-amt');
        if (game.keyAveAmt !== null && game.keyAveAmt !== undefined) {
            keyAveAmtEl.textContent = `¥${game.keyAveAmt.toFixed(2)}`;
        }
        
        // 添加收藏按钮功能
        const favoriteButton = card.querySelector('.favorite-btn');
        if (favoriteButton) {
            if (isFavorite(game.id)) {
                favoriteButton.textContent = '★';
                favoriteButton.classList.add('active');
            }
            
            favoriteButton.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(game, favoriteButton);
            });
        }
        
        const sellersButton = card.querySelector('.sellers-btn');
        sellersButton.addEventListener('click', () => openSellersModal(game));

        return card;
    }

    /**
     * Handle the game search
     */
    async function handleGameSearch(event) {
        // Ensure to prevent the default form submission behavior
        event.preventDefault();
        event.stopPropagation();

        const query = searchInput.value.trim();
        if (!query) {
            resultsContainer.innerHTML = '<p class="text-muted">请输入关键词进行搜索。</p>';
            return;
        }

        resultsContainer.innerHTML = '<div class="loading-indicator"><div class="spin"></div><span>正在搜索...</span></div>';
        
        try {
            const searchUrl = `/api/games/search?query=${encodeURIComponent(query)}`;
            const response = await fetch(searchUrl);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }));
                throw new Error(errorData.detail || '搜索请求失败');
            }
            const games = await response.json();
            
            resultsContainer.innerHTML = '';
            if (!Array.isArray(games) || games.length === 0) {
                resultsContainer.innerHTML = '<p>没有找到相关游戏。</p>';
                return;
            }
            
            games.forEach(game => {
                const card = createGameCard(game);
                resultsContainer.appendChild(card);
            });

        } catch (error) {
            resultsContainer.innerHTML = `<p class="error-indicator">搜索失败: ${error.message}</p>`;
        }
    }

    /**
     * 渲染收藏列表
     */
    function renderFavorites() {
        const favoritesContainer = document.getElementById('favorites-container');
        if (!favoritesContainer) return;
        
        if (favorites.length === 0) {
            favoritesContainer.innerHTML = '<div class="empty-favorites">您还没有收藏任何游戏</div>';
            return;
        }
        
        // 创建无序列表元素
        const favoritesList = document.createElement('ul');
        favoritesList.className = 'favorites-list';
        
        // 清空容器并添加列表
        favoritesContainer.innerHTML = '';
        favoritesContainer.appendChild(favoritesList);
        
        // 添加每个收藏项目
        favorites.forEach(game => {
            const card = createGameCard(game);
            // 创建列表项并添加卡片
            const listItem = document.createElement('li');
            listItem.className = 'favorites-item';
            listItem.appendChild(card);
            favoritesList.appendChild(listItem);
        });
    }

    /**
     * 获取并显示卖家列表
     * @param {object} game - 游戏数据对象
     * @param {boolean} isRefresh - 是否为刷新操作
     */
    async function fetchAndDisplaySellers(game, isRefresh = false) {
        if (!isRefresh) {
            modalSellersList.innerHTML = '';
        }
        modalLoading.style.display = 'block';

        try {
            const response = await fetch(`/api/games/${game.id}/sellers`);
            const responseData = await response.json().catch(e => ({ error: '解析响应失败' }));
            
            modalLoading.style.display = 'none';
            
            if (!response.ok) {
                // 非200响应，显示错误信息
                const errorMessage = responseData.detail || '获取卖家信息失败';
                console.error('Error fetching sellers:', errorMessage);
                modalSellersList.innerHTML = `<li class="error">错误: ${errorMessage}</li>`;
                return;
            }
            
            // 响应正常，但可能是空数组
            if (!Array.isArray(responseData) || responseData.length === 0) {
                modalSellersList.innerHTML = '<li>暂无卖家信息。</li>';
                return;
            }

            // 有卖家数据，创建列表
            modalSellersList.innerHTML = ''; // 清空现有内容
            const list = document.createElement('ul');
            list.className = 'sellers-list';
            
            responseData.forEach(seller => {
                const item = document.createElement('li');
                
                // 根据API返回字段获取卖家信息
                const sellerName = seller.steamName || 'N/A';
                const price = typeof seller.keyPrice === 'number' ? `¥${seller.keyPrice.toFixed(2)}` : 'N/A';
                const quantity = seller.stock || 0;
                const saleId = seller.saleId || '';
                const discount = seller.discount ? `${(seller.discount * 100).toFixed(0)}%` : '';
                
                item.className = 'seller-item';
                item.setAttribute('data-sale-id', saleId); // 将saleId存储为数据属性
                
                // 创建更详细的卖家信息显示
                item.innerHTML = `
                    <div class="seller-info-group">
                        <span class="seller-name">卖家: ${sellerName}</span>
                        <span class="seller-price">价格: ${price}</span>
                        ${discount ? `<span class="seller-discount">折扣: ${discount}</span>` : ''}
                        <span class="seller-quantity">库存: ${quantity}</span>
                    </div>
                    <div class="seller-actions">
                        <span class="seller-id text-muted">订单号: ${saleId}</span>
                        <button class="order-btn ${quantity <= 0 ? 'disabled' : ''}" data-sale-id="${saleId}" ${quantity <= 0 ? 'disabled' : ''}>
                            ${quantity > 0 ? '下单购买' : '库存不足'}
                        </button>
                    </div>
                `;
                
                // 添加下单按钮点击事件监听
                const orderBtn = item.querySelector('.order-btn');
                if (orderBtn && !orderBtn.disabled) {
                    orderBtn.addEventListener('click', function() {
                        const saleId = this.getAttribute('data-sale-id');
                        if (saleId) {
                            placeOrder(saleId);
                        }
                    });
                }
                
                list.appendChild(item);
            });
            
            modalSellersList.appendChild(list);

            // 刷新按钮反馈
            if (isRefresh) {
                const refreshNotice = document.createElement('div');
                refreshNotice.className = 'refresh-notice';
                refreshNotice.textContent = '已更新卖家信息';
                modalSellersList.insertBefore(refreshNotice, modalSellersList.firstChild);
                
                // 2秒后自动隐藏提示
                setTimeout(() => {
                    refreshNotice.style.opacity = '0';
                    setTimeout(() => refreshNotice.remove(), 500);
                }, 2000);
            }

        } catch (error) {
            console.error('Error fetching sellers:', error);
            modalLoading.style.display = 'none';
            modalSellersList.innerHTML = `<li class="error">加载卖家信息失败: ${error.message || '未知错误'}</li>`;
        }
    }

    /**
     * Open the sellers modal
     * @param {object} game
     */
    async function openSellersModal(game) {
        modal.style.display = 'block';
        modalGameTitle.textContent = `卖家列表 - ${game.gameName}`;
        currentGameInfo = game; // 保存当前查看的游戏信息
        
        // 获取卖家列表
        await fetchAndDisplaySellers(game);
    }

    /**
     * 刷新当前卖家列表
     */
    async function refreshSellersList() {
        if (!currentGameInfo) return;
        
        // 先显示旋转效果
        const refreshIcon = refreshModalButton.querySelector('svg');
        if (refreshIcon) {
            refreshIcon.classList.add('spin');
        }
        
        // 获取最新数据
        await fetchAndDisplaySellers(currentGameInfo, true);
        
        // 停止旋转效果
        if (refreshIcon) {
            setTimeout(() => {
                refreshIcon.classList.remove('spin');
            }, 500);
        }
    }

    /**
     * 获取游戏价格
     * @param {string} gameId - 游戏ID
     * @param {string} gameName - 游戏名称，用于日志（可选）
     * @returns {Promise<number>} 游戏价格
     */
    async function fetchGamePrice(gameId, gameName = null) {
        if (!gameId) {
            console.error('fetchGamePrice: 游戏ID为空');
            return 0;
        }

        try {
            // 直接使用现有的sellers API，但只获取一个卖家信息
            const url = `/api/games/${gameId}/sellers?pageSize=1`;
            const response = await fetch(url);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }));
                console.error(`获取游戏价格失败: ${errorData.detail || '请求失败'}`);
                return 0;
            }
            
            const sellersData = await response.json();
            
            // 检查返回的卖家数据
            if (Array.isArray(sellersData) && sellersData.length > 0) {
                const firstSeller = sellersData[0];
                if (firstSeller && typeof firstSeller.keyPrice === 'number') {
                    console.log(`获取到游戏 ${gameName || gameId} 的价格: ${firstSeller.keyPrice}`);
                    return firstSeller.keyPrice;
                }
            }
            
            console.warn(`没有找到游戏 ${gameName || gameId} 的有效价格`);
            return 0;
        } catch (error) {
            console.error(`获取游戏价格时出错: ${error.message || error}`);
            return 0;
        }
    }

    /**
     * 下单购买
     * @param {string} saleId - 卖家订单ID
     */
    async function placeOrder(saleId) {
        if (!saleId) {
            console.error('下单失败：无效的订单ID');
            showToast('下单失败：无效的订单ID', true);
            return;
        }

        // 确认是否下单
        if (!confirm('确定要购买该商品吗？')) {
            return;
        }

        // 从本地存储获取下单设置（使用新的CDK下单设置）
        const isMainAccount = localStorage.getItem('cdkUseMainAccount') !== 'false'; // 默认使用主账号
        const payType = localStorage.getItem('cdkPayType') || 'AU';
        
        // 直接获取localStorage中的值，不添加默认值
        // 如果值是空字符串，也应该保持为空字符串
        const walletFlag = localStorage.getItem('cdkUseBalance');
        
        // 显示当前使用的账号类型
        const accountTypeText = isMainAccount ? '下单主账号' : '下单副账号';
        
        // 获取当前游戏名称，用于传递到下单API
        const gameName = currentGameInfo ? currentGameInfo.gameName : '';

        try {
            // 显示下单中
            showToast(`正在使用${accountTypeText}处理下单请求...`, false);

            // 构建API请求URL和参数
            const url = `/api/xboot/steamKeyOrder/payOrder`;
            const params = new URLSearchParams({
                saleId: saleId,
                payType: payType,
                promoCodeId: '',
                walletFlag: walletFlag,
                version: 'v1',
                is_main_account: isMainAccount.toString(), // 添加是否使用主账号的参数
                gameName: gameName // 添加游戏名称作为参数
            });
            
            // 发送下单请求
            const response = await fetch(`${url}?${params.toString()}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            // 解析响应
            let data;
            try {
                data = await response.json();
            } catch (parseError) {
                console.error('解析响应失败：', parseError);
                showToast(`服务器响应解析失败：${parseError.message}`, true);
                return;
            }

            // 处理错误状态
            if (!response.ok) {
                // 特殊错误处理
                if (response.status === 404 && data.detail && data.detail.includes('未配置')) {
                    // 账号未配置错误
                    showToast(`${data.detail} 请前往账号管理页面设置`, true);
                    setTimeout(() => {
                        if (confirm(`${data.detail} 是否前往账号管理页面？`)) {
                            window.location.href = '/accounts';
                        }
                    }, 1000);
                    return;
                } else if (response.status === 401 && data.detail && data.detail.includes('未登录')) {
                    // 账号未登录错误
                    showToast(`${data.detail}`, true);
                    return;
                } else {
                    // 其他API错误
                    const errorMessage = data.detail || `服务器返回错误状态：${response.status}`;
                    console.error('下单失败：', errorMessage);
                    showToast(`下单失败：${errorMessage}`, true);
                    return;
                }
            }

            // 处理响应结果
            if (data && data.success) {
                showToast(`${accountTypeText}下单成功！${data.message || ''}`, false);
                // 关闭卖家列表模态窗口
                if (modal) {
                    modal.style.display = 'none';
                }
            } else {
                // API返回成功但业务逻辑失败
                const errorMessage = data.message || '未知错误';
                console.error('下单失败：', errorMessage);
                showToast(`下单失败：${errorMessage}`, true);
            }
        } catch (error) {
            console.error('下单过程中发生错误：', error);
            showToast(`下单失败：${error.message || '未知错误'}`, true);
        }
    }
    
    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {boolean} isError - 是否为错误消息
     */
    function showToast(message, isError = false) {
        // 查找现有的toast元素或创建新的
        let toast = document.querySelector('.toast');
        if (!toast) {
            toast = document.createElement('div');
            toast.className = 'toast';
            document.body.appendChild(toast);
        }
        
        // 设置消息内容和样式
        toast.textContent = message;
        toast.className = `toast ${isError ? 'error' : ''}`;
        
        // 显示toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // 3秒后关闭
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // Event Listeners
    if (searchForm) {
        searchForm.addEventListener('submit', handleGameSearch);
    }

    // Modal event listeners
    if (closeModalButton) {
        closeModalButton.addEventListener('click', () => {
            modal.style.display = 'none';
        });
    }
    
    // 刷新按钮事件监听
    if (refreshModalButton) {
        refreshModalButton.addEventListener('click', refreshSellersList);
    }

    if (modal) {
        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
    
    // 初始化
    // 如果在收藏页面，渲染收藏列表
    if (window.location.pathname === '/favorites') {
        renderFavorites();
    }
});