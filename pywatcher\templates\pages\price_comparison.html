{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}游戏比价 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <div class="header-content">
        <h1>
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M12 6v6l4 2"></path>
            </svg>
            游戏比价
        </h1>
        <p class="page-description">
            搜索并比较不同来源的游戏价格信息。
        </p>
    </div>
</header>

<!-- 统计信息卡片 -->
<section>
    <div id="stats-card" class="card">
        <h3>数据统计</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-label">总游戏数</span>
                <span class="stat-value" id="total-games">-</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">fhyx来源</span>
                <span class="stat-value" id="fhyx-count">-</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">py来源</span>
                <span class="stat-value" id="py-count">-</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最近更新</span>
                <span class="stat-value" id="last-update">-</span>
            </div>
        </div>
    </div>
</section>

<!-- 数据管理区域 -->
<section>
    <div class="card">
        <h3>数据管理</h3>
        <div class="form-actions">
            <button id="update-fhyx-btn" class="primary">
                {{ icon('refresh-cw') }} 更新fhyx数据
            </button>
            <button id="migrate-py-btn" class="outline">
                {{ icon('database') }} 迁移py数据
            </button>
        </div>
        <div id="update-status" class="status-message" style="display: none;"></div>
    </div>
</section>

<!-- 搜索区域 -->
<section>
    <div id="search-card" class="card">
        <form id="price-search-form" class="form-grid">
            <div class="form-group" style="grid-column: 1 / -1;">
                <label for="price-search-input">搜索游戏名称</label>
                <input type="search" id="price-search-input" name="term" placeholder="例如：黑神话悟空" required>
            </div>
            <div class="form-actions" style="grid-column: 1 / -1;">
                <button type="submit" class="primary">
                    {{ icon('search') }} 搜索价格
                </button>
                <button type="button" id="show-all-btn" class="outline">
                    {{ icon('list') }} 显示全部
                </button>
            </div>
        </form>
    </div>
</section>

<!-- 搜索结果区域 -->
<section>
    <header class="article-header">
        <h2>价格比较结果</h2>
    </header>
    <div id="price-results-container">
        <p class="text-muted">输入关键词开始搜索，或点击"显示全部"查看所有游戏...</p>
    </div>
</section>

<!-- 价格比较结果模板 -->
<template id="price-result-template">
    <div class="price-comparison-card">
        <div class="game-info">
            <h4 class="game-name"></h4>
            <div class="price-sources">
                <!-- 价格来源信息将在这里动态生成 -->
            </div>
        </div>
    </div>
</template>

<!-- 价格来源项模板 -->
<template id="price-source-template">
    <div class="price-source-item">
        <span class="source-name"></span>
        <span class="source-price"></span>
        <span class="source-updated"></span>
    </div>
</template>

<style>
/* 页面特定样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--background-secondary);
    border-radius: 8px;
}

.stat-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.price-comparison-card {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: var(--background-primary);
}

.game-name {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.price-sources {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.price-source-item {
    display: grid;
    grid-template-columns: 80px 1fr auto;
    gap: 1rem;
    align-items: center;
    padding: 0.75rem;
    background: var(--background-secondary);
    border-radius: 6px;
}

.source-name {
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    text-align: center;
}

.source-name.fhyx {
    background: #e3f2fd;
    color: #1565c0;
}

.source-name.py {
    background: #f3e5f5;
    color: #7b1fa2;
}

.source-price {
    font-weight: 600;
    color: var(--success-color);
    font-size: 1.125rem;
}

.source-updated {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.status-message {
    margin-top: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    border-left: 4px solid;
}

.status-message.success {
    background: var(--success-background);
    border-color: var(--success-color);
    color: var(--success-color);
}

.status-message.error {
    background: var(--error-background);
    border-color: var(--error-color);
    color: var(--error-color);
}

.status-message.info {
    background: var(--info-background);
    border-color: var(--info-color);
    color: var(--info-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .price-source-item {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 0.5rem;
    }
    
    .form-actions {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/price-comparison.js') }}" defer></script>
{% endblock %}

