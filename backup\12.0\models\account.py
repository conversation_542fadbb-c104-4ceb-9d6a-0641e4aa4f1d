from pydantic import BaseModel, Field
from typing import Optional, Literal

class MobileAccount(BaseModel):
    """用于短信登录和存储的账户模型"""
    id: str = Field(..., description="通常是手机号")
    name: str = Field("", description="用户备注或昵称")
    access_token: str = Field(..., description="从API获取的访问令牌")
    account_type: str = "SMS" # 保持和旧版兼容，可选值: SMS, primary, order_primary, order_secondary, cdk_query
    is_cdk_query: bool = Field(False, description="是否为CDK专用查询账号") 