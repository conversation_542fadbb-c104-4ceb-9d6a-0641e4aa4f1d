from fastapi import APIRouter, HTTPException, Body, Depends
from typing import List, Dict, Any
from pydantic import BaseModel, Field

from pywatcher.models.cookie_pool import MobileAccount, AccountType
from pywatcher.crud.cookie_pool_manager import CookiePoolManager
from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.api.dependencies import get_steam_client_for_login, get_cookie_pool_manager

# 账号类型的中文名称映射
ACCOUNT_TYPE_NAMES = {
    AccountType.QUERY: "查询账户",
    AccountType.ORDER_PRIMARY: "下单主账号",
    AccountType.ORDER_SECONDARY: "下单副账号",
    AccountType.BACKUP: "备用账号"
}

router = APIRouter(
    prefix="/accounts",
    tags=["Accounts"]
)

class SmsLoginRequest(BaseModel):
    phone: str
    code: str
    remark: str = "" # Add an optional remark field

class AccountTypeRequest(BaseModel):
    account_type: AccountType

@router.get("", response_model=List[MobileAccount])
async def get_accounts(
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """获取所有存储在 Mobile Cookie Pool 中的账户列表。"""
    return cookie_manager.get_all_mobile_accounts()

@router.post("/login", response_model=MobileAccount)
async def login_and_add_account(
    login_data: SmsLoginRequest,
    client: SteamPyClient = Depends(get_steam_client_for_login),
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """通过短信验证码登录，如果成功，则将账户添加到 Mobile Cookie Pool 中。"""
    response = await client.verify_login_code(login_data.phone, login_data.code)
    
    if not response or response.get("code") != 200:
        raise HTTPException(status_code=401, detail=response.get("message", "登录失败或验证码错误"))
        
    user_info = response.get("result", {})
    access_token = user_info.get("token")
    
    if not access_token:
        raise HTTPException(status_code=500, detail="登录成功，但未能获取到Token")

    # The password is not returned by the API, so we leave it empty or handle as needed.
    # We use the phone number as the 'account' identifier.
    new_account = MobileAccount(
        account=login_data.phone,
        password="", # Password is not available from SMS login
        access_token=access_token,
        token_expire_time=user_info.get("tokenExpireTime"),
        remark=login_data.remark or f"用户-{login_data.phone[-4:]}",
        status=1, # Assume status is active upon successful login
        account_type=AccountType.BACKUP # 默认为备用账号
    )
    
    try:
        # The manager handles adding and saving to the JSON file.
        added_account = cookie_manager.add_mobile_account(new_account)
        cookie_manager.save_pool()  # 持久化更改
        return added_account
    except ValueError as e:
        # This will catch duplicates
        raise HTTPException(status_code=409, detail=str(e))


@router.delete("/{account_name}", status_code=204)
async def delete_account(
    account_name: str,
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """从 Mobile Cookie Pool 中删除一个账户。"""
    success = cookie_manager.remove_mobile_account(account_name)
    if not success:
        raise HTTPException(status_code=404, detail=f"未在移动账户池中找到账户: {account_name}")
    
    cookie_manager.save_pool()  # 持久化更改
    return None

@router.put("/{account_id}/set-type")
async def set_account_type(
    account_id: str,
    request: AccountTypeRequest,
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager)
):
    """设置指定账户的类型"""
    # 先找到指定账户
    account = cookie_manager.get_mobile_account(account_id)
    if not account:
        raise HTTPException(status_code=404, detail=f"未找到账户: {account_id}")
    
    # 如果不是设置为备用账号，需要检查是否已经存在同类型账号
    if request.account_type != AccountType.BACKUP:
        # 获取所有账号
        all_accounts = cookie_manager.get_all_mobile_accounts()
        # 检查是否存在相同类型的账号(除了当前账号)
        for acc in all_accounts:
            if acc.account != account.account and acc.account_type == request.account_type:
                raise HTTPException(
                    status_code=400, 
                    detail=f"已存在一个{ACCOUNT_TYPE_NAMES.get(request.account_type, request.account_type)}，请先将其修改为其他类型"
                )
    
    # 设置账户类型
    account.account_type = request.account_type
    cookie_manager.save_pool()  # 持久化更改
    
    return {"message": f"已将账户 '{account.name}' 设置为 {request.account_type} 类型", "account_id": account_id, "account_type": request.account_type} 