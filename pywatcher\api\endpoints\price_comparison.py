from fastapi import APIRouter, Depends, Query, HTTPException, BackgroundTasks
from typing import List, Dict, Any
import logging
import aiohttp
from pydantic import BaseModel

from pywatcher.models.price_comparison import (
    PriceSearchRequest, PriceSearchResult, GamePriceCreate, FhyxGameData
)
from pywatcher.crud.price_comparison_manager import get_price_manager
from pywatcher.api.dependencies import get_client_session

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/price-comparison",
    tags=["Price Comparison"]
)

class UpdateFhyxRequest(BaseModel):
    """更新fhyx数据的请求模型"""
    pass

@router.get("/search", response_model=List[PriceSearchResult])
async def search_prices(
    q: str = Query(..., min_length=1, description="搜索关键词"),
    price_manager = Depends(get_price_manager)
):
    """搜索游戏价格"""
    try:
        results = await price_manager.search_games(q)
        return results
    except Exception as e:
        logger.error(f"搜索价格时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/games", response_model=List[PriceSearchResult])
async def get_all_games(
    limit: int = Query(100, ge=1, le=1000, description="返回结果数量限制"),
    price_manager = Depends(get_price_manager)
):
    """获取所有游戏价格数据"""
    try:
        results = await price_manager.get_all_games(limit)
        return results
    except Exception as e:
        logger.error(f"获取游戏列表时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取游戏列表失败: {str(e)}")

@router.get("/stats", response_model=Dict[str, Any])
async def get_stats(price_manager = Depends(get_price_manager)):
    """获取价格数据库统计信息"""
    try:
        stats = await price_manager.get_stats()
        return stats
    except Exception as e:
        logger.error(f"获取统计信息时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

async def fetch_fhyx_data_from_api(session: aiohttp.ClientSession, page: int = 1) -> List[dict]:
    """从fhyx API获取数据"""
    url = "https://api.lmaoyx.com/fhyx-zzdz"
    
    # 完全模拟Postman的请求方式
    import json
    payload = json.dumps({"page": page})
    
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }
    
    try:
        # 使用data参数发送原始JSON字符串，而不是json参数
        async with session.post(url, data=payload, headers=headers, ssl=False) as response:
            logger.info(f"请求URL: {url}")
            logger.info(f"请求payload: {payload}")
            logger.info(f"请求headers: {headers}")
            logger.info(f"API响应状态码: {response.status}")
            logger.info(f"API响应Content-Type: {response.headers.get('content-type', 'unknown')}")
            logger.info(f"所有响应头: {dict(response.headers)}")
            
            if response.status == 200:
                # 尝试直接解析JSON，不管content-type
                try:
                    result = await response.json(content_type=None)  # 忽略content-type检查
                    logger.info(f"成功解析JSON，数据类型: {type(result)}")
                    
                    # 根据API响应结构提取数据
                    if isinstance(result, dict) and "data" in result:
                        games_data = result["data"]
                        logger.info(f"从data字段提取到 {len(games_data)} 条游戏数据")
                        return games_data
                    elif isinstance(result, list):
                        logger.info(f"直接返回列表数据: {len(result)} 条")
                        return result
                    else:
                        logger.warning(f"意外的API响应格式: {type(result)}, 内容: {result}")
                        return []
                        
                except Exception as json_error:
                    logger.error(f"JSON解析失败: {json_error}")
                    # 获取原始文本内容用于调试
                    text_content = await response.text()
                    logger.error(f"原始响应内容: {text_content}")
                    return []
            else:
                error_text = await response.text()
                logger.error(f"API请求失败，状态码: {response.status}, 响应: {error_text}")
                return []
                
    except Exception as e:
        logger.error(f"获取fhyx数据时发生错误: {e}")
        return []

async def update_fhyx_data_background(session: aiohttp.ClientSession, price_manager):
    """后台任务：更新fhyx数据"""
    import asyncio
    
    logger.info("开始更新fhyx数据...")
    
    all_games = []
    page = 1
    max_pages = 200  # 提高最大页数限制，防止无限循环
    consecutive_empty_pages = 0  # 连续空页面计数
    max_consecutive_empty = 3    # 最多允许3个连续空页面
    
    while page <= max_pages:
        logger.info(f"正在获取第 {page} 页数据...")
        games_data = await fetch_fhyx_data_from_api(session, page)
        
        if not games_data:
            consecutive_empty_pages += 1
            logger.info(f"第 {page} 页没有数据，连续空页面数: {consecutive_empty_pages}")
            
            # 如果连续多页都没有数据，则停止获取
            if consecutive_empty_pages >= max_consecutive_empty:
                logger.info(f"连续 {max_consecutive_empty} 页没有数据，停止获取")
                break
        else:
            # 重置连续空页面计数
            consecutive_empty_pages = 0
            all_games.extend(games_data)
            logger.info(f"第 {page} 页获取到 {len(games_data)} 条数据，累计: {len(all_games)} 条")
        
        page += 1
        
        # 添加1秒间隔，避免请求过于频繁
        if page <= max_pages:  # 最后一页不需要等待
            logger.info(f"等待1秒后继续获取第 {page} 页...")
            await asyncio.sleep(1)
    
    if all_games:
        # 批量插入数据库
        logger.info(f"开始批量插入 {len(all_games)} 条游戏数据到数据库...")
        inserted_count = await price_manager.bulk_insert_fhyx_games(all_games)
        logger.info(f"fhyx数据更新完成！总共获取 {page-1} 页，处理 {len(all_games)} 条数据，成功插入 {inserted_count} 条")
    else:
        logger.warning("未获取到任何fhyx数据")

@router.post("/update-fhyx", response_model=Dict[str, Any])
async def update_fhyx_data(
    background_tasks: BackgroundTasks,
    session: aiohttp.ClientSession = Depends(get_client_session),
    price_manager = Depends(get_price_manager)
):
    """更新fhyx游戏价格数据"""
    try:
        # 添加后台任务
        background_tasks.add_task(update_fhyx_data_background, session, price_manager)
        
        return {
            "success": True,
            "message": "fhyx数据更新任务已启动，请稍后查看更新结果"
        }
    except Exception as e:
        logger.error(f"启动fhyx数据更新任务时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"启动更新任务失败: {str(e)}")

@router.post("/add-price", response_model=Dict[str, Any])
async def add_game_price(
    game_price: GamePriceCreate,
    price_manager = Depends(get_price_manager)
):
    """添加游戏价格数据"""
    try:
        result = await price_manager.add_game_price(game_price)
        return {
            "success": True,
            "message": "游戏价格添加成功",
            "data": result.model_dump()
        }
    except Exception as e:
        logger.error(f"添加游戏价格时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"添加游戏价格失败: {str(e)}")

# 为了兼容现有的游戏价格缓存，添加从旧数据库迁移数据的接口
@router.post("/migrate-py-data", response_model=Dict[str, Any])
async def migrate_py_data(price_manager = Depends(get_price_manager)):
    """从现有的game_prices.db迁移py来源的数据"""
    try:
        import sqlite3
        import os
        from pathlib import Path
        
        # 获取旧数据库路径
        base_dir = Path(__file__).resolve().parent.parent.parent
        old_db_path = base_dir / "data" / "game_prices.db"
        
        if not old_db_path.exists():
            return {
                "success": False,
                "message": "未找到旧的游戏价格数据库"
            }
        
        # 读取旧数据库中的数据
        conn = sqlite3.connect(str(old_db_path))
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT gameName, keyPrice FROM game_prices")
        rows = cursor.fetchall()
        conn.close()
        
        # 转换并插入新数据库
        py_games = []
        for row in rows:
            py_games.append({
                'name': row['gameName'],
                'price': str(row['keyPrice'])
            })
        
        if py_games:
            # 先删除旧的py数据
            import aiosqlite
            async with aiosqlite.connect(price_manager.db_path) as db:
                await db.execute("DELETE FROM game_prices WHERE source = 'py'")
                await db.commit()
            
            # 批量插入py数据
            from datetime import datetime
            async with aiosqlite.connect(price_manager.db_path) as db:
                current_time = datetime.now()
                insert_data = []
                for game in py_games:
                    try:
                        price = float(game['price'])
                        insert_data.append((game['name'], price, 'py', current_time, current_time))
                    except ValueError:
                        continue
                
                if insert_data:
                    await db.executemany(
                        "INSERT INTO game_prices (name, price, source, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
                        insert_data
                    )
                    await db.commit()
            
            return {
                "success": True,
                "message": f"成功迁移 {len(insert_data)} 条py来源的游戏价格数据"
            }
        else:
            return {
                "success": False,
                "message": "旧数据库中没有找到有效数据"
            }
            
    except Exception as e:
        logger.error(f"迁移py数据时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"数据迁移失败: {str(e)}")

