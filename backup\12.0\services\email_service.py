import asyncio
import logging
import os
import aiosmtplib
import json
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Optional

# It's better to get the logger from the calling module,
# but for simplicity let's create one here.
logger = logging.getLogger(__name__)

async def send_email(
    subject: str,
    body: str,
    recipient: Optional[str] = None,
    payment_url: Optional[str] = None,
    timeout: int = 10
) -> bool:
    """
    Asynchronously sends an email using aiosmtplib.

    Args:
        subject: The subject of the email.
        body: The plain text body of the email.
        recipient: Comma-separated string of recipient email addresses.
                   If None, uses RECEIVER_EMAIL from .env.
        payment_url: Optional URL to include as a button in the HTML body.
        timeout: Socket timeout in seconds for the SMTP connection.

    Returns:
        True if the email was sent successfully, False otherwise.
    """
    sender = os.getenv('SENDER_EMAIL')
    default_receiver = os.getenv('RECEIVER_EMAIL', '')
    # Handle multiple recipients, prioritize the 'recipient' parameter
    final_receivers = recipient if recipient else default_receiver
    receiver_list = [r.strip() for r in final_receivers.split(',')] if final_receivers else []

    if not receiver_list:
        logger.error("Recipient list is empty. Check RECEIVER_EMAIL environment variable or provide a valid recipient.")
        return False

    password = os.getenv('EMAIL_PASSWORD')
    smtp_server_addr = os.getenv('SMTP_SERVER', 'smtp.qq.com')
    smtp_port_val = int(os.getenv('SMTP_PORT', 465))
    use_tls = smtp_port_val == 465

    if not all([sender, receiver_list, password]):
        logger.error("Email sending configuration is incomplete (SENDER_EMAIL, RECEIVER_EMAIL, EMAIL_PASSWORD environment variables).")
        return False

    msg = MIMEMultipart('alternative')
    msg['From'] = sender
    msg['To'] = ', '.join(receiver_list)
    msg['Subject'] = subject

    # Attach plain text part
    msg.attach(MIMEText(body, 'plain', 'utf-8'))

    # 从支付链接响应中提取form字段的URL（如果是JSON格式）
    alipay_link = payment_url
    if payment_url:
        # 尝试解析为JSON（如果是完整的API响应）
        if payment_url.startswith("{"):
            try:
                payment_data = json.loads(payment_url)
                if "result" in payment_data and "form" in payment_data["result"]:
                    # 从form字段提取支付链接
                    alipay_link = payment_data["result"]["form"]
                    # 更新邮件正文中的链接信息
                    body = body.replace(payment_url, alipay_link)
                    msg.replace_header("Text", MIMEText(body, 'plain', 'utf-8'))
                    logger.info(f"从JSON响应中提取支付链接: {alipay_link}")
            except Exception as e:
                logger.error(f"解析支付响应数据失败: {e}")
                # 解析失败时保持原始链接不变
        
        # 确保链接非空
        if not alipay_link:
            alipay_link = payment_url

    # If payment_url is provided, create and attach an HTML part with a button
    if payment_url:
        # Check if the link is already an Alipay Deep Link
        if alipay_link and alipay_link.startswith("alipays://"):
            final_link = alipay_link
        else:
            # For a regular URL, assume it's a payment page and wrap it in a standard deep link format
            final_link = f"alipays://platformapi/startapp?appId=60000157&orderStr={alipay_link}"

        html_content = f"""
        <html>
        <head></head>
        <body>
            <p>{body.replace('\n', '<br>')}</p>
            <p>
                <a href="{final_link}"
                   style="display: inline-block; padding: 12px 24px; background: #1677FF; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">
                  点我直达支付宝
                </a>
            </p>
            <p>如果按钮无法工作，请复制链接: {alipay_link}</p>
        </body>
        </html>
        """
        msg.attach(MIMEText(html_content, 'html', 'utf-8'))

    try:
        smtp_client = aiosmtplib.SMTP(
            hostname=smtp_server_addr,
            port=smtp_port_val,
            use_tls=use_tls,
            timeout=timeout
        )
        async with smtp_client:
            # For non-SSL/TLS connections on other ports, you might need to call start_tls explicitly
            # await smtp_client.start_tls()
            await smtp_client.login(sender, password)
            await smtp_client.send_message(msg)
        
        logger.info(f"Email '{subject}' successfully sent to {', '.join(receiver_list)}.")
        return True
    except aiosmtplib.SMTPException as e:
        logger.error(f"aiosmtplib.SMTPException sending email: {e.code} {e.message}", exc_info=True)
        return False
    except Exception as e:
        logger.error(f"Unexpected error sending email: {e}", exc_info=True)
        return False 