import qrcode
import argparse
import os
import time
from urllib.parse import quote

def generate_alipay_qr_code(payment_link, output_dir="output", filename=None):
    """
    生成支付宝支付二维码
    :param payment_link: 支付宝支付链接
    :param output_dir: 输出目录
    :param filename: 文件名（不包含扩展名）
    :return: 生成的二维码文件路径
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成文件名
    if not filename:
        timestamp = time.strftime("%Y%m%d%H%M%S")
        filename = f"alipay_payment_{timestamp}"
    
    file_path = os.path.join(output_dir, f"{filename}.png")
    
    # 创建二维码
    qr = qrcode.QRCode(
        error_correction=qrcode.constants.ERROR_CORRECT_H,  # 高容错率
    )
    
    # 添加支付链接
    qr.add_data(payment_link)
    qr.make(fit=True)
    
    # 生成并保存二维码
    img = qr.make_image(fill_color="black", back_color="white")
    img.save(file_path)
    
    return file_path

if __name__ == "__main__":
    # 设置命令行参数
    parser = argparse.ArgumentParser(description='支付宝支付二维码生成器')
    parser.add_argument('link', type=str, help='支付宝支付链接')
    parser.add_argument('-o', '--output', type=str, default="output", help='输出目录 (默认: output)')
    parser.add_argument('-f', '--filename', type=str, help='自定义文件名 (不含扩展名)')
    
    args = parser.parse_args()
    
    # 生成二维码
    try:
        qr_path = generate_alipay_qr_code(
            payment_link=args.link,
            output_dir=args.output,
            filename=args.filename
        )
        
        print(f"二维码已生成: {qr_path}")
        print(f"文件大小: {os.path.getsize(qr_path)//1024} KB")
    except Exception as e:
        print(f"生成二维码失败: {str(e)}")
        