"""
Web账户管理器
用于管理Web端登录的账户信息
"""

import json
import os
import hashlib
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime

from pywatcher.models.account import WebAccount


class WebAccountManager:
    """Web账户管理器"""
    
    def __init__(self, data_file: str = "data/web_accounts.json"):
        self.data_file = data_file
        self._ensure_data_file()
    
    def _ensure_data_file(self):
        """确保数据文件存在"""
        try:
            data_dir = os.path.dirname(self.data_file)
            if data_dir:  # 如果有目录部分
                os.makedirs(data_dir, exist_ok=True)
            if not os.path.exists(self.data_file):
                with open(self.data_file, 'w', encoding='utf-8') as f:
                    json.dump({}, f)
        except Exception as e:
            print(f"Error creating data file: {e}")
            # 如果无法创建文件，使用内存存储
            self.data_file = None
    
    def _load_accounts(self) -> Dict[str, dict]:
        """加载账户数据"""
        if not self.data_file:
            return {}
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def _save_accounts(self, accounts: Dict[str, dict]):
        """保存账户数据"""
        if not self.data_file:
            return
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(accounts, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            print(f"Error saving accounts: {e}")
    
    def _hash_password(self, password: str) -> str:
        """对密码进行哈希处理"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def add_account(self, username: str, password: str, access_token: str) -> WebAccount:
        """添加Web账户"""
        accounts = self._load_accounts()
        
        # 检查是否已存在相同用户名的账户
        for account_data in accounts.values():
            if account_data.get('username') == username:
                # 更新现有账户
                account_data.update({
                    'password': self._hash_password(password),
                    'access_token': access_token,
                    'is_active': True,
                    'last_used': datetime.now().isoformat()
                })
                self._save_accounts(accounts)
                return WebAccount(**account_data)
        
        # 创建新账户
        account_id = str(uuid.uuid4())
        account_data = {
            'id': account_id,
            'username': username,
            'password': self._hash_password(password),
            'access_token': access_token,
            'account_type': 'web_cdk_query',
            'identifier': username,
            'is_active': True,
            'created_at': datetime.now().isoformat(),
            'last_used': datetime.now().isoformat()
        }
        
        accounts[account_id] = account_data
        self._save_accounts(accounts)
        
        return WebAccount(**account_data)
    
    def get_account_by_id(self, account_id: str) -> Optional[WebAccount]:
        """根据ID获取账户"""
        accounts = self._load_accounts()
        account_data = accounts.get(account_id)
        if account_data:
            # 处理datetime字段
            if isinstance(account_data.get('created_at'), str):
                account_data['created_at'] = datetime.fromisoformat(account_data['created_at'])
            if isinstance(account_data.get('last_used'), str) and account_data['last_used']:
                account_data['last_used'] = datetime.fromisoformat(account_data['last_used'])
            return WebAccount(**account_data)
        return None
    
    def get_account_by_username(self, username: str) -> Optional[WebAccount]:
        """根据用户名获取账户"""
        accounts = self._load_accounts()
        for account_data in accounts.values():
            if account_data.get('username') == username:
                # 处理datetime字段
                if isinstance(account_data.get('created_at'), str):
                    account_data['created_at'] = datetime.fromisoformat(account_data['created_at'])
                if isinstance(account_data.get('last_used'), str) and account_data['last_used']:
                    account_data['last_used'] = datetime.fromisoformat(account_data['last_used'])
                return WebAccount(**account_data)
        return None
    
    def get_all_accounts(self) -> List[WebAccount]:
        """获取所有账户"""
        accounts = self._load_accounts()
        result = []
        for account_data in accounts.values():
            # 处理datetime字段
            if isinstance(account_data.get('created_at'), str):
                account_data['created_at'] = datetime.fromisoformat(account_data['created_at'])
            if isinstance(account_data.get('last_used'), str) and account_data['last_used']:
                account_data['last_used'] = datetime.fromisoformat(account_data['last_used'])
            result.append(WebAccount(**account_data))
        return result
    
    def get_active_accounts(self) -> List[WebAccount]:
        """获取所有活跃账户"""
        accounts = self.get_all_accounts()
        return [account for account in accounts if account.is_active]
    
    def update_account_status(self, account_id: str, is_active: bool) -> bool:
        """更新账户状态"""
        accounts = self._load_accounts()
        if account_id in accounts:
            accounts[account_id]['is_active'] = is_active
            if is_active:
                accounts[account_id]['last_used'] = datetime.now().isoformat()
            self._save_accounts(accounts)
            return True
        return False
    
    def update_access_token(self, account_id: str, access_token: str) -> bool:
        """更新访问令牌"""
        accounts = self._load_accounts()
        if account_id in accounts:
            accounts[account_id]['access_token'] = access_token
            accounts[account_id]['last_used'] = datetime.now().isoformat()
            self._save_accounts(accounts)
            return True
        return False
    
    def delete_account(self, account_id: str) -> bool:
        """删除账户"""
        accounts = self._load_accounts()
        if account_id in accounts:
            del accounts[account_id]
            self._save_accounts(accounts)
            return True
        return False
    
    def get_available_account(self) -> Optional[WebAccount]:
        """获取一个可用的账户（用于CDK查询）"""
        active_accounts = self.get_active_accounts()
        if active_accounts:
            # 返回最近使用的账户
            return sorted(active_accounts, key=lambda x: x.last_used or x.created_at, reverse=True)[0]
        return None
    
    def verify_password(self, username: str, password: str) -> bool:
        """验证密码"""
        account = self.get_account_by_username(username)
        if account:
            return account.password == self._hash_password(password)
        return False


# 全局Web账户管理器实例
_web_account_manager = None

def get_web_account_manager() -> WebAccountManager:
    """获取Web账户管理器实例"""
    global _web_account_manager
    if _web_account_manager is None:
        _web_account_manager = WebAccountManager()
    return _web_account_manager