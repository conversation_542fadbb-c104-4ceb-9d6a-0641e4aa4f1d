/**
 * Steam Key激活页面功能
 */

class SteamKeyRedeemer {
    constructor() {
        this.settings = this.loadSettings();
        this.keys = [];
        this.results = [];
        this.unusedKeys = [];
        this.currentIndex = 0;
        this.sessionID = '';
        this.steamLoggedIn = false;
        this.loginCheckPromise = null;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSettings();
        this.updateUI();
        this.checkSteamLogin();
    }

    bindEvents() {
        // 激活方式切换
        document.getElementById('activation-method').addEventListener('change', (e) => {
            this.settings.method = e.target.value;
            this.saveSettings();
            this.updateUI();
        });

        // ASF配置
        ['asf-protocol', 'asf-host', 'asf-port', 'asf-password', 'asf-bot'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    const key = id.replace('asf-', '');
                    this.settings.asf[key] = e.target.value;
                    this.saveSettings();
                });
            }
        });

        // 按钮事件
        document.getElementById('extract-keys-btn').addEventListener('click', () => this.extractKeys());
        document.getElementById('activate-keys-btn').addEventListener('click', () => this.activateKeys());
        document.getElementById('test-asf-btn').addEventListener('click', () => this.testASFConnection());
        document.getElementById('clear-results-btn').addEventListener('click', () => this.clearResults());
        document.getElementById('export-unused-btn').addEventListener('click', () => this.exportUnusedKeys());

        // ASF指令按钮
        document.getElementById('execute-asf-btn').addEventListener('click', () => this.executeASFCommand());
        document.getElementById('asf-status-btn').addEventListener('click', () => this.executeASFCommand('!status'));
        document.getElementById('asf-2fa-btn').addEventListener('click', () => this.executeASFCommand('!2fa'));
        document.getElementById('asf-pause-btn').addEventListener('click', () => this.executeASFCommand('!pause'));
        document.getElementById('asf-resume-btn').addEventListener('click', () => this.executeASFCommand('!resume'));

        // Key输入监听
        document.getElementById('steam-keys').addEventListener('input', () => {
            this.extractKeys();
        });

        // Enter键执行ASF指令
        document.getElementById('asf-command').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.executeASFCommand();
            }
        });

        // Steam登录按钮
        document.getElementById('steam-login-btn').addEventListener('click', () => this.handleSteamLogin());
        document.getElementById('check-login-btn').addEventListener('click', () => this.checkSteamLogin(true));

        // 保存粘贴的Steam Cookie（可选）
        const saveCookieBtn = document.getElementById('save-steam-cookie-btn');
        if (saveCookieBtn) {
            saveCookieBtn.addEventListener('click', async () => {
                const raw = (document.getElementById('steam-cookie-input').value || '').trim();
                try {
                    const resp = await fetch('/api/steam/save-cookies', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        credentials: 'include',
                        body: JSON.stringify({ cookies: raw })
                    });
                    if (resp.ok) {
                        this.showStatus('已保存到本站 HttpOnly Cookie', 'success');
                        // 立即刷新一次登录状态
                        setTimeout(() => this.checkSteamLogin(true), 500);
                    } else {
                        this.showStatus('保存失败，请检查格式', 'error');
                    }
                } catch (e) {
                    this.showStatus('网络错误，保存失败', 'error');
                }
            });
        }
    }

    loadSettings() {
        const defaultSettings = {
            method: 'web',
            asf: {
                protocol: 'http',
                host: '127.0.0.1',
                port: '1242',
                password: '',
                bot: ''
            }
        };

        const saved = localStorage.getItem('redeem-settings');
        this.settings = saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
        
        return this.settings;
    }

    saveSettings() {
        localStorage.setItem('redeem-settings', JSON.stringify(this.settings));
    }

    updateUI() {
        // 更新激活方式选择
        document.getElementById('activation-method').value = this.settings.method;

        // 显示/隐藏ASF相关元素
        const isASF = this.settings.method === 'asf';
        const isWeb = this.settings.method === 'web';
        
        document.getElementById('asf-settings').style.display = isASF ? 'block' : 'none';
        document.getElementById('asf-config').style.display = isASF ? 'block' : 'none';
        document.getElementById('test-asf-btn').style.display = isASF ? 'inline-block' : 'none';
        document.getElementById('asf-commands-section').style.display = isASF ? 'block' : 'none';
        
        // 显示/隐藏Steam登录状态区域
        const steamLoginSection = document.getElementById('steam-login-section');
        if (steamLoginSection) {
            steamLoginSection.style.display = isWeb ? 'block' : 'none';
        }

        // 更新ASF配置
        if (isASF) {
            document.getElementById('asf-protocol').value = this.settings.asf.protocol;
            document.getElementById('asf-host').value = this.settings.asf.host;
            document.getElementById('asf-port').value = this.settings.asf.port;
            document.getElementById('asf-password').value = this.settings.asf.password;
            document.getElementById('asf-bot').value = this.settings.asf.bot;
        }
    }

    extractKeys() {
        const text = document.getElementById('steam-keys').value;
        const keyRegex = /([0-9A-Z]{5}-){2,4}[0-9A-Z]{5}/gi;
        const matches = text.match(keyRegex) || [];
        
        // 去重并转大写
        this.keys = [...new Set(matches.map(key => key.toUpperCase()))];
        
        // 更新计数
        document.getElementById('key-count').textContent = this.keys.length;
        document.getElementById('activate-keys-btn').disabled = this.keys.length === 0;
        
        this.showStatus(`提取到 ${this.keys.length} 个有效的Steam Key`, 'info');
    }

    async activateKeys() {
        if (this.keys.length === 0) {
            this.showStatus('请先输入Steam Key', 'error');
            return;
        }

        this.clearResults();
        this.currentIndex = 0;

        // 显示结果区域
        document.getElementById('activation-results').style.display = 'block';
        document.getElementById('activation-stats').style.display = 'block';

        // 根据激活方式调用不同的方法
        switch (this.settings.method) {
            case 'asf':
                await this.activateWithASF();
                break;
            case 'newTab':
                this.activateWithNewTab();
                break;
            default:
                await this.activateWithWeb();
                break;
        }
    }

    async activateWithASF() {
        const command = `!redeem ${this.settings.asf.bot ? this.settings.asf.bot + ' ' : ''}${this.keys.join(',')}`;
        
        try {
            const result = await this.sendASFCommand(command);
            this.showASFResult(result);
            
            // 解析ASF结果并更新表格
            this.parseASFResult(result);
            
        } catch (error) {
            this.showStatus(`ASF激活失败: ${error.message}`, 'error');
        }
    }

    activateWithNewTab() {
        const url = `https://store.steampowered.com/account/registerkey?key=${this.keys.join(',')}`;
        window.open(url, '_blank');
        this.showStatus('已在新标签页打开Steam激活页面', 'success');
    }

    async activateWithWeb() {
        // 首先检查Steam登录状态
        if (!this.steamLoggedIn) {
            await this.checkSteamLogin();
        }

        if (!this.steamLoggedIn) {
            this.showLoginRequired();
            return;
        }

        // 获取sessionID
        if (!this.sessionID) {
            try {
                await this.getSessionID();
            } catch (error) {
                this.showStatus('无法获取Steam会话，请重新登录', 'error');
                this.handleLoginExpired();
                return;
            }
        }

        // 逐个激活Key（避免频率限制）
        for (let i = 0; i < this.keys.length; i++) {
            const key = this.keys[i];
            this.addResultRow(i + 1, key, '激活中...', '', '');
            
            try {
                const result = await this.redeemKey(key);
                this.updateResultRow(i + 1, key, result);
                this.updateStats();
                
                // 延迟避免频率限制
                if (i < this.keys.length - 1) {
                    await this.delay(2000);
                }
            } catch (error) {
                this.updateResultRow(i + 1, key, {
                    success: false,
                    message: '网络错误',
                    detail: error.message
                });
                this.updateStats();
            }
        }

        this.updateUnusedKeys();
        this.showStatus('激活完成', 'success');
    }

    // Steam登录状态检查
    async checkSteamLogin(forceCheck = false) {
        if (this.loginCheckPromise && !forceCheck) {
            return this.loginCheckPromise;
        }

        this.loginCheckPromise = this._checkSteamLoginStatus();
        const result = await this.loginCheckPromise;
        
        this.updateLoginStatus(result);
        return result;
    }

    async _checkSteamLoginStatus() {
        try {
            // 通过后端代理检查Steam登录状态
            const response = await fetch('/api/steam/check-login', {
                method: 'GET',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.logged_in) {
                    this.steamLoggedIn = true;
                    this.sessionID = data.session_id;
                    return { logged_in: true, username: data.username };
                }
            }

            // 如果后端检查失败，尝试直接检查
            return await this._directSteamLoginCheck();

        } catch (error) {
            console.warn('后端登录检查失败，尝试直接检查:', error);
            return await this._directSteamLoginCheck();
        }
    }

    async _directSteamLoginCheck() {
        try {
            const response = await fetch('https://store.steampowered.com/account/', {
                method: 'GET',
                credentials: 'include',
                mode: 'cors'
            });

            if (response.ok) {
                const html = await response.text();
                
                // 检查是否包含登录用户信息
                const isLoggedIn = html.includes('account_pulldown') || 
                                 html.includes('playerAvatar') ||
                                 html.includes('persona_name');

                if (isLoggedIn) {
                    this.steamLoggedIn = true;
                    
                    // 提取sessionID
                    const sessionMatch = html.match(/g_sessionID = "(.+?)";/);
                    if (sessionMatch) {
                        this.sessionID = sessionMatch[1];
                    }

                    // 提取用户名
                    const usernameMatch = html.match(/data-miniprofile="(\d+)"/);
                    const username = usernameMatch ? `用户ID: ${usernameMatch[1]}` : '已登录';

                    return { logged_in: true, username };
                }
            }

            this.steamLoggedIn = false;
            this.sessionID = '';
            return { logged_in: false };

        } catch (error) {
            console.error('Steam登录检查失败:', error);
            this.steamLoggedIn = false;
            this.sessionID = '';
            return { logged_in: false, error: error.message };
        }
    }

    updateLoginStatus(status) {
        const statusElement = document.getElementById('steam-login-status');
        const loginBtn = document.getElementById('steam-login-btn');
        const checkBtn = document.getElementById('check-login-btn');

        if (status.logged_in) {
            statusElement.innerHTML = `
                <span class="status-success">✅ 已登录 Steam</span>
                <small>${status.username || ''}</small>
            `;
            loginBtn.style.display = 'none';
            checkBtn.textContent = '刷新状态';
        } else {
            statusElement.innerHTML = `
                <span class="status-error">❌ 未登录 Steam</span>
                <small>需要登录Steam才能使用Web激活功能</small>
            `;
            loginBtn.style.display = 'inline-block';
            checkBtn.textContent = '检查登录';
        }
    }

    handleSteamLogin() {
        const loginWindow = window.open(
            'https://store.steampowered.com/login/',
            'steam-login',
            'width=800,height=600,scrollbars=yes,resizable=yes'
        );

        // 监听登录窗口关闭
        const checkClosed = setInterval(() => {
            if (loginWindow.closed) {
                clearInterval(checkClosed);
                // 延迟检查登录状态，给Steam时间设置cookie
                setTimeout(() => {
                    this.checkSteamLogin(true);
                }, 2000);
            }
        }, 1000);

        this.showStatus('请在弹出窗口中完成Steam登录', 'info');
    }

    showLoginRequired() {
        const modal = this.createModal('需要Steam登录', `
            <div class="login-required-content">
                <p>Web激活功能需要您先登录Steam账户。</p>
                <p>请选择以下方式之一：</p>
                <div class="login-options">
                    <button id="modal-steam-login" class="primary">打开Steam登录页面</button>
                    <button id="modal-switch-asf" class="outline">切换到ASF激活</button>
                    <button id="modal-switch-newtab" class="outline">切换到新标签页激活</button>
                </div>
            </div>
        `);

        modal.querySelector('#modal-steam-login').onclick = () => {
            this.handleSteamLogin();
            this.closeModal(modal);
        };

        modal.querySelector('#modal-switch-asf').onclick = () => {
            document.getElementById('activation-method').value = 'asf';
            this.settings.method = 'asf';
            this.saveSettings();
            this.updateUI();
            this.closeModal(modal);
        };

        modal.querySelector('#modal-switch-newtab').onclick = () => {
            document.getElementById('activation-method').value = 'newTab';
            this.settings.method = 'newTab';
            this.saveSettings();
            this.updateUI();
            this.closeModal(modal);
        };
    }

    handleLoginExpired() {
        this.steamLoggedIn = false;
        this.sessionID = '';
        this.updateLoginStatus({ logged_in: false });
        
        const modal = this.createModal('登录已过期', `
            <div class="login-expired-content">
                <p>您的Steam登录会话已过期，请重新登录。</p>
                <div class="login-options">
                    <button id="modal-relogin" class="primary">重新登录Steam</button>
                    <button id="modal-cancel" class="outline">取消</button>
                </div>
            </div>
        `);

        modal.querySelector('#modal-relogin').onclick = () => {
            this.handleSteamLogin();
            this.closeModal(modal);
        };

        modal.querySelector('#modal-cancel').onclick = () => {
            this.closeModal(modal);
        };
    }

    async getSessionID() {
        if (this.sessionID) {
            return this.sessionID;
        }

        try {
            // 优先通过后端获取sessionID
            const response = await fetch('/api/steam/session', {
                method: 'GET',
                credentials: 'include'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.session_id) {
                    this.sessionID = data.session_id;
                    return this.sessionID;
                }
            }
        } catch (error) {
            console.warn('后端获取sessionID失败，尝试直接获取:', error);
        }

        // 降级到直接获取
        return this._getSessionIDDirect();
    }

    async _getSessionIDDirect() {
        const response = await fetch('https://store.steampowered.com/account/registerkey', {
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error('无法访问Steam激活页面');
        }

        const html = await response.text();
        
        // 检查是否被重定向到登录页面
        if (html.includes('login') && !html.includes('g_sessionID')) {
            throw new Error('需要重新登录Steam');
        }

        const match = html.match(/g_sessionID = "(.+?)";/);
        if (match) {
            this.sessionID = match[1];
            return this.sessionID;
        }

        throw new Error('无法获取sessionID');
    }

    async redeemKey(key) {
        try {
            // 优先使用后端代理
            const response = await fetch('/api/steam/redeem', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                credentials: 'include',
                body: JSON.stringify({
                    product_key: key,
                    sessionid: this.sessionID
                })
            });

            if (response.ok) {
                const data = await response.json();
                return this.parseRedeemResult(data);
            }

            // 后端代理失败，降级到直接请求
            console.warn('后端代理失败，使用直接请求');
            return await this._redeemKeyDirect(key);

        } catch (error) {
            console.warn('后端代理出错，使用直接请求:', error);
            return await this._redeemKeyDirect(key);
        }
    }

    async _redeemKeyDirect(key) {
        const formData = new FormData();
        formData.append('product_key', key);
        formData.append('sessionid', this.sessionID);

        const response = await fetch('https://store.steampowered.com/account/ajaxregisterkey/', {
            method: 'POST',
            headers: {
                'Origin': 'https://store.steampowered.com',
                'Referer': 'https://store.steampowered.com/account/registerkey'
            },
            credentials: 'include',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        return this.parseRedeemResult(data);
    }

    parseRedeemResult(data) {
        const FAILURE_DETAILS = {
            14: "无效激活码",
            15: "重复激活", 
            53: "次数上限",
            13: "地区限制",
            9: "已拥有",
            24: "缺少主游戏",
            36: "需要PS3?",
            50: "这是充值码"
        };

        if (data.success === 1 && data.purchase_receipt_info?.line_items[0]) {
            const item = data.purchase_receipt_info.line_items[0];
            return {
                success: true,
                message: '激活成功',
                detail: item.line_item_description,
                packageId: item.packageid
            };
        } else if (data.purchase_result_details !== undefined) {
            const failureReason = FAILURE_DETAILS[data.purchase_result_details] || '其他错误';
            const item = data.purchase_receipt_info?.line_items[0];
            
            return {
                success: false,
                message: '激活失败',
                detail: failureReason,
                packageId: item?.packageid || 0,
                game: item?.line_item_description || '无'
            };
        }

        return {
            success: false,
            message: '激活失败',
            detail: '未知错误'
        };
    }

    async sendASFCommand(command) {
        const { protocol, host, port, password } = this.settings.asf;
        const url = `${protocol}://${host}:${port}/Api/Command`;
        
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authentication': password,
                'Accept': 'application/json'
            },
            body: JSON.stringify({ Command: command })
        });

        if (!response.ok) {
            throw new Error(`ASF请求失败: ${response.status}`);
        }

        const data = await response.json();
        
        if (!data.Success) {
            throw new Error(data.Message || '未知ASF错误');
        }

        return data.Result || data.Message || '执行成功';
    }

    async testASFConnection() {
        try {
            this.showStatus('正在测试ASF连接...', 'info');
            const result = await this.sendASFCommand('!status');
            this.showStatus('ASF连接成功!', 'success');
            this.showASFResult(result);
        } catch (error) {
            this.showStatus(`ASF连接失败: ${error.message}`, 'error');
        }
    }

    async executeASFCommand(command = null) {
        const cmd = command || document.getElementById('asf-command').value.trim();
        
        if (!cmd) {
            this.showStatus('请输入ASF指令', 'error');
            return;
        }

        try {
            this.showStatus('正在执行ASF指令...', 'info');
            const result = await this.sendASFCommand(cmd);
            this.showASFResult(result);
            this.showStatus('ASF指令执行完成', 'success');
            
            // 清空输入框
            if (!command) {
                document.getElementById('asf-command').value = '';
            }
        } catch (error) {
            this.showStatus(`ASF指令执行失败: ${error.message}`, 'error');
        }
    }

    showASFResult(result) {
        const outputDiv = document.getElementById('asf-output');
        const resultPre = document.getElementById('asf-result');
        
        resultPre.textContent = result;
        outputDiv.style.display = 'block';
    }

    parseASFResult(result) {
        // 解析ASF返回结果，提取激活信息
        const lines = result.split('\n');
        let index = 1;
        
        for (const line of lines) {
            if (line.includes('->')) {
                const parts = line.split('->');
                if (parts.length >= 2) {
                    const key = parts[0].trim();
                    const status = parts[1].trim();
                    
                    const success = status.includes('OK') || status.includes('成功');
                    this.addResultRow(index++, key, success ? '成功' : '失败', status, '');
                }
            }
        }
        
        this.updateStats();
        this.updateUnusedKeys();
    }

    addResultRow(index, key, status, detail, game) {
        const tbody = document.getElementById('activation-tbody');
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${index}</td>
            <td><code>${key}</code></td>
            <td class="status-${status === '激活中...' ? 'processing' : (status === '成功' ? 'success' : 'error')}">${status}</td>
            <td>${detail}</td>
            <td>${game}</td>
        `;
        
        tbody.appendChild(row);
        
        // 记录结果
        this.results.push({
            index,
            key,
            status,
            detail,
            game,
            success: status === '成功'
        });
    }

    updateResultRow(index, key, result) {
        const tbody = document.getElementById('activation-tbody');
        const rows = tbody.querySelectorAll('tr');
        
        if (rows[index - 1]) {
            const row = rows[index - 1];
            const statusCell = row.cells[2];
            const detailCell = row.cells[3];
            const gameCell = row.cells[4];
            
            statusCell.textContent = result.success ? '成功' : '失败';
            statusCell.className = result.success ? 'status-success' : 'status-error';
            detailCell.textContent = result.detail || '';
            gameCell.textContent = result.game || (result.packageId ? `Package ${result.packageId}` : '');
            
            // 更新结果记录
            const resultIndex = this.results.findIndex(r => r.key === key);
            if (resultIndex !== -1) {
                this.results[resultIndex] = {
                    ...this.results[resultIndex],
                    status: result.success ? '成功' : '失败',
                    detail: result.detail || '',
                    game: result.game || '',
                    success: result.success
                };
            }
        }
    }

    updateStats() {
        const total = this.results.length;
        const success = this.results.filter(r => r.success).length;
        const failed = this.results.filter(r => !r.success && r.status !== '激活中...').length;
        const processing = this.results.filter(r => r.status === '激活中...').length;
        
        document.getElementById('total-keys').textContent = total;
        document.getElementById('success-keys').textContent = success;
        document.getElementById('failed-keys').textContent = failed;
        document.getElementById('processing-keys').textContent = processing;
    }

    updateUnusedKeys() {
        // 收集未使用的Key（失败的Key中某些原因）
        const unusedReasons = ['次数上限', '地区限制', '已拥有', '缺少主游戏', '其他错误', '未知错误'];
        
        this.unusedKeys = this.results
            .filter(r => !r.success && unusedReasons.some(reason => r.detail.includes(reason)))
            .map(r => r.key);
        
        if (this.unusedKeys.length > 0) {
            const area = document.getElementById('unused-keys-area');
            const list = document.getElementById('unused-keys-list');
            
            list.innerHTML = this.unusedKeys.map(key => 
                `<span class="unused-key-item">${key}</span>`
            ).join('');
            
            area.style.display = 'block';
            document.getElementById('export-unused-btn').style.display = 'inline-block';
        }
    }

    exportUnusedKeys() {
        if (this.unusedKeys.length === 0) {
            this.showStatus('没有未使用的Key', 'warning');
            return;
        }

        // 复制到剪贴板
        navigator.clipboard.writeText(this.unusedKeys.join('\n')).then(() => {
            this.showStatus(`已复制 ${this.unusedKeys.length} 个未使用Key到剪贴板`, 'success');
        }).catch(() => {
            // 降级方案：创建文本文件下载
            const blob = new Blob([this.unusedKeys.join('\n')], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'unused-steam-keys.txt';
            a.click();
            URL.revokeObjectURL(url);
            
            this.showStatus('未使用Key已下载为文件', 'success');
        });
    }

    clearResults() {
        this.results = [];
        this.unusedKeys = [];
        
        document.getElementById('activation-tbody').innerHTML = '';
        document.getElementById('activation-results').style.display = 'none';
        document.getElementById('activation-stats').style.display = 'none';
        document.getElementById('unused-keys-area').style.display = 'none';
        document.getElementById('export-unused-btn').style.display = 'none';
        document.getElementById('asf-output').style.display = 'none';
        
        this.updateStats();
        this.showStatus('结果已清空', 'info');
    }

    showStatus(message, type = 'info') {
        // 创建状态提示
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        
        // 设置背景色
        const colors = {
            success: '#10b981',
            error: '#ef4444', 
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        toast.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 模态框工具方法
    createModal(title, content) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
            </div>
        `;

        // 添加样式
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        `;

        const modalContent = modal.querySelector('.modal-content');
        modalContent.style.cssText = `
            background: var(--background-primary);
            border-radius: 8px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;

        const modalHeader = modal.querySelector('.modal-header');
        modalHeader.style.cssText = `
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        `;

        const modalBody = modal.querySelector('.modal-body');
        modalBody.style.cssText = `
            padding: 1.5rem;
        `;

        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        // 关闭事件
        closeBtn.onclick = () => this.closeModal(modal);
        modal.onclick = (e) => {
            if (e.target === modal) {
                this.closeModal(modal);
            }
        };

        document.body.appendChild(modal);
        return modal;
    }

    closeModal(modal) {
        if (modal && modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new SteamKeyRedeemer();
});
