// ==============================
// 核心功能模块 - 肥皂盒管理
// ==============================
const ChestManager = (() => {
    // 配置常量
    const PAGE_SIZE = 10;
    const LOGS_PAGE_SIZE = 40;
    const FAVORITE_CHESTS_KEY = 'favorite_chests';
    const REMAINING_CACHE_KEY = 'remaining_chests_cache';
    const CACHE_EXPIRY_TIME = 24 * 60 * 60 * 1000; // 24小时

    // 状态管理
    let state = {
        sortField: 'sortOrder',
        sortDirection: 'asc',
        pageNumber: 1,
        totalPages: 1,
        currentLogsPage: 1,
        totalLogsPages: 1,
        isLoadingChests: false,
        isLoadingLogs: false,
        remainingDrawModalOpen: false,
        remainingPageCount: 1,
        remainingChestsData: []
    };

    // DOM 元素引用
    const elements = {
        chestsContainer: document.getElementById('chests-container'),
        logsContent: document.getElementById('logs-content'),
        pageIndicator: document.getElementById('page-indicator'),
        sortField: document.getElementById('sort-field'),
        sortDirection: document.getElementById('sort-direction'),
        pageNumber: document.getElementById('page-number'),
        prevPageBtn: document.getElementById('prev-page'),
        nextPageBtn: document.getElementById('next-page'),
        refreshLogsBtn: document.getElementById('refresh-logs-btn'),
        chestSearchInput: document.getElementById('chest-id-search'),
        chestSearchBtn: document.getElementById('chest-search-btn'),
        remainingDrawBtn: document.getElementById('remaining-draw-btn'),
        remainingCloseBtn: document.getElementById('remaining-close-btn'),
        refreshRemainingBtn: document.getElementById('refresh-remaining-btn'),
        remainingPageCountSelect: document.getElementById('remaining-page-count'),
        applyRemainingSettingsBtn: document.getElementById('apply-remaining-settings'),
        clearRemainingCacheBtn: document.getElementById('clear-remaining-cache'),
        remainingContainer: document.getElementById('remaining-chests-container'),
        modalCloseBtn: document.querySelector('#chest-detail-modal .close-btn')
    };

    // ======================
    // 工具函数
    // ======================
    const utils = {
        showToast(message, type = 'success') {
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                document.body.appendChild(toastContainer);
            }
            
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            toastContainer.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('toast-hide');
                setTimeout(() => toastContainer.removeChild(toast), 300);
            }, 3000);
        },

        ensureMarqueeAnimation() {
            // 不再需要此功能，但保留函数以兼容现有代码
        },

        createLoadingIndicator(text) {
            return `
                <div class="loading-indicator">
                    <div class="spin"></div>
                    <span>${text}</span>
                </div>
            `;
        },

        createErrorElement(message) {
            return `
                <div class="error-message">
                    <p>${message}</p>
                    <p>请检查登录状态或稍后再试。</p>
                </div>
            `;
        }
    };

    // ======================
    // 收藏功能
    // ======================
    const favorites = {
        getFavoriteChests() {
            const favoritesStr = localStorage.getItem(FAVORITE_CHESTS_KEY);
            return favoritesStr ? JSON.parse(favoritesStr) : [];
        },

        addChestToFavorites(chest) {
            if (!chest?.id) return false;
            
            const favorites = this.getFavoriteChests();
            if (favorites.some(item => item.id === chest.id)) return false;
            
            const simpleChest = {
                id: chest.id,
                name: chest.name || '未知肥皂盒',
                addTime: new Date().toISOString()
            };
            
            favorites.push(simpleChest);
            localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(favorites));
            utils.showToast('已添加到收藏', 'success');
            return true;
        },

        removeChestFromFavorites(chestId) {
            if (!chestId) return false;
            
            const favorites = this.getFavoriteChests();
            const initialLength = favorites.length;
            const newFavorites = favorites.filter(item => item.id !== chestId);
            
            if (newFavorites.length === initialLength) return false;
            
            localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(newFavorites));
            utils.showToast('已从收藏中移除', 'info');
            return true;
        },

        isChestFavorited(chestId) {
            return chestId && this.getFavoriteChests().some(item => item.id === chestId);
        },

        toggleFavoriteChest(chest) {
            if (!chest?.id) return false;
            
            if (this.isChestFavorited(chest.id)) {
                this.removeChestFromFavorites(chest.id);
                return false;
            } else {
                this.addChestToFavorites(chest);
                return true;
            }
        }
    };

    // ======================
    // 剩余数量缓存管理
    // ======================
    const remainingCache = {
        getRemainingCache() {
            const cacheStr = localStorage.getItem(REMAINING_CACHE_KEY);
            if (!cacheStr) return {};
            
            try {
                return JSON.parse(cacheStr);
            } catch (e) {
                console.error('解析剩余数量缓存失败:', e);
                return {};
            }
        },

        saveRemainingCache(cache) {
            try {
                localStorage.setItem(REMAINING_CACHE_KEY, JSON.stringify(cache));
            } catch (e) {
                console.error('保存剩余数量缓存失败:', e);
            }
        },

        updateRemainingCache(chests) {
            if (!Array.isArray(chests)) return {};
            
            const cache = this.getRemainingCache();
            const changes = {};
            const now = Date.now();
            
            // 清理过期缓存
            Object.keys(cache).forEach(id => {
                if (now - cache[id].timestamp > CACHE_EXPIRY_TIME) {
                    delete cache[id];
                }
            });
            
            // 更新缓存并检测变化
            chests.forEach(chest => {
                const id = chest.id;
                if (!id) return;
                
                const remainingDraw = chest.remainingDraw;
                
                if (cache[id] && cache[id].remaining !== remainingDraw) {
                    changes[id] = {
                        old: cache[id].remaining,
                        new: remainingDraw
                    };
                }
                
                cache[id] = { remaining: remainingDraw, timestamp: now };
            });
            
            this.saveRemainingCache(cache);
            return changes;
        },

        clearRemainingCache() {
            localStorage.removeItem(REMAINING_CACHE_KEY);
            utils.showToast('剩余数量缓存已清除', 'info');
        }
    };

    // ======================
    // 肥皂盒记录管理
    // ======================
    const logsManager = {
        async loadChestLogs(chestId = null) {
            if (state.isLoadingLogs) {
                console.log('活动记录正在加载中，跳过重复请求');
                return [];
            }
            
            state.isLoadingLogs = true;
            
            try {
                const loadingIndicator = elements.logsContent?.querySelector('.loading-indicator');
                if (loadingIndicator) loadingIndicator.style.display = 'flex';
                
                const url = '/api/chests/xboot/detLog/show';
                const params = new URLSearchParams();
                if (chestId) params.append('chestId', chestId);
                params.append('pageNumber', 1);
                params.append('pageSize', 20);
                params.append('sort', 'createTime');
                params.append('order', 'desc');

                const response = await fetch(`${url}?${params.toString()}`);
                if (!response.ok) throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
                
                const data = await response.json();
                if (loadingIndicator) loadingIndicator.style.display = 'none';
                
                // 处理不同API返回格式
                const logsData = 
                    data?.result?.content || 
                    data?.result?.records || 
                    data?.data?.records || 
                    [];
                
                if (logsData.length > 0) {
                    this.renderChestLogs(logsData);
                    return logsData;
                } else {
                    elements.logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
                    return [];
                }
            } catch (error) {
                console.error("加载活动记录时出错:", error);
                if (elements.logsContent) {
                    elements.logsContent.innerHTML = `
                        <div style="padding: 20px; text-align: center; color: rgba(255, 255, 255, 0.8);">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-bottom: 8px;">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                            <div>加载失败: ${error.message}</div>
                        </div>
                    `;
                }
                return [];
            } finally {
                state.isLoadingLogs = false;
            }
        },

        renderChestLogs(logs) {
            if (!elements.logsContent) return;
            elements.logsContent.innerHTML = '';
            
            if (!logs?.length) {
                elements.logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
                return;
            }
            
            const logsList = document.createElement('div');
            logsList.className = 'activity-list';
            
            // 构建返回路径
            const returnParams = new URLSearchParams();
            if (elements.sortField) returnParams.set('sort', elements.sortField.value);
            if (elements.sortDirection) returnParams.set('order', elements.sortDirection.value);
            if (elements.pageNumber) returnParams.set('page', elements.pageNumber.value);
            const returnPath = returnParams.toString() || '';
            
            logs.forEach(log => {
                const gameName = log.gameName || '未知游戏';
                const chestName = log.name || '未知肥皂盒';
                const createTime = log.createTime ? new Date(log.createTime).toLocaleString('zh-CN', {
                    year: 'numeric', month: '2-digit', day: '2-digit',
                    hour: '2-digit', minute: '2-digit', second: '2-digit'
                }).replace(/\//g, '-') : '未知时间';
                
                // 等级颜色
                const gameLv = parseInt(log.lv) || 4;
                const levelColors = {
                    1: '#ec4899', // 传说
                    2: '#7c3aed', // 史诗
                    3: '#3b82f6', // 稀有
                    4: '#4b5563'  // 普通
                };
                
                const logItem = document.createElement('div');
                logItem.className = 'activity-item';
                Object.assign(logItem.style, {
                    display: 'flex',
                    alignItems: 'center',
                    padding: '12px 24px',
                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                    transition: 'background-color 0.2s ease',
                    cursor: 'pointer'
                });
                
                logItem.addEventListener('mouseenter', () => logItem.style.backgroundColor = 'rgba(255, 255, 255, 0.1)');
                logItem.addEventListener('mouseleave', () => logItem.style.backgroundColor = 'transparent');
                
                // 游戏图标
                const gameIcon = document.createElement('div');
                Object.assign(gameIcon.style, {
                    width: '36px',
                    height: '36px',
                    borderRadius: '50%',
                    backgroundColor: levelColors[gameLv],
                    color: 'white',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginRight: '12px',
                    fontWeight: '600',
                    fontSize: '16px',
                    flexShrink: '0'
                });
                gameIcon.textContent = gameLv;
                
                // 内容区域
                const content = document.createElement('div');
                Object.assign(content.style, {
                    flex: '1',
                    minWidth: '0'
                });
                
                const gameNameSpan = document.createElement('div');
                gameNameSpan.textContent = gameName;
                Object.assign(gameNameSpan.style, {
                    fontWeight: '500',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden'
                });
                
                const activityInfo = document.createElement('div');
                Object.assign(activityInfo.style, {
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '0.85rem',
                    marginTop: '4px',
                    opacity: '0.8'
                });
                
                const chestNameSpan = document.createElement('span');
                Object.assign(chestNameSpan.style, {
                    marginRight: '12px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                });
                chestNameSpan.textContent = chestName;
                
                const timeSpan = document.createElement('span');
                timeSpan.textContent = createTime;
                timeSpan.style.whiteSpace = 'nowrap';
                
                activityInfo.appendChild(chestNameSpan);
                activityInfo.appendChild(timeSpan);
                content.appendChild(gameNameSpan);
                content.appendChild(activityInfo);
                logItem.appendChild(gameIcon);
                logItem.appendChild(content);
                
                if (log.chestId) {
                    logItem.addEventListener('click', () => {
                        window.location.href = `/chest-detail?id=${log.chestId}&return_path=${encodeURIComponent(returnPath)}`;
                    });
                }
                
                logsList.appendChild(logItem);
            });
            
            elements.logsContent.appendChild(logsList);
        }
    };

    // ======================
    // 肥皂盒数据管理
    // ======================
    const chestsManager = {
        async loadChests() {
            if (state.isLoadingChests) {
                console.log('肥皂盒数据正在加载中，跳过重复请求');
                return [];
            }
            
            state.isLoadingChests = true;
            
            try {
                // 更新URL参数
                const newUrl = new URL(window.location.href);
                newUrl.searchParams.set('sort', state.sortField);
                newUrl.searchParams.set('order', state.sortDirection);
                newUrl.searchParams.set('page', state.pageNumber.toString());
                window.history.replaceState({}, '', newUrl.toString());
                
                // 显示加载中
                if (elements.chestsContainer) {
                    elements.chestsContainer.innerHTML = utils.createLoadingIndicator('正在加载肥皂盒数据...');
                }
                
                // 构建API请求
                const isRemainingSortMode = state.sortField === 'remainingDraw';
                const pageSize = isRemainingSortMode ? 50 : PAGE_SIZE;
                const apiSort = isRemainingSortMode ? 'oncePrice' : state.sortField;
                const apiPageNumber = isRemainingSortMode ? 1 : state.pageNumber;
                
                const url = `/api/chests?page_number=${apiPageNumber}&page_size=${pageSize}&sort=${apiSort}&order=${state.sortDirection}`;
                const response = await fetch(url);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
                    throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
                }
                
                const data = await response.json();
                if (data.code !== 200) throw new Error(data.message || '获取数据失败');
                
                let content = data.result?.content || [];
                
                // 处理剩余数量排序
                if (isRemainingSortMode) {
                    content.forEach(chest => {
                        chest.remainingDraw = (chest.totalDraw || 0) - (chest.curDraw || 0);
                    });
                    
                    content.sort((a, b) => a.remainingDraw - b.remainingDraw);
                    state.totalPages = Math.ceil(content.length / PAGE_SIZE);
                    
                    const startIndex = (state.pageNumber - 1) * PAGE_SIZE;
                    content = content.slice(startIndex, startIndex + PAGE_SIZE);
                } else {
                    state.totalPages = Math.ceil(data.result.totalElements / data.result.size) || 1;
                }
                
                // 更新UI
                this.updatePaginationControls();
                this.renderChests(content);
                
                return content;
            } catch (error) {
                console.error('获取肥皂盒数据失败:', error);
                if (elements.chestsContainer) {
                    elements.chestsContainer.innerHTML = utils.createErrorElement(`获取肥皂盒数据失败: ${error.message}`);
                }
                throw error;
            } finally {
                state.isLoadingChests = false;
            }
        },

        renderChests(chests) {
            if (!elements.chestsContainer || !chests?.length) {
                if (elements.chestsContainer) {
                    elements.chestsContainer.innerHTML = '<p>没有找到肥皂盒数据。</p>';
                }
                return;
            }
            
            elements.chestsContainer.innerHTML = '';
            const chestsListElement = document.createElement('div');
            chestsListElement.className = 'chests-list';
            
            // 构建返回链接
            const returnPath = `?sort=${state.sortField}&order=${state.sortDirection}&page=${state.pageNumber}`;
            
            chests.forEach(chest => {
                const chestItemTemplate = document.getElementById('chest-item-template');
                if (!chestItemTemplate) return;
                
                const chestItem = chestItemTemplate.content.cloneNode(true).firstElementChild;
                const chestId = chest.id || '';
                chestItem.dataset.chestId = chestId;
                
                // 设置内容
                chestItem.querySelector('.chest-name').textContent = chest.name || '未知肥皂盒';
                chestItem.querySelector('.chest-id').textContent = chestId;
                chestItem.querySelector('.chest-price').textContent = chest.oncePrice ? `¥${chest.oncePrice.toFixed(2)}` : 'N/A';
                
                // 多抽价格和次数
                const multiPriceElem = chestItem.querySelector('.chest-multi-price');
                const multiDrawCountElem = chestItem.querySelector('.multi-draw-count');
                
                if (multiPriceElem) {
                    multiPriceElem.textContent = chest.multiPrice ? `¥${chest.multiPrice.toFixed(2)}` : 'N/A';
                }
                
                // 设置多抽次数
                if (multiDrawCountElem) {
                    const multiDrawCount = chest.multidraw || chest.multiDraw || 3;
                    multiDrawCountElem.textContent = multiDrawCount;
                }
                
                // 卖家信息
                const sellerElem = chestItem.querySelector('.chest-seller');
                if (sellerElem) {
                    sellerElem.textContent = chest.nameSeller || '未知卖家';
                }
                
                // 肥皂盒编号
                const numberElem = chestItem.querySelector('.chest-number');
                if (numberElem) {
                    numberElem.textContent = chest.chestNo || '未知';
                }
                
                // 剩余和总数
                const totalDraw = chest.totalDraw || 0;
                const curDraw = chest.curDraw || 0;
                const remainDraws = totalDraw - curDraw;
                
                chestItem.querySelector('.chest-remain').textContent = remainDraws;
                chestItem.querySelector('.chest-total').textContent = totalDraw;
                
                // 概率显示
                const rate1 = chest.rate1 || chest.probability1 || 0;
                const probabilityDisplay = rate1 <= 1 ? 
                    `${(rate1 * 100).toFixed(2)}%` : 
                    `${parseFloat(rate1).toFixed(2)}%`;
                chestItem.querySelector('.chest-probability').textContent = probabilityDisplay;
                
                // 收藏按钮
                const isFavorited = favorites.isChestFavorited(chestId);
                const favoriteBtn = document.createElement('button');
                favoriteBtn.className = 'favorite-btn';
                favoriteBtn.innerHTML = isFavorited ? 
                    '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#FFB800" stroke="#FFB800" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>' : 
                    '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>';
                
                Object.assign(favoriteBtn.style, {
                    background: 'transparent',
                    border: 'none',
                    cursor: 'pointer',
                    padding: '4px',
                    borderRadius: '50%',
                    marginLeft: '8px'
                });
                
                favoriteBtn.title = isFavorited ? '取消收藏' : '添加到收藏';
                favoriteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const newStatus = favorites.toggleFavoriteChest(chest);
                    favoriteBtn.innerHTML = newStatus ? 
                        '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#FFB800" stroke="#FFB800" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>' : 
                        '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>';
                    favoriteBtn.title = newStatus ? '取消收藏' : '添加到收藏';
                });
                
                // 标题区域
                const chestTitle = chestItem.querySelector('.chest-title');
                chestTitle.style.display = 'flex';
                chestTitle.style.alignItems = 'center';
                chestTitle.appendChild(favoriteBtn);
                
                // 名称点击事件
                const nameElement = chestItem.querySelector('.chest-name');
                if (nameElement) {
                    nameElement.style.cursor = 'pointer';
                    nameElement.title = '点击查看详情页面';
                    nameElement.addEventListener('click', (e) => {
                        e.stopPropagation();
                        window.location.href = `/chest-detail?id=${chest.id}&return_path=${encodeURIComponent(returnPath)}`;
                    });
                }
                
                // 整个卡片点击事件
                chestItem.addEventListener('click', () => {
                    window.location.href = `/chest-detail?id=${chest.id}&return_path=${encodeURIComponent(returnPath)}`;
                });
                
                chestsListElement.appendChild(chestItem);
            });
            
            elements.chestsContainer.appendChild(chestsListElement);
        },

        updatePageIndicator() {
            if (elements.pageIndicator) {
                elements.pageIndicator.textContent = `第 ${state.pageNumber || 1} 页`;
            }
        },

        updatePaginationControls() {
            if (elements.prevPageBtn) elements.prevPageBtn.disabled = state.pageNumber <= 1;
            if (elements.nextPageBtn) elements.nextPageBtn.disabled = state.pageNumber >= state.totalPages;
            this.updatePageIndicator();
        },

        handlePageChange() {
            let page = parseInt(elements.pageNumber?.value) || 1;
            if (page < 1) page = 1;
            if (page > state.totalPages) page = state.totalPages;
            
            if (page !== state.pageNumber) {
                state.pageNumber = page;
                this.loadChests();
            }
        },

        goToPrevPage() {
            if (state.pageNumber > 1) {
                state.pageNumber--;
                this.loadChests();
            }
        },

        goToNextPage() {
            if (state.pageNumber < state.totalPages) {
                state.pageNumber++;
                this.loadChests();
            }
        }
    };

    // ======================
    // 剩余数量排序管理
    // ======================
    const remainingSortManager = {
        async loadRemainingDrawChests() {
            if (!elements.remainingContainer) return;
            
            const loadingIndicator = elements.remainingContainer.querySelector('.loading-indicator');
            if (loadingIndicator) loadingIndicator.style.display = 'flex';
            elements.remainingContainer.innerHTML = '';
            
            try {
                const requests = [];
                for (let page = 1; page <= state.remainingPageCount; page++) {
                    const url = `/api/chests?page_number=${page}&page_size=50&sort=${state.sortField}&order=${state.sortDirection}`;
                    requests.push(fetch(url).then(response => {
                        if (!response.ok) throw new Error(`页面 ${page} 请求失败: ${response.status}`);
                        return response.json();
                    }));
                }
                
                const results = await Promise.all(requests);
                let allChests = [];
                
                results.forEach((data, index) => {
                    if (data.code === 200 && data.result?.content) {
                        allChests = allChests.concat(data.result.content);
                    }
                });
                
                allChests.forEach(chest => {
                    chest.remainingDraw = (chest.totalDraw || 0) - (chest.curDraw || 0);
                });
                
                allChests.sort((a, b) => a.remainingDraw - b.remainingDraw);
                const changes = remainingCache.updateRemainingCache(allChests);
                
                allChests.forEach(chest => {
                    if (changes[chest.id]) {
                        chest.hasChanged = true;
                        const change = changes[chest.id];
                        chest.changeDirection = change.new < change.old ? 'decrease' : 'increase';
                        chest.oldRemaining = change.old;
                    }
                });
                
                state.remainingChestsData = allChests;
                this.renderRemainingChests(allChests);
            } catch (error) {
                console.error('获取剩余数量排序数据失败:', error);
                if (elements.remainingContainer) {
                    elements.remainingContainer.innerHTML = utils.createErrorElement(`获取数据失败: ${error.message}`);
                }
            } finally {
                if (loadingIndicator) loadingIndicator.style.display = 'none';
            }
        },

        renderRemainingChests(chests) {
            if (!elements.remainingContainer || !chests?.length) {
                if (elements.remainingContainer) {
                    elements.remainingContainer.innerHTML = '<p style="padding: 20px; text-align: center;">没有找到肥皂盒数据。</p>';
                }
                return;
            }
            
            elements.remainingContainer.innerHTML = '';
            this.addChangedChestsSection(chests, elements.remainingContainer);
            
            const table = document.createElement('table');
            Object.assign(table.style, {
                width: '100%',
                borderCollapse: 'collapse',
                marginBottom: '20px'
            });
            
            // 表头
            table.innerHTML = `
                <thead>
                    <tr style="background-color: #f5f5f5; font-weight: bold;">
                        <th style="padding: 10px; text-align: left; border-bottom: 1px solid #ddd;">肥皂盒名称</th>
                        <th style="padding: 10px; text-align: center; border-bottom: 1px solid #ddd;">ID</th>
                        <th style="padding: 10px; text-align: center; border-bottom: 1px solid #ddd;">剩余数量</th>
                        <th style="padding: 10px; text-align: right; border-bottom: 1px solid #ddd;">单抽价格</th>
                        <th style="padding: 10px; text-align: center; border-bottom: 1px solid #ddd;">操作</th>
                    </tr>
                </thead>
            `;
            
            const tbody = document.createElement('tbody');
            
            chests.forEach((chest, index) => {
                const row = document.createElement('tr');
                
                if (chest.hasChanged) {
                    row.style.backgroundColor = chest.changeDirection === 'decrease' ? '#ffebee' : '#e8f5e9';
                } else {
                    row.style.backgroundColor = index % 2 === 0 ? '#ffffff' : '#f9f9f9';
                }
                
                row.style.transition = 'background-color 0.2s';
                row.addEventListener('mouseenter', () => row.style.backgroundColor = '#e9f5fe');
                row.addEventListener('mouseleave', () => {
                    if (chest.hasChanged) {
                        row.style.backgroundColor = chest.changeDirection === 'decrease' ? '#ffebee' : '#e8f5e9';
                    } else {
                        row.style.backgroundColor = index % 2 === 0 ? '#ffffff' : '#f9f9f9';
                    }
                });
                
                // 名称单元格
                const nameCell = document.createElement('td');
                nameCell.textContent = chest.name || '未知肥皂盒';
                Object.assign(nameCell.style, {
                    padding: '10px',
                    textAlign: 'left',
                    borderBottom: '1px solid #ddd'
                });
                
                // ID单元格
                const idCell = document.createElement('td');
                idCell.textContent = chest.id || '';
                Object.assign(idCell.style, {
                    padding: '10px',
                    textAlign: 'center',
                    borderBottom: '1px solid #ddd'
                });
                
                // 剩余数量单元格
                const remainingCell = document.createElement('td');
                remainingCell.style.fontWeight = 'bold';
                remainingCell.style.color = chest.remainingDraw < 10 ? '#f44336' : '#4CAF50';
                
                if (chest.hasChanged) {
                    const changeIcon = chest.changeDirection === 'decrease' ? '↓' : '↑';
                    const changeText = document.createElement('span');
                    changeText.textContent = `(${changeIcon} 之前: ${chest.oldRemaining})`;
                    Object.assign(changeText.style, {
                        fontSize: '0.85em',
                        color: chest.changeDirection === 'decrease' ? '#f44336' : '#4CAF50',
                        marginLeft: '5px'
                    });
                    
                    remainingCell.textContent = `${chest.remainingDraw} / ${chest.totalDraw || 0} `;
                    remainingCell.appendChild(changeText);
                } else {
                    remainingCell.textContent = `${chest.remainingDraw} / ${chest.totalDraw || 0}`;
                }
                
                Object.assign(remainingCell.style, {
                    padding: '10px',
                    textAlign: 'center',
                    borderBottom: '1px solid #ddd'
                });
                
                // 价格单元格
                const priceCell = document.createElement('td');
                priceCell.textContent = chest.oncePrice ? `¥${chest.oncePrice.toFixed(2)}` : 'N/A';
                Object.assign(priceCell.style, {
                    padding: '10px',
                    textAlign: 'right',
                    borderBottom: '1px solid #ddd'
                });
                
                // 操作单元格
                const actionCell = document.createElement('td');
                const viewBtn = document.createElement('button');
                viewBtn.textContent = '查看详情';
                Object.assign(viewBtn.style, {
                    padding: '5px 10px',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer'
                });
                
                viewBtn.addEventListener('click', () => {
                    const returnPath = `?sort=${state.sortField}&order=${state.sortDirection}&page=${state.pageNumber}`;
                    window.open(`/chest-detail?id=${chest.id}&return_path=${encodeURIComponent(returnPath)}`, '_blank');
                });
                
                actionCell.appendChild(viewBtn);
                Object.assign(actionCell.style, {
                    padding: '10px',
                    textAlign: 'center',
                    borderBottom: '1px solid #ddd'
                });
                
                // 组装行
                row.appendChild(nameCell);
                row.appendChild(idCell);
                row.appendChild(remainingCell);
                row.appendChild(priceCell);
                row.appendChild(actionCell);
                tbody.appendChild(row);
            });
            
            table.appendChild(tbody);
            elements.remainingContainer.appendChild(table);
        },

        addChangedChestsSection(chests, container) {
            const changedChests = chests.filter(chest => chest.hasChanged);
            if (!changedChests.length) return;
            
            const changesSection = document.createElement('div');
            Object.assign(changesSection.style, {
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px',
                border: '1px solid #dee2e6'
            });
            
            const title = document.createElement('h4');
            title.textContent = `数量变动记录 (${changedChests.length}个)`;
            Object.assign(title.style, {
                margin: '0 0 15px 0',
                fontSize: '16px',
                fontWeight: 'bold',
                color: '#343a40'
            });
            
            changesSection.appendChild(title);
            
            changedChests.forEach((chest, index) => {
                const changeItem = document.createElement('div');
                Object.assign(changeItem.style, {
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '10px 0',
                    borderBottom: index < changedChests.length - 1 ? '1px solid #e9ecef' : 'none'
                });
                
                // 左侧信息
                const leftInfo = document.createElement('div');
                leftInfo.style.flex = '1';
                
                const nameSpan = document.createElement('span');
                nameSpan.textContent = chest.name || '未知肥皂盒';
                Object.assign(nameSpan.style, {
                    fontWeight: '500',
                    color: '#343a40',
                    marginRight: '15px'
                });
                
                const idSpan = document.createElement('span');
                idSpan.textContent = chest.id || '';
                Object.assign(idSpan.style, {
                    color: '#6c757d',
                    fontSize: '14px'
                });
                
                leftInfo.appendChild(nameSpan);
                leftInfo.appendChild(idSpan);
                
                // 右侧信息
                const rightInfo = document.createElement('div');
                Object.assign(rightInfo.style, {
                    display: 'flex',
                    alignItems: 'center',
                    gap: '15px'
                });
                
                // 数量变化显示
                const changeDisplay = document.createElement('div');
                changeDisplay.style.textAlign = 'right';
                
                const currentCount = document.createElement('div');
                currentCount.textContent = `${chest.remainingDraw} / ${chest.totalDraw || 0}`;
                Object.assign(currentCount.style, {
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: '#28a745'
                });
                
                const changeInfo = document.createElement('div');
                const isDecrease = chest.changeDirection === 'decrease';
                changeInfo.textContent = `(${isDecrease ? '↓' : '↑'} 之前: ${chest.oldRemaining})`;
                Object.assign(changeInfo.style, {
                    fontSize: '12px',
                    color: isDecrease ? '#dc3545' : '#28a745'
                });
                
                changeDisplay.appendChild(currentCount);
                changeDisplay.appendChild(changeInfo);
                
                // 详情按钮
                const detailBtn = document.createElement('button');
                detailBtn.textContent = '查看详情';
                Object.assign(detailBtn.style, {
                    padding: '6px 12px',
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px'
                });
                
                detailBtn.addEventListener('click', () => {
                    const returnPath = `?sort=${state.sortField}&order=${state.sortDirection}&page=${state.pageNumber}`;
                    window.open(`/chest-detail?id=${chest.id}&return_path=${encodeURIComponent(returnPath)}`, '_blank');
                });
                
                rightInfo.appendChild(changeDisplay);
                rightInfo.appendChild(detailBtn);
                changeItem.appendChild(leftInfo);
                changeItem.appendChild(rightInfo);
                changesSection.appendChild(changeItem);
            });
            
            container.appendChild(changesSection);
        },

        toggleRemainingDrawModal(show) {
            const modal = document.getElementById('remaining-draw-modal');
            if (!modal) return;
            
            if (show) {
                modal.style.display = 'block';
                state.remainingDrawModalOpen = true;
                
                if (elements.remainingPageCountSelect) {
                    elements.remainingPageCountSelect.value = state.remainingPageCount.toString();
                }
                
                this.loadRemainingDrawChests();
            } else {
                modal.style.display = 'none';
                state.remainingDrawModalOpen = false;
            }
        }
    };

    // ======================
    // 事件绑定和初始化
    // ======================
    const init = {
        async initialize() {
            try {
                // 初始化旋转动画样式
                this.addRotateAnimationStyle();
                
                // 从URL参数初始化状态
                this.initStateFromUrl();
                
                // 绑定事件
                this.bindEvents();
                
                // 并行加载数据
                await Promise.all([
                    chestsManager.loadChests(),
                    logsManager.loadChestLogs()
                ]);
                
                console.log('页面初始化完成');
            } catch (error) {
                console.error('页面初始化失败:', error);
            }
        },

        addRotateAnimationStyle() {
            const style = document.createElement('style');
            style.innerHTML = `
                @keyframes rotate {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }
                .rotating {
                    animation: rotate 1s linear infinite;
                }
            `;
            document.head.appendChild(style);
        },

        initStateFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            
            state.sortField = urlParams.get('sort') || 'sortOrder';
            state.sortDirection = urlParams.get('order') || 'asc';
            state.pageNumber = parseInt(urlParams.get('page')) || 1;
            
            // 更新UI控件
            if (elements.sortField) elements.sortField.value = state.sortField;
            if (elements.sortDirection) elements.sortDirection.value = state.sortDirection;
            if (elements.pageNumber) elements.pageNumber.value = state.pageNumber;
        },

        bindEvents() {
            // 排序按钮
            document.querySelectorAll('.sort-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    if (btn.classList.contains('active')) return;
                    
                    if (btn.classList.contains('sort-dir-btn')) {
                        document.querySelectorAll('.sort-dir-btn').forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        state.sortDirection = btn.dataset.direction;
                    } else {
                        document.querySelectorAll('.sort-btn:not(.sort-dir-btn)').forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                        state.sortField = btn.dataset.sort;
                    }
                    
                    chestsManager.loadChests();
                });
            });
            
            // 分页控制
            if (elements.pageNumber) elements.pageNumber.addEventListener('change', () => chestsManager.handlePageChange());
            if (elements.prevPageBtn) elements.prevPageBtn.addEventListener('click', () => chestsManager.goToPrevPage());
            if (elements.nextPageBtn) elements.nextPageBtn.addEventListener('click', () => chestsManager.goToNextPage());
            
            // 日志刷新
            if (elements.refreshLogsBtn) {
                elements.refreshLogsBtn.addEventListener('click', async () => {
                    elements.refreshLogsBtn.classList.add('rotating');
                    await logsManager.loadChestLogs();
                    setTimeout(() => elements.refreshLogsBtn.classList.remove('rotating'), 1000);
                });
            }
            
            // 肥皂盒搜索
            if (elements.chestSearchBtn && elements.chestSearchInput) {
                elements.chestSearchBtn.addEventListener('click', () => this.handleChestSearch());
                elements.chestSearchInput.addEventListener('keypress', e => {
                    if (e.key === 'Enter') this.handleChestSearch();
                });
            }
            
            // 剩余数量排序
            if (elements.remainingDrawBtn) {
                elements.remainingDrawBtn.addEventListener('click', () => {
                    remainingSortManager.toggleRemainingDrawModal(true);
                });
            }
            
            if (elements.remainingCloseBtn) {
                elements.remainingCloseBtn.addEventListener('click', () => {
                    remainingSortManager.toggleRemainingDrawModal(false);
                });
            }
            
            if (elements.refreshRemainingBtn) {
                elements.refreshRemainingBtn.addEventListener('click', () => {
                    elements.refreshRemainingBtn.classList.add('rotating');
                    remainingSortManager.loadRemainingDrawChests().finally(() => {
                        setTimeout(() => elements.refreshRemainingBtn.classList.remove('rotating'), 1000);
                    });
                });
            }
            
            if (elements.applyRemainingSettingsBtn && elements.remainingPageCountSelect) {
                elements.applyRemainingSettingsBtn.addEventListener('click', () => {
                    state.remainingPageCount = parseInt(elements.remainingPageCountSelect.value) || 1;
                    remainingSortManager.loadRemainingDrawChests();
                });
            }
            
            if (elements.clearRemainingCacheBtn) {
                elements.clearRemainingCacheBtn.addEventListener('click', () => {
                    remainingCache.clearRemainingCache();
                    remainingSortManager.loadRemainingDrawChests();
                });
            }
            
            // 模态框外部点击关闭
            window.addEventListener('click', event => {
                const modal = document.getElementById('remaining-draw-modal');
                if (event.target === modal) {
                    remainingSortManager.toggleRemainingDrawModal(false);
                }
            });
        },

        handleChestSearch() {
            const chestId = elements.chestSearchInput.value.trim();
            if (!chestId) {
                utils.showToast('请输入肥皂盒ID', 'error');
                return;
            }
            
            const returnPath = `?sort=${state.sortField}&order=${state.sortDirection}&page=${state.pageNumber}`;
            window.location.href = `/chest-detail?id=${chestId}&return_path=${encodeURIComponent(returnPath)}`;
        }
    };

    // 公共API
    return {
        init: () => init.initialize(),
        loadChests: () => chestsManager.loadChests(),
        loadChestLogs: (chestId) => logsManager.loadChestLogs(chestId),
        toggleRemainingDrawModal: (show) => remainingSortManager.toggleRemainingDrawModal(show),
        openChestDetail: (chestId) => {
            const returnPath = `?sort=${state.sortField}&order=${state.sortDirection}&page=${state.pageNumber}`;
            window.location.href = `/chest-detail?id=${chestId}&return_path=${encodeURIComponent(returnPath)}`;
        }
    };
})();

// 初始化页面
document.addEventListener('DOMContentLoaded', () => {
    ChestManager.init();
    
    // 检查URL参数，是否需要打开特定肥皂盒详情
    const urlParams = new URLSearchParams(window.location.search);
    const openChestId = urlParams.get('open_chest');
    if (openChestId) {
        ChestManager.openChestDetail(openChestId);
    }
});

// 暴露必要的方法到全局
window.loadChests = ChestManager.loadChests;
window.loadChestLogs = ChestManager.loadChestLogs;
window.openChestDetail = ChestManager.openChestDetail;
window.toggleRemainingDrawModal = ChestManager.toggleRemainingDrawModal;

// 统计按钮事件
document.getElementById('statistics-btn')?.addEventListener('click', async () => {
    const modal = document.getElementById('statistics-modal');
    const content = document.getElementById('statistics-content');
    
    modal.style.display = 'block';
    content.innerHTML = '<div class="loading">正在统计...</div>';
    
    try {
        await generateGameStatistics();
    } catch (error) {
        content.innerHTML = `<div class="error">统计失败: ${error.message}</div>`;
    }
});

// 生成游戏统计
async function generateGameStatistics() {
    const content = document.getElementById('statistics-content');
    const gameStats = new Map();
    
    // 获取当前页面显示的肥皂盒数据
    const chestsContainer = document.getElementById('chests-container');
    const chestItems = chestsContainer.querySelectorAll('.chest-item');
    
    if (chestItems.length === 0) {
        content.innerHTML = '<div class="text-center text-muted">当前页面没有肥皂盒数据</div>';
        return;
    }
    
    for (const chestItem of chestItems) {
        const chestId = chestItem.dataset.chestId;
        
        if (!chestId) continue;
        
        try {
            const response = await fetch(`/api/xboot/chest/showGame?chestId=${chestId}&pageNumber=1&pageSize=100&sort=lv&order=asc`);
            if (!response.ok) continue;
            
            const data = await response.json();
            const games = data.result?.content || data.result?.records || [];
            
            games.forEach(game => {
                const gameName = game.gameNameCn || game.name || '未知游戏';
                const gameId = game.gameId || game.id;
                const stock = parseInt(game.stock) || 0; // 游戏库存数量
                
                if (!gameStats.has(gameId)) {
                    gameStats.set(gameId, {
                        name: gameName,
                        totalStock: 0
                    });
                }
                
                const stat = gameStats.get(gameId);
                stat.totalStock += stock; // 累加库存数量
            });
        } catch (error) {
            console.error(`获取肥皂盒 ${chestId} 游戏列表失败:`, error);
        }
    }
    
    // 渲染统计结果
    renderGameStatistics(gameStats);
}

// 渲染统计结果
function renderGameStatistics(gameStats) {
    const content = document.getElementById('statistics-content');
    
    if (gameStats.size === 0) {
        content.innerHTML = '<div class="text-center text-muted">暂无统计数据</div>';
        return;
    }
    
    // 按库存总数量排序
    const sortedGames = Array.from(gameStats.entries())
        .sort((a, b) => b[1].totalStock - a[1].totalStock);
    
    let html = `
        <div class="statistics-summary">
            <p>共统计 <strong>${gameStats.size}</strong> 款游戏</p>
        </div>
        <div class="statistics-list">
    `;
    
    sortedGames.forEach(([gameId, stat]) => {
        html += `
            <div class="statistics-item" style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee;">
                <span>${stat.name}</span>
                <span style="font-weight: bold; color: #007bff;">${stat.totalStock} 个</span>
            </div>
        `;
    });
    
    html += '</div>';
    content.innerHTML = html;
}

// 滚动到指定肥皂盒
function scrollToChest(chestId) {
    const chestElement = document.querySelector(`[data-chest-id="${chestId}"]`);
    if (chestElement) {
        chestElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        chestElement.style.backgroundColor = '#fff3cd';
        setTimeout(() => {
            chestElement.style.backgroundColor = '';
        }, 2000);
    }
    // 关闭模态框
    document.getElementById('statistics-modal').style.display = 'none';
}







