{% from "macros/icons.html" import icon %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PyWatcher{% endblock %}</title>
    <!-- 添加页面预加载主题逻辑，避免闪烁 -->
    <script>
        // 在DOM渲染前应用保存的主题
        (function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
        })();
    </script>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/style.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div id="top-loader"></div>

    <!-- 顶部导航栏（桌面端） -->
    <header class="app-header">
        <div class="container">
            <nav>
                <div class="brand-logo">
                    <strong>PyWatcher</strong>
                </div>
                <div class="nav-links">
                    <a href="/" class="nav-link">
                        {{ icon('home') }} 仪表盘
                    </a>
                    <a href="/accounts" class="nav-link">
                        {{ icon('users') }} 账户管理
                    </a>
                    <a href="/chests" class="nav-link">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg>
                        肥皂盒
                    </a>
                </div>
                <div class="mode-toggle">
                    <a href="/games" class="icon-btn outline" title="游戏列表">
                        {{ icon('search') }}
                    </a>
                    <button id="theme-toggle" class="outline icon-btn" aria-label="切换主题">
                        <!-- 图标由 JS 注入 -->
                    </button>
                </div>
            </nav>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="container">
        {% block content %}{% endblock %}
    </main>

    <!-- 底部导航栏（移动端） -->
    <nav class="bottom-nav">
        <a href="/" class="nav-link">
            {{ icon('home') }}
            <span>仪表盘</span>
        </a>
        <a href="/accounts" class="nav-link">
            {{ icon('users') }}
            <span>账户管理</span>
        </a>
        <a href="/games" class="nav-link">
            {{ icon('search') }}
            <span>游戏列表</span>
        </a>
        <a href="/chests" class="nav-link">
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect></svg>
            <span>肥皂盒</span>
        </a>
    </nav>
    
    <!-- 将脚本移动到 body 末尾以避免渲染阻塞 -->
    <script src="{{ url_for('static', path='js/lib/Sortable.min.js') }}"></script>
    <script src="{{ url_for('static', path='js/common.js') }}" defer></script>
    
    <!-- 页面特定脚本 -->
    {% block scripts %}{% endblock %}
</body>
</html> 