document.addEventListener('DOMContentLoaded', () => {
    const phoneInput = document.getElementById('phone');
    const codeInput = document.getElementById('code');
    const sendSmsBtn = document.getElementById('send-sms-btn');
    const loginForm = document.getElementById('sms-login-form');
    const accountListContainer = document.getElementById('mobile-account-list-container');
    const refreshBtn = document.getElementById('refresh-mobile-accounts-btn');

    // 账号类型对照表 - 显示名称和CSS类
    const ACCOUNT_TYPES = {
        'query': { name: '查询账户', color: 'bg-info' },
        'order_primary': { name: '下单主账号', color: 'bg-success' },
        'order_secondary': { name: '下单副账号', color: 'bg-warning' },
        'backup': { name: '备用账号', color: 'bg-secondary' }
    };

    // Define SVGs for dynamic injection
    const loaderSVG = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-loader spin"><line x1="12" y1="2" x2="12" y2="6"></line><line x1="12" y1="18" x2="12" y2="22"></line><line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line><line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line><line x1="2" y1="12" x2="6" y2="12"></line><line x1="18" y1="12" x2="22" y2="12"></line><line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line><line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line></svg>`;
    const alertCircleSVG = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-alert-circle"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>`;
    const trash2SVG = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>`;
    const settingsSVG = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-settings"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>`;

    // --- 添加在页面上全局可访问的方法 ---
    // 使用window对象使这些函数在HTML中可调用
    window.deleteMobileAccount = deleteMobileAccount;
    window.setAccountType = setAccountType;
    window.showAccountTypeMenu = showAccountTypeMenu;

    // --- Notifications ---
    function showToast(message, isError = false) {
        const toast = document.createElement('div');
        toast.className = `toast ${isError ? 'error' : ''}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        // Trigger the animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Remove the toast after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            toast.addEventListener('transitionend', () => toast.remove());
        }, 3000);
    }

    // 显示加载器
    function showLoader() {
        const loader = document.createElement('div');
        loader.id = 'global-loader';
        loader.innerHTML = loaderSVG;
        document.body.appendChild(loader);
    }

    // 隐藏加载器
    function hideLoader() {
        const loader = document.getElementById('global-loader');
        if (loader) loader.remove();
    }

    // --- API Calls ---
    async function fetchAPI(url, options = {}) {
        try {
            const response = await fetch(url, options);
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.detail || `HTTP error! status: ${response.status}`);
            }
            return data;
        } catch (error) {
            console.error('API Error:', error);
            showToast(error.message, true);
            throw error;
        }
    }

    async function fetchAndRenderMobileAccounts() {
        if (!accountListContainer) return;
        accountListContainer.innerHTML = `<div class="loading-indicator">${loaderSVG} 正在加载账户...</div>`;
        
        try {
            const response = await fetch('/api/accounts');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const accounts = await response.json();

            if (accounts.length === 0) {
                accountListContainer.innerHTML = '<p class="text-muted">尚未添加任何移动账户。</p>';
                return;
            }

            const ul = document.createElement('ul');
            accounts.forEach(account => {
                const li = document.createElement('li');
                const accountType = ACCOUNT_TYPES[account.account_type] || ACCOUNT_TYPES.backup;
                
                li.innerHTML = `
                    <div class="account-info">
                        <strong>${account.name || '未知账户'}</strong>
                        <div class="account-badges">
                            <span class="badge ${accountType.color}">${accountType.name}</span>
                        </div>
                        <span class="text-muted">ID: ${account.id || 'N/A'}</span>
                    </div>
                    <div class="account-actions">
                        <button class="outline icon-btn" data-id="${account.id}" onclick="showAccountTypeMenu('${account.id}')" title="设置账户类型">
                            ${settingsSVG}
                        </button>
                        <button class="outline icon-btn" data-id="${account.id}" onclick="deleteMobileAccount('${account.id}')" title="删除">
                            ${trash2SVG}
                        </button>
                    </div>
                    <div id="type-menu-${account.id}" class="account-type-menu" style="display:none;">
                        <div class="menu-header">选择账户类型</div>
                        <div class="menu-options">
                            <button onclick="setAccountType('${account.id}', 'query')">查询账户</button>
                            <button onclick="setAccountType('${account.id}', 'order_primary')">下单主账号</button>
                            <button onclick="setAccountType('${account.id}', 'order_secondary')">下单副账号</button>
                            <button onclick="setAccountType('${account.id}', 'backup')">备用账号</button>
                        </div>
                    </div>
                `;
                ul.appendChild(li);
            });
            accountListContainer.innerHTML = '';
            accountListContainer.appendChild(ul);
        } catch (error) {
            console.error('无法加载移动账户:', error);
            accountListContainer.innerHTML = `<div class="error-indicator">${alertCircleSVG} 加载失败，请重试。</div>`;
        }
    }

    async function deleteMobileAccount(accountId) {
        if (!confirm('确定要删除这个账户吗？')) {
            return;
        }
        showLoader();
        try {
            const response = await fetch(`/api/accounts/${accountId}`, {
                method: 'DELETE',
            });
            
            if (response.ok) {
                showToast('账户删除成功');
                fetchAndRenderMobileAccounts(); // 刷新列表
            } else {
                const result = await response.json().catch(() => ({}));
                throw new Error(result.detail || '删除失败');
            }
        } catch (error) {
            showToast(`删除失败: ${error.message}`, true);
        } finally {
            hideLoader();
        }
    }
    
    // 设置账户类型
    async function setAccountType(accountId, accountType) {
        showLoader();
        try {
            const result = await fetchAPI(`/api/accounts/${accountId}/set-type`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ account_type: accountType })
            });
            showToast(result.message || '账户类型已更新');
            
            // 隐藏菜单
            const menu = document.getElementById(`type-menu-${accountId}`);
            if (menu) menu.style.display = 'none';
            
            fetchAndRenderMobileAccounts(); // 刷新列表
        } catch (error) {
            // Error toast is already shown by fetchAPI
        } finally {
            hideLoader();
        }
    }
    
    // 显示账户类型选择菜单
    function showAccountTypeMenu(accountId) {
        // 获取目标菜单
        const menu = document.getElementById(`type-menu-${accountId}`);
        if (!menu) return;
        
        // 切换菜单显示状态
        const isVisible = menu.style.display === 'block';
        
        // 先关闭所有打开的菜单
        document.querySelectorAll('.account-type-menu').forEach(m => {
            m.style.display = 'none';
        });
        
        // 如果菜单原来是隐藏的，则显示它
        if (!isVisible) {
            menu.style.display = 'block';
            
            // 立即添加一个点击监听器到document上
            setTimeout(() => {
                const closeMenuListener = function(e) {
                    // 如果点击的不是菜单本身或触发按钮
                    if (!menu.contains(e.target) && !e.target.matches(`button[data-id="${accountId}"]`)) {
                        menu.style.display = 'none';
                        document.removeEventListener('click', closeMenuListener);
                    }
                };
                
                document.addEventListener('click', closeMenuListener);
            }, 0);
        }
    }

    // --- Event Handlers ---
    sendSmsBtn.addEventListener('click', async () => {
        const phone = phoneInput.value;
        if (!phone) {
            showToast('请输入手机号码。', true);
            return;
        }

        sendSmsBtn.disabled = true;
        sendSmsBtn.textContent = '发送中...';

        try {
            const result = await fetchAPI('/api/login/sms', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ phone }),
            });
            showToast(result.message || '验证码已发送。');
        } finally {
            sendSmsBtn.disabled = false;
            sendSmsBtn.textContent = '发送验证码';
        }
    });

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const phone = phoneInput.value;
        const code = codeInput.value;

        if (!phone || !code) {
            showToast('手机号和验证码不能为空。', true);
            return;
        }

        try {
            await fetchAPI('/api/accounts/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ phone, code, remark: `用户-${phone.slice(-4)}` }),
            });
            showToast('账户添加成功！');
            phoneInput.value = '';
            codeInput.value = '';
            fetchAndRenderMobileAccounts(); // Refresh the list
        } catch (error) {
            // Error toast is already shown by fetchAPI
        }
    });
    
    if (refreshBtn) {
        refreshBtn.addEventListener('click', fetchAndRenderMobileAccounts);
    }

    // --- Initial Load ---
    fetchAndRenderMobileAccounts();
}); 