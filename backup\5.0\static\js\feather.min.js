!(function (e, t) {
  "object" == typeof exports && "object" == typeof module
    ? (module.exports = t())
    : "function" == typeof define && define.amd
    ? define([], t)
    : "object" == typeof exports
    ? (exports.feather = t())
    : (e.feather = t());
})(this, function () {
  return (function (e) {
    var t = {};
    function n(r) {
      if (t[r]) return t[r].exports;
      var i = (t[r] = { i: r, l: !1, exports: {} });
      return e[r].call(i.exports, i, i.exports, n), (i.l = !0), i.exports;
    }
    return (
      (n.m = e),
      (n.c = t),
      (n.d = function (e, t, r) {
        n.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: r });
      }),
      (n.r = function (e) {
        "undefined" != typeof Symbol &&
          Symbol.toStringTag &&
          Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }),
          Object.defineProperty(e, "__esModule", { value: !0 });
      }),
      (n.t = function (e, t) {
        if ((1 & t && (e = n(e)), 8 & t)) return e;
        if (4 & t && "object" == typeof e && e && e.__esModule) return e;
        var r = Object.create(null);
        if (
          (n.r(r),
          Object.defineProperty(r, "default", { enumerable: !0, value: e }),
          2 & t && "string" != typeof e)
        )
          for (var i in e)
            n.d(
              r,
              i,
              function (t) {
                return e[t];
              }.bind(null, i)
            );
        return r;
      }),
      (n.n = function (e) {
        var t =
          e && e.__esModule
            ? function () {
                return e.default;
              }
            : function () {
                return e;
              };
        return n.d(t, "a", t), t;
      }),
      (n.o = function (e, t) {
        return Object.prototype.hasOwnProperty.call(e, t);
      }),
      (n.p = ""),
      n((n.s = 87))
    );
  })({
    13: function (e, t, n) {
      "use strict";
      Object.defineProperty(t, "__esModule", { value: !0 });
      var r = {
        activity: "activity",
        airplay: "airplay",
        alertCircle: "alert-circle",
        "alert-octagon": "alert-octagon",
        "alert-triangle": "alert-triangle",
        "align-center": "align-center",
        "align-justify": "align-justify",
        "align-left": "align-left",
        "align-right": "align-right",
        anchor: "anchor",
        aperture: "aperture",
        archive: "archive",
        arrowDown: "arrow-down",
        "arrow-down-circle": "arrow-down-circle",
        "arrow-down-left": "arrow-down-left",
        "arrow-down-right": "arrow-down-right",
        arrowLeft: "arrow-left",
        "arrow-left-circle": "arrow-left-circle",
        "arrow-right": "arrow-right",
        "arrow-right-circle": "arrow-right-circle",
        arrowUp: "arrow-up",
        "arrow-up-circle": "arrow-up-circle",
        "arrow-up-left": "arrow-up-left",
        "arrow-up-right": "arrow-up-right",
        "at-sign": "at-sign",
        award: "award",
        "bar-chart": "bar-chart",
        "bar-chart-2": "bar-chart-2",
        battery: "battery",
        "battery-charging": "battery-charging",
        bell: "bell",
        "bell-off": "bell-off",
        bluetooth: "bluetooth",
        bold: "bold",
        "book-open": "book-open",
        book: "book",
        bookmark: "bookmark",
        box: "box",
        briefcase: "briefcase",
        calendar: "calendar",
        "camera-off": "camera-off",
        camera: "camera",
        cast: "cast",
        "check-circle": "check-circle",
        check: "check",
        "check-square": "check-square",
        chevronDown: "chevron-down",
        "chevron-left": "chevron-left",
        "chevron-right": "chevron-right",
        chevronUp: "chevron-up",
        chevronsDown: "chevrons-down",
        chevronsLeft: "chevrons-left",
        chevronsRight: "chevrons-right",
        chevronsUp: "chevrons-up",
        chrome: "chrome",
        circle: "circle",
        clipboard: "clipboard",
        clock: "clock",
        "cloud-drizzle": "cloud-drizzle",
        "cloud-lightning": "cloud-lightning",
        "cloud-off": "cloud-off",
        "cloud-rain": "cloud-rain",
        "cloud-snow": "cloud-snow",
        cloud: "cloud",
        code: "code",
        codepen: "codepen",
        codesandbox: "codesandbox",
        coffee: "coffee",
        columns: "columns",
        command: "command",
        compass: "compass",
        copy: "copy",
        "corner-down-left": "corner-down-left",
        "corner-down-right": "corner-down-right",
        "corner-left-down": "corner-left-down",
        "corner-left-up": "corner-left-up",
        "corner-right-down": "corner-right-down",
        "corner-right-up": "corner-right-up",
        "corner-up-left": "corner-up-left",
        "corner-up-right": "corner-up-right",
        cpu: "cpu",
        "credit-card": "credit-card",
        crop: "crop",
        crosshair: "crosshair",
        database: "database",
        delete: "delete",
        disc: "disc",
        "divide-circle": "divide-circle",
        "divide-square": "divide-square",
        divide: "divide",
        "dollar-sign": "dollar-sign",
        "download-cloud": "download-cloud",
        download: "download",
        dribbble: "dribbble",
        droplet: "droplet",
        "edit-2": "edit-2",
        "edit-3": "edit-3",
        edit: "edit",
        "external-link": "external-link",
        "eye-off": "eye-off",
        eye: "eye",
        facebook: "facebook",
        "fast-forward": "fast-forward",
        feather: "feather",
        figma: "figma",
        "file-minus": "file-minus",
        "file-plus": "file-plus",
        "file-text": "file-text",
        file: "file",
        film: "film",
        filter: "filter",
        flag: "flag",
        "folder-minus": "folder-minus",
        "folder-plus": "folder-plus",
        folder: "folder",
        framer: "framer",
        frown: "frown",
        gift: "gift",
        "git-branch": "git-branch",
        "git-commit": "git-commit",
        "git-merge": "git-merge",
        "git-pull-request": "git-pull-request",
        github: "github",
        gitlab: "gitlab",
        globe: "globe",
        grid: "grid",
        "hard-drive": "hard-drive",
        hash: "hash",
        headphones: "headphones",
        heart: "heart",
        "help-circle": "help-circle",
        hexagon: "hexagon",
        home: "home",
        image: "image",
        inbox: "inbox",
        info: "info",
        instagram: "instagram",
        italic: "italic",
        key: "key",
        layers: "layers",
        layout: "layout",
        "life-buoy": "life-buoy",
        "link-2": "link-2",
        link: "link",
        linkedin: "linkedin",
        list: "list",
        loader: "loader",
        lock: "lock",
        "log-in": "log-in",
        "log-out": "log-out",
        mail: "mail",
        "map-pin": "map-pin",
        map: "map",
        "maximize-2": "maximize-2",
        maximize: "maximize",
        meh: "meh",
        menu: "menu",
        "message-circle": "message-circle",
        "message-square": "message-square",
        "mic-off": "mic-off",
        mic: "mic",
        "minimize-2": "minimize-2",
        minimize: "minimize",
        "minus-circle": "minus-circle",
        "minus-square": "minus-square",
        minus: "minus",
        monitor: "monitor",
        moon: "moon",
        "more-horizontal": "more-horizontal",
        "more-vertical": "more-vertical",
        "mouse-pointer": "mouse-pointer",
        move: "move",
        music: "music",
        "navigation-2": "navigation-2",
        navigation: "navigation",
        octagon: "octagon",
        package: "package",
        paperclip: "paperclip",
        "pause-circle": "pause-circle",
        pause: "pause",
        "pen-tool": "pen-tool",
        percent: "percent",
        "phone-call": "phone-call",
        "phone-forwarded": "phone-forwarded",
        "phone-incoming": "phone-incoming",
        "phone-missed": "phone-missed",
        "phone-off": "phone-off",
        "phone-outgoing": "phone-outgoing",
        phone: "phone",
        "pie-chart": "pie-chart",
        "play-circle": "play-circle",
        play: "play",
        "plus-circle": "plus-circle",
        "plus-square": "plus-square",
        plus: "plus",
        pocket: "pocket",
        power: "power",
        printer: "printer",
        radio: "radio",
        "refresh-ccw": "refresh-ccw",
        "refresh-cw": "refresh-cw",
        repeat: "repeat",
        rewind: "rewind",
        "rotate-ccw": "rotate-ccw",
        "rotate-cw": "rotate-cw",
        rss: "rss",
        save: "save",
        scissors: "scissors",
        search: "search",
        send: "send",
        server: "server",
        settings: "settings",
        "share-2": "share-2",
        share: "share",
        "shield-off": "shield-off",
        shield: "shield",
        "shopping-bag": "shopping-bag",
        "shopping-cart": "shopping-cart",
        shuffle: "shuffle",
        sidebar: "sidebar",
        "skip-back": "skip-back",
        "skip-forward": "skip-forward",
        slack: "slack",
        slash: "slash",
        sliders: "sliders",
        smartphone: "smartphone",
        smile: "smile",
        speaker: "speaker",
        square: "square",
        star: "star",
        "stop-circle": "stop-circle",
        sun: "sun",
        sunrise: "sunrise",
        sunset: "sunset",
        table: "table",
        tablet: "tablet",
        tag: "tag",
        target: "target",
        terminal: "terminal",
        thermometer: "thermometer",
        "thumbs-down": "thumbs-down",
        "thumbs-up": "thumbs-up",
        "toggle-left": "toggle-left",
        "toggle-right": "toggle-right",
        tool: "tool",
        "trash-2": "trash-2",
        trash: "trash",
        trello: "trello",
        "trending-down": "trending-down",
        "trending-up": "trending-up",
        triangle: "triangle",
        truck: "truck",
        tv: "tv",
        twitch: "twitch",
        twitter: "twitter",
        type: "type",
        umbrella: "umbrella",
        underline: "underline",
        unlock: "unlock",
        "upload-cloud": "upload-cloud",
        upload: "upload",
        "user-check": "user-check",
        "user-minus": "user-minus",
        "user-plus": "user-plus",
        "user-x": "user-x",
        user: "user",
        users: "users",
        "video-off": "video-off",
        video: "video",
        voicemail: "voicemail",
        volume: "volume",
        "volume-1": "volume-1",
        "volume-2": "volume-2",
        "volume-x": "volume-x",
        watch: "watch",
        "wifi-off": "wifi-off",
        wifi: "wifi",
        wind: "wind",
        "x-circle": "x-circle",
        "x-octagon": "x-octagon",
        "x-square": "x-square",
        x: "x",
        youtube: "youtube",
        "zap-off": "zap-off",
        zap: "zap",
        "zoom-in": "zoom-in",
        "zoom-out": "zoom-out",
      };
      t.default = r;
    },
    87: function (e, t, n) {
      "use strict";
      Object.defineProperty(t, "__esModule", { value: !0 });
      var r = n(88),
        i = n(13);
      function a(e) {
        if ("string" == typeof e)
          return (function (e, t) {
            if ((void 0 === t && (t = {}), !e))
              throw new Error("Cannot feather an empty string.");
            var n = /\{\{([\s\S]+?)\}\}/g,
              r = e.replace(n, function (e, n) {
                return t[n] || "";
              });
            return r;
          })(i.default[e]);
        for (var t in e) a(t);
        return a(null);
      }
      (t.icons = i.default),
        (t.toSvg = function (e, t) {
          if ((void 0 === t && (t = {}), !e))
            throw new Error(
              "The required `key` (icon name) parameter is missing."
            );
          if (!i.default[e])
            throw new Error(
              "No icon matching '" +
                e +
                "'. See the complete list of icons at https://feathericons.com"
            );
          return r.default(e, i.default[e], t).outerHTML;
        }),
        (t.replace = function (e) {
          void 0 === e && (e = {});
          var t = e.name || "data-feather",
            n = Array.prototype.slice.call(
              document.querySelectorAll("[" + t + "]")
            );
          n.forEach(function (n) {
            var i = n.getAttribute(t);
            n.outerHTML = r.default(i, a(i), e).outerHTML;
          });
        }),
        (t.default = { icons: i.default, toSvg: t.toSvg, replace: t.replace });
    },
    88: function (e, t, n) {
      "use strict";
      Object.defineProperty(t, "__esModule", { value: !0 });
      var r = {
        width: 24,
        height: 24,
        viewBox: "0 0 24 24",
        fill: "none",
        stroke: "currentColor",
        "stroke-width": 2,
        "stroke-linecap": "round",
        "stroke-linejoin": "round",
      };
      t.default = function (e, t, n) {
        var i = (function (e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {},
              i = Object.keys(n);
            "function" == typeof Object.getOwnPropertySymbols &&
              (i = i.concat(
                Object.getOwnPropertySymbols(n).filter(function (e) {
                  return Object.getOwnPropertyDescriptor(n, e).enumerable;
                })
              )),
              i.forEach(function (t) {
                !(function (e, t, n) {
                  t in e
                    ? Object.defineProperty(e, t, {
                        value: n,
                        enumerable: !0,
                        configurable: !0,
                        writable: !0,
                      })
                    : (e[t] = n);
                })(e, t, n[t]);
              });
          }
          return e;
        })({}, r, n);
        return (function (e, t) {
          var n = document.createElementNS("http://www.w3.org/2000/svg", "svg");
          return (
            Object.keys(t).forEach(function (e) {
              n.setAttribute(e, t[e]);
            }),
            (n.innerHTML = e),
            n
          );
        })(
          t,
          (function (e) {
            var t = (function (e) {
              for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {},
                  i = Object.keys(n);
                "function" == typeof Object.getOwnPropertySymbols &&
                  (i = i.concat(
                    Object.getOwnPropertySymbols(n).filter(function (e) {
                      return Object.getOwnPropertyDescriptor(n, e).enumerable;
                    })
                  )),
                  i.forEach(function (t) {
                    !(function (e, t, n) {
                      t in e
                        ? Object.defineProperty(e, t, {
                            value: n,
                            enumerable: !0,
                            configurable: !0,
                            writable: !0,
                          })
                        : (e[t] = n);
                    })(e, t, n[t]);
                  });
              }
              return e;
            })({}, e);
            return (
              (t.class = (function (e) {
                return e
                  ? e + " feather feather-" + t.name
                  : "feather feather-" + t.name;
              })(e.class)),
              delete t.name,
              t
            );
          })(
            (function (e, t) {
              var n = {};
              return (
                Object.keys(e).forEach(function (t) {
                  -1 === Object.keys(n).indexOf(t) && (n[t] = e[t]);
                }),
                Object.keys(t).forEach(function (e) {
                  n[e] = t[e];
                }),
                n
              );
            })(i, { name: e, class: n.class })
          )
        );
      };
    },
  });
});
