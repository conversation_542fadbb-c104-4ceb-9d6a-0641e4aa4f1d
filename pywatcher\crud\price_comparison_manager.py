import aiosqlite
import logging
from typing import List, Optional
from datetime import datetime
from pathlib import Path
from pywatcher.models.price_comparison import GamePrice, GamePriceCreate, PriceSearchResult

logger = logging.getLogger(__name__)

class PriceComparisonManager:
    """价格比较数据管理器"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            # 使用项目根目录下的data文件夹
            base_dir = Path(__file__).resolve().parent.parent
            self.db_path = str(base_dir / "data" / "price_comparison.db")
        else:
            self.db_path = db_path
        
    async def init_db(self):
        """初始化数据库表"""
        # 确保data目录存在
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(exist_ok=True)
        
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS game_prices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    price REAL NOT NULL,
                    source TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引以提高搜索性能
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_game_name ON game_prices(name)
            """)
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_source ON game_prices(source)
            """)
            
            await db.commit()
            logger.info(f"价格比较数据库已初始化: {self.db_path}")
    
    async def add_game_price(self, game_price: GamePriceCreate) -> GamePrice:
        """添加游戏价格数据"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            
            # 检查是否已存在相同名称和来源的记录
            cursor = await db.execute(
                "SELECT id FROM game_prices WHERE name = ? AND source = ?",
                (game_price.name, game_price.source)
            )
            existing = await cursor.fetchone()
            
            current_time = datetime.now()
            
            if existing:
                # 更新现有记录
                await db.execute(
                    "UPDATE game_prices SET price = ?, updated_at = ? WHERE id = ?",
                    (game_price.price, current_time, existing['id'])
                )
                game_id = existing['id']
                logger.info(f"更新游戏价格: {game_price.name} ({game_price.source}) - ¥{game_price.price}")
            else:
                # 插入新记录
                cursor = await db.execute(
                    "INSERT INTO game_prices (name, price, source, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
                    (game_price.name, game_price.price, game_price.source, current_time, current_time)
                )
                game_id = cursor.lastrowid
                logger.info(f"添加新游戏价格: {game_price.name} ({game_price.source}) - ¥{game_price.price}")
            
            await db.commit()
            
            # 返回创建/更新的记录
            cursor = await db.execute(
                "SELECT * FROM game_prices WHERE id = ?",
                (game_id,)
            )
            row = await cursor.fetchone()
            
            return GamePrice(
                id=row['id'],
                name=row['name'],
                price=row['price'],
                source=row['source'],
                created_at=datetime.fromisoformat(row['created_at']),
                updated_at=datetime.fromisoformat(row['updated_at'])
            )
    
    async def search_games(self, search_term: str) -> List[PriceSearchResult]:
        """搜索游戏价格"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            
            # 使用模糊搜索
            cursor = await db.execute(
                """
                SELECT name, price, source, updated_at 
                FROM game_prices 
                WHERE name LIKE ? 
                ORDER BY name, source
                """,
                (f"%{search_term}%",)
            )
            rows = await cursor.fetchall()
            
            results = []
            for row in rows:
                results.append(PriceSearchResult(
                    name=row['name'],
                    price=row['price'],
                    source=row['source'],
                    updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                ))
            
            logger.info(f"搜索 '{search_term}' 找到 {len(results)} 条结果")
            return results
    
    async def get_all_games(self, limit: int = 100) -> List[PriceSearchResult]:
        """获取所有游戏价格数据"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            
            cursor = await db.execute(
                """
                SELECT name, price, source, updated_at 
                FROM game_prices 
                ORDER BY updated_at DESC 
                LIMIT ?
                """,
                (limit,)
            )
            rows = await cursor.fetchall()
            
            results = []
            for row in rows:
                results.append(PriceSearchResult(
                    name=row['name'],
                    price=row['price'],
                    source=row['source'],
                    updated_at=datetime.fromisoformat(row['updated_at']) if row['updated_at'] else None
                ))
            
            return results
    
    async def bulk_insert_fhyx_games(self, games_data: List[dict]):
        """批量插入fhyx游戏数据"""
        async with aiosqlite.connect(self.db_path) as db:
            current_time = datetime.now()
            
            # 先删除旧的fhyx数据
            await db.execute("DELETE FROM game_prices WHERE source = 'fhyx'")
            
            # 批量插入新数据
            insert_data = []
            for game in games_data:
                try:
                    price = float(game['price'])
                    insert_data.append((game['name'], price, 'fhyx', current_time, current_time))
                except (ValueError, KeyError) as e:
                    logger.warning(f"跳过无效游戏数据: {game}, 错误: {e}")
                    continue
            
            if insert_data:
                await db.executemany(
                    "INSERT INTO game_prices (name, price, source, created_at, updated_at) VALUES (?, ?, ?, ?, ?)",
                    insert_data
                )
                await db.commit()
                logger.info(f"批量插入 {len(insert_data)} 条fhyx游戏价格数据")
            
            return len(insert_data)
    
    async def get_stats(self) -> dict:
        """获取数据库统计信息"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            
            # 总记录数
            cursor = await db.execute("SELECT COUNT(*) as total FROM game_prices")
            total = (await cursor.fetchone())['total']
            
            # 按来源统计
            cursor = await db.execute(
                "SELECT source, COUNT(*) as count FROM game_prices GROUP BY source"
            )
            source_stats = {row['source']: row['count'] for row in await cursor.fetchall()}
            
            # 最近更新时间
            cursor = await db.execute("SELECT MAX(updated_at) as last_update FROM game_prices")
            last_update = (await cursor.fetchone())['last_update']
            
            return {
                'total': total,
                'by_source': source_stats,
                'last_update': last_update
            }

# 全局实例
_price_manager = None

async def get_price_manager() -> PriceComparisonManager:
    """获取价格比较管理器实例"""
    global _price_manager
    if _price_manager is None:
        _price_manager = PriceComparisonManager()
        await _price_manager.init_db()
    return _price_manager

