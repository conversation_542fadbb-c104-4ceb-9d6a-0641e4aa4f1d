"""
Steam相关API端点
"""
import logging
import re
import aiohttp
from fastapi import APIRouter, Request, HTTPException, Depends
from typing import Dict, Any, Optional
import json

from ..dependencies import get_client_session

logger = logging.getLogger(__name__)
router = APIRouter()

def _extract_cookie_string(request: Request) -> str:
    """优先从本站 HttpOnly cookie `steam_proxy_cookies` 中取出原始 Steam Cookie 串。
    若没有，则尝试用 request.cookies 里能拿到的键拼接（通常拿不到第三方域的）。"""
    # 1) 优先使用用户通过前端保存到本站域名的 HttpOnly cookie
    raw = request.cookies.get("steam_proxy_cookies", "").strip()
    if raw:
        return raw

    # 2) 兜底：从可见 cookies 中挑可能与 steam 相关的（通常为空）
    steam_cookies: dict[str, str] = {}
    for cookie_name, cookie_value in request.cookies.items():
        if any(name in cookie_name.lower() for name in ["steam", "session", "login"]):
            steam_cookies[cookie_name] = cookie_value
    return "; ".join([f"{k}={v}" for k, v in steam_cookies.items()])


@router.get("/api/steam/check-login")
async def check_steam_login(
    request: Request,
    session: aiohttp.ClientSession = Depends(get_client_session)
) -> Dict[str, Any]:
    """
    检查Steam登录状态
    """
    try:
        # 取 cookie 串
        cookie_string = _extract_cookie_string(request)
        if not cookie_string:
            return {"logged_in": False, "message": "未找到Steam cookies"}
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cookie': cookie_string
        }
        
        # 检查Steam账户页面
        async with session.get(
            'https://store.steampowered.com/account/',
            headers=headers,
            allow_redirects=False
        ) as response:
            
            if response.status == 302:
                # 被重定向到登录页面，说明未登录
                return {"logged_in": False, "message": "需要登录"}
            
            if response.status != 200:
                return {"logged_in": False, "message": f"请求失败: {response.status}"}
            
            html = await response.text()
            
            # 检查是否包含登录用户信息
            is_logged_in = any(indicator in html for indicator in [
                'account_pulldown',
                'playerAvatar', 
                'persona_name',
                'data-miniprofile'
            ])
            
            if is_logged_in:
                # 提取sessionID
                session_id = ""
                session_match = re.search(r'g_sessionID = "(.+?)";', html)
                if session_match:
                    session_id = session_match.group(1)
                
                # 提取用户信息
                username = "已登录"
                username_match = re.search(r'data-miniprofile="(\d+)"', html)
                if username_match:
                    username = f"用户ID: {username_match.group(1)}"
                
                # 尝试提取Steam用户名
                persona_match = re.search(r'persona_name_text_content[^>]*>([^<]+)', html)
                if persona_match:
                    username = persona_match.group(1).strip()
                
                return {
                    "logged_in": True,
                    "username": username,
                    "session_id": session_id
                }
            
            return {"logged_in": False, "message": "未检测到登录状态"}
            
    except Exception as e:
        logger.error(f"检查Steam登录状态失败: {e}")
        return {"logged_in": False, "error": str(e)}

@router.get("/api/steam/session")
async def get_steam_session(
    request: Request,
    session: aiohttp.ClientSession = Depends(get_client_session)
) -> Dict[str, Any]:
    """
    获取Steam会话ID
    """
    try:
        cookie_string = _extract_cookie_string(request)
        if not cookie_string:
            raise HTTPException(status_code=401, detail="未找到Steam登录信息")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Cookie': cookie_string
        }
        
        # 访问Steam激活页面获取sessionID
        async with session.get(
            'https://store.steampowered.com/account/registerkey',
            headers=headers
        ) as response:
            
            if response.status != 200:
                raise HTTPException(status_code=response.status, detail="无法访问Steam激活页面")
            
            html = await response.text()
            
            # 检查是否被重定向到登录页面
            if 'login' in response.url.path and 'g_sessionID' not in html:
                raise HTTPException(status_code=401, detail="Steam会话已过期，请重新登录")
            
            # 提取sessionID
            session_match = re.search(r'g_sessionID = "(.+?)";', html)
            if session_match:
                session_id = session_match.group(1)
                return {"session_id": session_id}
            
            raise HTTPException(status_code=500, detail="无法获取sessionID")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Steam会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取Steam会话失败: {str(e)}")

@router.post("/api/steam/redeem")
async def redeem_steam_key(
    request: Request,
    data: dict,
    session: aiohttp.ClientSession = Depends(get_client_session)
) -> Dict[str, Any]:
    """
    代理Steam Key激活请求
    """
    try:
        product_key = data.get('product_key')
        session_id = data.get('sessionid')
        
        if not product_key or not session_id:
            raise HTTPException(status_code=400, detail="缺少必要参数")
        
        cookie_string = _extract_cookie_string(request)
        if not cookie_string:
            raise HTTPException(status_code=401, detail="未找到Steam登录信息，请先保存Steam Cookie 或在浏览器完成登录")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Origin': 'https://store.steampowered.com',
            'Referer': 'https://store.steampowered.com/account/registerkey',
            'X-Requested-With': 'XMLHttpRequest',
            'Cookie': cookie_string
        }
        
        # 构建表单数据
        form_data = aiohttp.FormData()
        form_data.add_field('product_key', product_key)
        form_data.add_field('sessionid', session_id)
        
        # 发送激活请求
        async with session.post(
            'https://store.steampowered.com/account/ajaxregisterkey/',
            headers=headers,
            data=form_data
        ) as response:
            
            if response.status != 200:
                raise HTTPException(status_code=response.status, detail="Steam激活请求失败")
            
            try:
                result = await response.json()
                return result
            except json.JSONDecodeError:
                text = await response.text()
                raise HTTPException(status_code=500, detail=f"Steam返回非JSON响应: {text[:200]}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Steam Key激活失败: {e}")
        raise HTTPException(status_code=500, detail=f"激活失败: {str(e)}")


@router.post("/api/steam/save-cookies")
async def save_steam_cookies(request: Request):
    """前端粘贴 Steam Cookie 原始串，保存到本站域名 HttpOnly cookie，供后端代理使用。
    注意：前端JS无法读取此cookie，安全性更高；会话级有效，关闭浏览器即失效。"""
    try:
        payload = await request.json()
        raw = (payload.get("cookies", "") or "").strip()
    except Exception:
        raw = ""

    from fastapi.responses import Response
    resp = Response(content="ok")
    if raw:
        # 1) 规范化：接受随意格式（换行/制表/冒号/多空格）并转为 Cookie 请求头格式
        text = raw.replace("\r", "\n")
        # 常见粘贴格式：
        # - "Cookie: a=1; b=2" 带前缀
        # - 行表格：每行一个 name [tab/space] value [tab] Domain ...
        # - "name=value; Path=/; Domain=.steam..." 混杂属性
        if text.lower().startswith("cookie:"):
            text = text.split(":", 1)[1]
        # 将分隔统一为分号
        for sep in ["\n", "\t", ","]:
            text = text.replace(sep, ";")
        # 切片并剔除空段
        parts = [p.strip() for p in text.split(";") if p and p.strip()]
        pairs: list[tuple[str, str]] = []
        blocked_keys = {"path", "domain", "expires", "max-age", "samesite", "secure", "httponly", "priority"}
        for p in parts:
            if "=" not in p:
                continue
            name, value = p.split("=", 1)
            n = name.strip().strip('"').strip("'")
            v = value.strip().strip('"').strip("'")
            ln = n.lower()
            if ln in blocked_keys:
                continue
            if not n or not v:
                continue
            pairs.append((n, v))
        # 只保留常见/必要 cookie 优先顺序，若未给出则保留全部
        preferred = [
            "sessionid", "steamLoginSecure", "steamCountry", "browserid", "timezoneOffset",
            "recentapps", "birthtime", "wants_mature_content"
        ]
        mapping = {k: v for k, v in pairs}
        out_pairs: list[str] = []
        found_any = False
        for k in preferred:
            if k in mapping:
                out_pairs.append(f"{k}={mapping[k]}")
                found_any = True
        if not found_any:
            # 回退：使用全部解析到的键值
            out_pairs = [f"{k}={v}" for k, v in pairs]
        normalized = "; ".join(out_pairs)

        resp.set_cookie(
            key="steam_proxy_cookies",
            value=normalized,
            httponly=True,
            samesite="lax"
        )
    else:
        resp.delete_cookie("steam_proxy_cookies")
    return resp
