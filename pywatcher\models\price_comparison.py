from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class GamePrice(BaseModel):
    """游戏价格数据模型"""
    id: Optional[int] = None
    name: str = Field(..., description="游戏名称")
    price: float = Field(..., description="游戏价格")
    source: str = Field(..., description="价格来源 (fhyx, py)")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class GamePriceCreate(BaseModel):
    """创建游戏价格数据的请求模型"""
    name: str
    price: float
    source: str

class PriceSearchRequest(BaseModel):
    """价格搜索请求模型"""
    search_term: str = Field(..., min_length=1, description="搜索关键词")

class PriceSearchResult(BaseModel):
    """价格搜索结果模型"""
    name: str
    price: float
    source: str
    updated_at: Optional[datetime] = None

class FhyxGameData(BaseModel):
    """fhyx API返回的游戏数据模型"""
    name: str
    price: str
    iscoupon: int
    old_price: str
    goodid: str
    zk: str
    lowest: str
    newlowest: int
    odayid: int
    img_src: str

class FhyxApiResponse(BaseModel):
    """fhyx API响应模型"""
    data: List[FhyxGameData]
    # 根据实际API响应添加其他字段

