/* 热门游戏模态框美化升级 */
#hot-games-modal {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(30, 41, 59, 0.25);
    z-index: 2000;
    backdrop-filter: blur(4px);
    transition: opacity 0.2s;
    display: none;
}
#hot-games-modal .modal-content {
    background: #fff;
    border-radius: 16px;
    border: none;
    box-shadow: 0 12px 32px rgba(0,0,0,0.18);
    max-width: 700px;
    width: 95vw;
    padding: 0;
    overflow: hidden;
    animation: popIn 0.25s cubic-bezier(.4,2,.6,1) 1;
}
@keyframes popIn {
  0% { transform: scale(0.95); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}
#hot-games-modal .modal-header {
    background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
    color: #fff;
    padding: 1.1rem 1.5rem;
    border-bottom: none;
    border-radius: 16px 16px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
#hot-games-modal .modal-title {
    font-weight: 700;
    font-size: 1.25rem;
    letter-spacing: 0.5px;
}
#hot-games-modal .modal-body {
    max-height: 65vh;
    overflow-y: auto;
    padding: 1.5rem 1.2rem 1.2rem 1.2rem;
    background: #f8fafc;
}
#hot-games-modal .game-avatar-small {
    width: 44px;
    height: 44px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(30,41,59,0.10);
    border: 1.5px solid #e0e7ef;
}
#hot-games-modal .table-responsive {
    width: 100%;
    overflow-x: auto;
}
#hot-games-modal .table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0 8px;
    background: transparent;
    min-width: 600px;
}
#hot-games-modal .table th {
    border-top: none;
    border-bottom: 2px solid #2563eb33;
    padding: 0.7rem 0.9rem;
    font-weight: 700;
    background: #f1f5fb;
    color: #1e293b;
    font-size: 1rem;
}
#hot-games-modal .table td {
    vertical-align: middle;
    padding: 0.7rem 0.9rem;
    border-top: none;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(30,41,59,0.04);
    font-size: 0.98rem;
}
#hot-games-modal .table tr {
    transition: background 0.18s;
}
#hot-games-modal .table tr:hover td {
    background: #e0e7ff;
}
#hot-games-modal .btn,
#hot-games-modal button {
    border-radius: 6px;
    font-weight: 500;
    transition: background 0.18s, color 0.18s, box-shadow 0.18s;
    box-shadow: 0 1px 2px rgba(30,41,59,0.06);
    border: none;
    outline: none;
}
#hot-games-modal .btn-primary,
#hot-games-modal .add-to-monitor.btn-primary {
    background: linear-gradient(90deg, #2563eb 0%, #1e40af 100%);
    color: #fff;
    border: none;
}
#hot-games-modal .btn-primary:hover,
#hot-games-modal .add-to-monitor.btn-primary:hover {
    background: linear-gradient(90deg, #1e40af 0%, #2563eb 100%);
    color: #fff;
}
#hot-games-modal .btn-secondary,
#hot-games-modal .add-to-monitor.btn-secondary {
    background: #e5e7eb;
    color: #64748b;
    border: none;
}
#hot-games-modal .btn-secondary:disabled,
#hot-games-modal .add-to-monitor.btn-secondary:disabled {
    background: #f1f5f9;
    color: #a1a1aa;
    opacity: 1;
}
#hot-games-modal .btn-sm {
    padding: 0.32rem 0.9rem;
    font-size: 0.95rem;
}
#hot-games-pagination {
    margin-top: 18px;
    padding-top: 12px;
    border-top: 1.5px solid #e0e7ef;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 18px;
}
#hot-games-pagination .page-indicator {
    font-size: 15px;
    color: #2563eb;
    font-weight: 600;
    letter-spacing: 0.5px;
}
#hot-games-pagination button {
    min-width: 90px;
    font-size: 1rem;
    background: #f1f5fb;
    color: #2563eb;
    border: 1.5px solid #2563eb33;
    border-radius: 6px;
    font-weight: 600;
    transition: background 0.18s, color 0.18s;
}
#hot-games-pagination button:hover:not(:disabled) {
    background: #2563eb;
    color: #fff;
}
#hot-games-pagination button:disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background: #e5e7eb;
    color: #a1a1aa;
}
@media (max-width: 700px) {
    #hot-games-modal .modal-content {
        max-width: 99vw;
        border-radius: 0;
    }
    #hot-games-modal .modal-header {
        border-radius: 0;
        padding: 1rem 0.7rem;
    }
    #hot-games-modal .modal-body {
        padding: 1rem 0.5rem 0.8rem 0.5rem;
    }
    #hot-games-modal .table {
        min-width: 400px;
        font-size: 0.92rem;
    }
    #hot-games-modal .table th, #hot-games-modal .table td {
        padding: 0.5rem 0.5rem;
    }
    #hot-games-pagination {
        gap: 8px;
    }
}

/* 游戏分组标签样式 */
.game-group-badge {
    margin: 0.2rem 0;
}

.game-group-badge small {
    display: inline-block;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    color: #0369a1;
    padding: 0.15rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid #bae6fd;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.game-group-badge small:hover {
    background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
    color: #0c4a6e;
    transform: translateY(-1px);
    transition: all 0.2s ease;
}




