{% extends "shared/base.html" %}

{% block title %}游戏搜索{% endblock %}

{% block content %}
<div class="container">
    <header class="page-header">
        <h1><i data-feather="search"></i> 游戏搜索</h1>
        <p class="text-muted">在 Steam 商店中查找并搜索游戏。</p>
    </header>

    <!-- 搜索卡片 -->
    <article>
        <div class="form-group">
            <label for="game-search-input">游戏名称</label>
            <input type="text" id="game-search-input" placeholder="例如: Counter-Strike 2">
        </div>
        <div class="form-actions">
            <button id="game-search-button" role="button" type="button">
                <i data-feather="search"></i>
                <span>搜索</span>
            </button>
        </div>
    </article>

    <!-- 搜索结果 -->
    <section>
        <h2 class="section-title">搜索结果</h2>
        <div id="search-results-container" class="results-area">
            <p>输入关键词开始搜索...</p>
        </div>
    </section>
</div>

<!-- 游戏卡片模板 (隐藏) -->
<template id="game-card-template">
    <div class="result-card">
        <img class="result-card-image" src="" alt="Game Image">
        <div class="result-card-info">
            <strong class="result-card-name"></strong>
            <span class="text-muted result-card-id"></span>
        </div>
        <!-- 卖家信息按钮 -->
        <button class="sellers-btn" role="button">查看卖家</button>
    </div>
</template>

<!-- 卖家信息模态框 -->
<div id="sellers-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modal-game-title">卖家信息</h3>
            <button class="close-btn" title="关闭">&times;</button>
        </div>
        <div class="modal-body">
            <div id="modal-loading" style="display: none;">正在加载卖家信息...</div>
            <div id="modal-sellers-list"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/app.js') }}"></script>
{% endblock %} 