// 全局变量
window.logSortField = null;
window.logSortDirection = null;
window.logPageNumber = null;
window.chestLogsContainer = null;
window.currentLogsPage = 1;
window.totalLogsPages = 1;
window.LOGS_PAGE_SIZE = 40;
const PAGE_SIZE = 10;

// 肥皂盒收藏相关
const FAVORITE_CHESTS_KEY = 'favorite_chests';

// 防止重复请求的标记
let isLoadingChests = false;
let isLoadingLogs = false;

// 将肥皂盒详情函数暴露为全局函数
window.openChestDetail = null; // 预先声明全局函数变量

// 添加刷新按钮旋转动画样式
const style = document.createElement('style');
style.innerHTML = `
    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    .rotating {
        animation: rotate 1s linear infinite;
    }
`;
document.head.appendChild(style);

/**
 * 全局函数：加载肥皂盒抽取记录
 * @param {string} chestId - 可选，特定肥皂盒ID
 * @returns {Promise} - 返回Promise以支持异步处理
 */
window.loadChestLogs = async function(chestId = null) {
    // 防止同时发起多个请求
    if (isLoadingLogs) {
        console.log('活动记录正在加载中，跳过重复请求');
        return Promise.resolve([]);
    }
    
    isLoadingLogs = true;
    
    try {
        // 获取容器元素
        const logsContainer = document.getElementById('chest-logs-container');
        if (!logsContainer) {
            console.error('找不到chest-logs-container元素');
            return Promise.reject(new Error('找不到日志容器'));
        }
        
        // 获取加载指示器
        const loadingIndicator = logsContainer.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'flex';
        }
        
        // 获取日志内容容器
        const logsContent = document.getElementById('logs-content');
        if (!logsContent) {
            console.error('找不到logs-content元素');
            if (loadingIndicator) loadingIndicator.style.display = 'none';
            return Promise.reject(new Error('找不到日志内容容器'));
        }
        
        console.log('开始加载活动记录...');
        
        // 构建API URL - 使用正确的API路径
        const url = '/api/chests/xboot/detLog/show';
        const params = new URLSearchParams();
        if (chestId) {
            params.append('chestId', chestId);
        }
        params.append('pageNumber', 1);
        params.append('pageSize', 50);
        params.append('sort', 'createTime');
        params.append('order', 'desc');

        const fullUrl = `${url}?${params.toString()}`;
        console.log('API请求URL:', fullUrl);
        
        // 发送API请求
        const response = await fetch(fullUrl);
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // 隐藏加载指示器
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
        
        // 检查API返回数据格式
        let logsData = null;
        if (data && data.result && Array.isArray(data.result.content)) {
            logsData = data.result.content;
        } else if (data && data.result && Array.isArray(data.result.records)) {
            logsData = data.result.records;
        } else if (data && data.data && Array.isArray(data.data.records)) {
            logsData = data.data.records;
        } else {
            console.log('未找到有效的记录数据');
            logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
            return Promise.resolve([]);
        }
        
        if (logsData && logsData.length > 0) {
            renderChestLogs(logsData);
            return Promise.resolve(logsData);
        } else {
            logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
            return Promise.resolve([]);
        }
        
    } catch (error) {
        console.error("加载活动记录时出错:", error);
        const logsContent = document.getElementById('logs-content');
        if (logsContent) {
            logsContent.innerHTML = `<div style="padding: 20px; text-align: center; color: rgba(255, 255, 255, 0.8);">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-bottom: 8px;">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <div>加载失败: ${error.message}</div>
            </div>`;
        }
        
        // 隐藏加载指示器
        const loadingIndicator = document.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
        
        return Promise.reject(error);
    } finally {
        isLoadingLogs = false; // 无论成功或失败，都重置加载状态
    }
};

/**
 * 加载肥皂盒数据
 */
window.loadChests = async function() {
    // 防止重复请求
    if (isLoadingChests) {
        console.log('肥皂盒数据正在加载中，跳过重复请求');
        return Promise.resolve([]);
    }
    
    isLoadingChests = true;
    
    const chestsContainer = document.getElementById('chests-container');
    if (!chestsContainer) {
        isLoadingChests = false;
        return Promise.resolve([]);
    }
    
    // 获取排序和分页参数
    const sortField = document.getElementById('sort-field');
    const sortDirection = document.getElementById('sort-direction');
    const pageNumber = document.getElementById('page-number');
    
    const sort = sortField ? sortField.value : 'sortOrder';
    const order = sortDirection ? sortDirection.value : 'asc';
    const page = pageNumber ? parseInt(pageNumber.value) || 1 : 1;
    
    // 更新当前页
    window.currentPage = page;
    window.updatePageIndicator();
    
    // 显示加载中
    chestsContainer.innerHTML = `
        <div class="loading-indicator">
            <div class="spin"></div>
            <span>正在加载肥皂盒数据...</span>
        </div>
    `;
    
    try {
        // 构建API请求URL
        const url = `/api/chests?page_number=${page}&page_size=${PAGE_SIZE}&sort=${sort}&order=${order}`;
        
        // 发送请求
        const response = await fetch(url);
        
        // 检查响应状态
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
            throw new Error(errorData.detail || `HTTP错误: ${response.status}`);
        }
        
        // 解析响应数据
        const data = await response.json();
        
        // 检查API返回状态
        if (data.code !== 200) {
            throw new Error(data.message || '获取数据失败');
        }
        
        // 获取内容
        const result = data.result || {};
        const content = result.content || [];
        
        // 计算总页数
        if (result.totalElements && result.size) {
            window.totalPages = Math.ceil(result.totalElements / result.size);
        } else {
            window.totalPages = 1;
        }
        
        // 更新分页控件
        window.updatePaginationControls();
        
        // 渲染肥皂盒列表
        window.renderChests(content);
        
        return content; // 返回数据，以便后续处理
    } catch (error) {
        console.error('获取肥皂盒数据失败:', error);
        chestsContainer.innerHTML = `
            <div class="error-message">
                <p>获取肥皂盒数据失败: ${error.message}</p>
                <p>请检查登录状态或稍后再试。</p>
            </div>
        `;
        throw error; // 重新抛出错误，以便调用者处理
    } finally {
        isLoadingChests = false; // 无论成功或失败，都重置加载状态
    }
};

/**
 * 更新页码指示器
 */
window.updatePageIndicator = function() {
    const pageIndicator = document.getElementById('page-indicator');
    if (pageIndicator) {
        pageIndicator.textContent = `第 ${window.currentPage} 页 / 共 ${window.totalPages || 1} 页`;
    }
};

/**
 * 更新分页控件状态
 */
window.updatePaginationControls = function() {
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    
    if (prevPageBtn) prevPageBtn.disabled = window.currentPage <= 1;
    if (nextPageBtn) nextPageBtn.disabled = window.currentPage >= window.totalPages;
    window.updatePageIndicator();
};

/**
 * 获取收藏的肥皂盒列表
 * @returns {Array} 收藏的肥皂盒ID数组
 */
function getFavoriteChests() {
    const favoritesStr = localStorage.getItem(FAVORITE_CHESTS_KEY);
    return favoritesStr ? JSON.parse(favoritesStr) : [];
}

/**
 * 添加肥皂盒到收藏
 * @param {Object} chest - 肥皂盒数据对象
 * @returns {boolean} - 是否添加成功
 */
function addChestToFavorites(chest) {
    if (!chest || !chest.id) return false;
    
    const favorites = getFavoriteChests();
    
    // 检查是否已经在收藏列表中
    if (favorites.some(item => item.id === chest.id)) {
        return false; // 已存在，不重复添加
    }
    
    // 创建简化版的肥皂盒对象，只保存必要的信息
    const simpleChest = {
        id: chest.id,
        name: chest.name || '未知肥皂盒', // 仅保存名称作为显示用
        addTime: new Date().toISOString() // 添加时间
    };
    
    // 添加到收藏列表
    favorites.push(simpleChest);
    
    // 保存到本地存储
    localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(favorites));
    
    // 显示提示
    showToast('已添加到收藏', 'success');
    
    return true;
}

/**
 * 从收藏中移除肥皂盒
 * @param {string} chestId - 肥皂盒ID
 * @returns {boolean} - 是否移除成功
 */
function removeChestFromFavorites(chestId) {
    if (!chestId) return false;
    
    const favorites = getFavoriteChests();
    const initialLength = favorites.length;
    
    // 过滤掉要移除的项目
    const newFavorites = favorites.filter(item => item.id !== chestId);
    
    // 如果长度没变，说明没找到要移除的项目
    if (newFavorites.length === initialLength) {
        return false;
    }
    
    // 保存新的收藏列表
    localStorage.setItem(FAVORITE_CHESTS_KEY, JSON.stringify(newFavorites));
    
    // 显示提示
    showToast('已从收藏中移除', 'info');
    
    return true;
}

/**
 * 检查肥皂盒是否已收藏
 * @param {string} chestId - 肥皂盒ID
 * @returns {boolean} - 是否已收藏
 */
function isChestFavorited(chestId) {
    if (!chestId) return false;
    
    const favorites = getFavoriteChests();
    return favorites.some(item => item.id === chestId);
}

/**
 * 切换肥皂盒收藏状态
 * @param {Object} chest - 肥皂盒数据对象
 * @returns {boolean} - 操作后的收藏状态（true为已收藏，false为未收藏）
 */
function toggleFavoriteChest(chest) {
    if (!chest || !chest.id) return false;
    
    const isFavorited = isChestFavorited(chest.id);
    
    if (isFavorited) {
        removeChestFromFavorites(chest.id);
        return false;
    } else {
        addChestToFavorites(chest);
        return true;
    }
}

/**
 * 渲染肥皂盒列表
 * @param {Array} chests - 肥皂盒数据数组
 */
window.renderChests = function(chests) {
    const chestsContainer = document.getElementById('chests-container');
    const chestItemTemplate = document.getElementById('chest-item-template');
    if (!chestsContainer || !chestItemTemplate) return;
    
    // 如果没有数据
    if (!chests || chests.length === 0) {
        chestsContainer.innerHTML = '<p>没有找到肥皂盒数据。</p>';
        return;
    }
    
    // 清空容器
    chestsContainer.innerHTML = '';
    
    // 创建肥皂盒列表容器
    const chestsListElement = document.createElement('div');
    chestsListElement.className = 'chests-list';
    
    // 遍历肥皂盒数据
    chests.forEach(chest => {
        // 克隆模板
        const chestItem = chestItemTemplate.content.cloneNode(true).firstElementChild;
        
        // 设置肥皂盒ID属性
        const chestId = chest.id || '';
        chestItem.dataset.chestId = chestId;
        
        // 设置内容
        chestItem.querySelector('.chest-name').textContent = chest.name || '未知肥皂盒';
        
        // 设置ID - 新结构
        const chestIdSpan = chestItem.querySelector('.chest-id');
        if (chestIdSpan) {
            chestIdSpan.textContent = chestId;
        }
        
        chestItem.querySelector('.chest-price').textContent = chest.oncePrice ? `¥${chest.oncePrice.toFixed(2)}` : 'N/A';
        
        // 添加卖家信息
        if (chestItem.querySelector('.chest-seller')) {
            chestItem.querySelector('.chest-seller').textContent = chest.nameSeller || '未知卖家';
        }
        
        // 当前未抽 = 总数 - 已抽
        const totalDraw = chest.totalDraw || 0;
        const curDraw = chest.curDraw || 0;
        const remainDraws = totalDraw - curDraw;
        
        chestItem.querySelector('.chest-remain').textContent = remainDraws;
        chestItem.querySelector('.chest-total').textContent = totalDraw;
        
        // 使用rate1作为概率 - 将小数转换为百分比
        const rate1 = chest.rate1 || chest.probability1 || 0;
        // 如果rate1是小数(0-1之间)，则乘以100转为百分比
        const probabilityDisplay = rate1 <= 1 ? 
            `${(rate1 * 100).toFixed(2)}%` : 
            `${parseFloat(rate1).toFixed(2)}%`;
        
        chestItem.querySelector('.chest-probability').textContent = probabilityDisplay;
        
        // 设置收藏按钮
        const isFavorited = isChestFavorited(chestId);
        const favoriteBtn = document.createElement('button');
        favoriteBtn.className = 'favorite-btn';
        favoriteBtn.innerHTML = isFavorited ? 
            '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#FFB800" stroke="#FFB800" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>' : 
            '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>';
        favoriteBtn.style.background = 'transparent';
        favoriteBtn.style.border = 'none';
        favoriteBtn.style.cursor = 'pointer';
        favoriteBtn.style.padding = '4px';
        favoriteBtn.style.borderRadius = '50%';
        favoriteBtn.style.marginLeft = '8px';
        favoriteBtn.title = isFavorited ? '取消收藏' : '添加到收藏';
        
        // 添加收藏按钮点击事件
        favoriteBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            const newStatus = toggleFavoriteChest(chest);
            
            // 更新按钮状态
            this.innerHTML = newStatus ? 
                '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="#FFB800" stroke="#FFB800" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>' : 
                '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path></svg>';
            this.title = newStatus ? '取消收藏' : '添加到收藏';
        });
        
        // 将收藏按钮添加到标题区域
        const chestTitle = chestItem.querySelector('.chest-title');
        chestTitle.style.display = 'flex';
        chestTitle.style.alignItems = 'center';
        chestTitle.appendChild(favoriteBtn);
        
        // 移除徽章显示
        const badge = chestItem.querySelector('.chest-badge');
        if (badge) {
            badge.style.display = 'none';
        }
        
        // 给名称点击事件 - 跳转到详情页面
        const nameElement = chestItem.querySelector('.chest-name');
        if (nameElement) {
            nameElement.style.cursor = 'pointer';
            nameElement.title = '点击查看详情页面';
            nameElement.addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡到整个卡片
                window.location.href = `/chest-detail?id=${chest.id}`;
            });
        }
        
        // 添加点击事件，跳转到详情页面
        chestItem.addEventListener('click', function() {
            window.location.href = `/chest-detail?id=${chest.id}`;
        });
        
        // 添加到容器
        chestsListElement.appendChild(chestItem);
    });
    
    // 将列表添加到容器
    chestsContainer.appendChild(chestsListElement);
};

/**
 * 渲染肥皂盒记录
 * @param {Array} logs - 肥皂盒记录数据数组
 */
function renderChestLogs(logs) {
    // 获取新的列表容器
    const logsContent = document.getElementById('logs-content');
    if (!logsContent) {
        console.error('找不到logs-content元素');
        return;
    }
    
    // 清空现有内容
    logsContent.innerHTML = '';
    
    // 检查是否有记录
    if (!logs || logs.length === 0) {
        logsContent.innerHTML = '<div style="padding: 20px; text-align: center; color: rgba(255,255,255,0.8);">暂无活动记录</div>';
        return;
    }
    
    // 创建记录列表
    const logsList = document.createElement('div');
    logsList.className = 'activity-list';
    
    // 遍历所有记录数据，而不是只取两条
    logs.forEach((log) => {
        const gameName = log.gameName || '未知游戏';
        const chestName = log.name || '未知肥皂盒';
        const createTime = log.createTime ?
            new Date(log.createTime).toLocaleString('zh-CN', {
                year: 'numeric', month: '2-digit', day: '2-digit',
                hour: '2-digit', minute: '2-digit', second: '2-digit'
            }).replace(/\//g, '-') : '未知时间';
        
        // 获取游戏名称的第一个字符作为图标
        const gameInitial = gameName.charAt(0);
        
        // 创建记录项
        const logItem = document.createElement('div');
        logItem.className = 'activity-item';
        logItem.style.display = 'flex';
        logItem.style.alignItems = 'center';
        logItem.style.padding = '12px 24px';
        logItem.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
        logItem.style.transition = 'background-color 0.2s ease';
        
        // 添加悬停效果
        logItem.addEventListener('mouseenter', () => {
            logItem.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
        });
        logItem.addEventListener('mouseleave', () => {
            logItem.style.backgroundColor = 'transparent';
        });
        
        // 创建游戏图标
        const gameIcon = document.createElement('div');
        gameIcon.className = 'game-icon';
        gameIcon.style.width = '36px';
        gameIcon.style.height = '36px';
        gameIcon.style.borderRadius = '50%';
        gameIcon.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
        gameIcon.style.display = 'flex';
        gameIcon.style.justifyContent = 'center';
        gameIcon.style.alignItems = 'center';
        gameIcon.style.marginRight = '16px';
        gameIcon.style.flexShrink = '0';
        gameIcon.style.fontSize = '18px';
        gameIcon.style.fontWeight = '600';
        gameIcon.textContent = gameInitial;
        
        // 创建内容容器
        const contentDiv = document.createElement('div');
        contentDiv.className = 'activity-content';
        contentDiv.style.flex = '1';
        
        // 游戏名称
        const gameNameElem = document.createElement('div');
        gameNameElem.className = 'game-name';
        gameNameElem.style.fontSize = '16px';
        gameNameElem.style.fontWeight = '600';
        gameNameElem.style.marginBottom = '4px';
        gameNameElem.textContent = gameName;
        
        // 肥皂盒信息和时间
        const infoElem = document.createElement('div');
        infoElem.className = 'activity-info';
        infoElem.style.fontSize = '14px';
        infoElem.style.color = 'rgba(255, 255, 255, 0.7)';
        infoElem.style.display = 'flex';
        infoElem.style.justifyContent = 'space-between';
        
        const chestInfo = document.createElement('span');
        chestInfo.textContent = chestName;
        
        const timeInfo = document.createElement('span');
        timeInfo.textContent = createTime;
        timeInfo.style.opacity = '0.7';
        
        infoElem.appendChild(chestInfo);
        infoElem.appendChild(timeInfo);
        
        // 组装内容
        contentDiv.appendChild(gameNameElem);
        contentDiv.appendChild(infoElem);
        
        // 组装项目
        logItem.appendChild(gameIcon);
        logItem.appendChild(contentDiv);
        
        // 添加到列表
        logsList.appendChild(logItem);
    });
    
    // 将列表添加到容器
    logsContent.appendChild(logsList);
}

/**
 * 确保页面中有marquee动画的CSS定义
 */
function ensureMarqueeAnimation() {
    // 不再需要此功能，但保留函数以兼容现有代码
}

// 移除旧的事件监听器，避免重复加载
document.removeEventListener('DOMContentLoaded', window.domLoadHandler);
window.removeEventListener('load', window.loadHandler);

// 仅使用一个统一的初始化函数
document.addEventListener('DOMContentLoaded', async function initPage() {
    try {
        // 移除监听器，确保只执行一次
        document.removeEventListener('DOMContentLoaded', initPage);
        console.log('DOM已加载完成，开始初始化页面...');
        
        // 初始化加载常规肥皂盒数据
        await window.loadChests();
        console.log('已触发常规肥皂盒数据加载');
        
        // 立即加载抽取记录，不使用setTimeout延迟
        console.log('准备加载抽取记录...');
        await window.loadChestLogs();
        
        // 添加刷新按钮事件
        const refreshLogsBtn = document.getElementById('refresh-logs-btn');
        if (refreshLogsBtn) {
            refreshLogsBtn.addEventListener('click', async function() {
                // 添加旋转动画
                this.classList.add('rotating');
                try {
                    // 重新加载活动记录
                    await window.loadChestLogs();
                } finally {
                    // 无论成功或失败，1秒后移除旋转动画
                    setTimeout(() => {
                        this.classList.remove('rotating');
                    }, 1000);
                }
            });
        }
        
        // 检查URL参数，是否需要打开特定肥皂盒详情
        const urlParams = new URLSearchParams(window.location.search);
        const openChestId = urlParams.get('open_chest');
        if (openChestId) {
            console.log('检测到open_chest参数，跳转到详情页面:', openChestId);
            // 直接跳转到肥皂盒详情页面
            window.location.href = `/chest-detail?id=${openChestId}`;
        }
    } catch (error) {
        console.error('页面初始化失败:', error);
    }
});

// 移除window load事件后备，避免重复调用
document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const sortField = document.getElementById('sort-field');
    const sortDirection = document.getElementById('sort-direction');
    const pageNumber = document.getElementById('page-number');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');

    // 排序和分页控制事件监听
    if (sortField) sortField.addEventListener('change', window.loadChests);
    if (sortDirection) sortDirection.addEventListener('change', window.loadChests);
    if (pageNumber) pageNumber.addEventListener('change', handlePageChange);
    if (prevPageBtn) prevPageBtn.addEventListener('click', goToPrevPage);
    if (nextPageBtn) nextPageBtn.addEventListener('click', goToNextPage);

    // 分页处理函数
    function handlePageChange() {
        if (!pageNumber) return;
        let page = parseInt(pageNumber.value) || 1;
        if (page < 1) page = 1;
        if (window.totalPages && page > window.totalPages) page = window.totalPages;
        pageNumber.value = page;
        window.loadChests();
    }
    function goToPrevPage() {
        if (!pageNumber) return;
        let page = parseInt(pageNumber.value) || 1;
        if (page > 1) {
            page--;
            pageNumber.value = page;
            window.loadChests();
        }
    }
    function goToNextPage() {
        if (!pageNumber) return;
        let page = parseInt(pageNumber.value) || 1;
        if (window.totalPages && page < window.totalPages) {
            page++;
            pageNumber.value = page;
            window.loadChests();
        }
    }
});

/**
 * 显示提示消息
 * @param {string} message - 提示消息
 * @param {string} type - 消息类型 (success, error, warning)
 */
function showToast(message, type = 'success') {
    // 检查是否已存在toast容器，如果不存在则创建
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        document.body.appendChild(toastContainer);
    }
    
    // 创建新的toast元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加到容器
    toastContainer.appendChild(toast);
    
    // 设置定时器移除
    setTimeout(() => {
        toast.classList.add('toast-hide');
        setTimeout(() => {
            if (toastContainer.contains(toast)) {
                toastContainer.removeChild(toast);
            }
        }, 300); // 淡出动画后移除
    }, 3000); // 3秒后开始淡出
} 