#!/usr/bin/env python3
"""
测试价格比较功能的脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pywatcher.crud.price_comparison_manager import get_price_manager
from pywatcher.models.price_comparison import GamePriceCreate

async def test_price_comparison():
    """测试价格比较功能"""
    print("🧪 开始测试价格比较功能...")
    
    try:
        # 获取价格管理器
        price_manager = await get_price_manager()
        print("✅ 价格管理器初始化成功")
        
        # 测试数据
        test_games = [
            GamePriceCreate(name="黑神话：悟空", price=268.0, source="py"),
            GamePriceCreate(name="黑神话：悟空", price=258.0, source="fhyx"),
            GamePriceCreate(name="赛博朋克2077", price=128.0, source="py"),
            GamePriceCreate(name="赛博朋克2077", price=120.0, source="fhyx"),
            GamePriceCreate(name="艾尔登法环", price=298.0, source="py"),
        ]
        
        # 添加测试数据
        print("\n📝 添加测试游戏价格数据...")
        for game in test_games:
            result = await price_manager.add_game_price(game)
            print(f"  ✅ 添加: {result.name} ({result.source}) - ¥{result.price}")
        
        # 测试搜索功能
        print("\n🔍 测试搜索功能...")
        search_results = await price_manager.search_games("黑神话")
        print(f"  搜索 '黑神话' 找到 {len(search_results)} 条结果:")
        for result in search_results:
            print(f"    - {result.name} ({result.source}): ¥{result.price}")
        
        # 测试获取所有游戏
        print("\n📊 获取所有游戏数据...")
        all_games = await price_manager.get_all_games(limit=10)
        print(f"  找到 {len(all_games)} 条游戏记录")
        
        # 测试统计功能
        print("\n📈 获取数据库统计信息...")
        stats = await price_manager.get_stats()
        print(f"  总记录数: {stats['total']}")
        print(f"  按来源统计: {stats['by_source']}")
        print(f"  最后更新: {stats['last_update']}")
        
        print("\n🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_price_comparison())





