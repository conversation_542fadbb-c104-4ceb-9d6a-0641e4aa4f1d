import aiosqlite
from aiofiles.os import makedirs
import os
from pathlib import Path

# 定义数据库文件路径
# 使用 Path 对象确保跨平台兼容性
BASE_DIR = Path(__file__).resolve().parent.parent
DATABASE_URL = str(BASE_DIR / "data" / "pywatcher.db")


async def get_db_conn():
    """获取数据库连接"""
    # aiosqlite.connect 实际上是异步的，尽管没有 await
    # connect 本身返回一个 future/awaitable
    conn = await aiosqlite.connect(DATABASE_URL)
    conn.row_factory = aiosqlite.Row
    return conn


async def db_init():
    """初始化数据库和表 (异步版)"""
    db_dir = os.path.dirname(DATABASE_URL)
    # 异步地创建目录
    await makedirs(db_dir, exist_ok=True)
    
    async with aiosqlite.connect(DATABASE_URL) as db:
        # The game_prices table is no longer needed.
        # If other tables were here, they would remain.
        pass 