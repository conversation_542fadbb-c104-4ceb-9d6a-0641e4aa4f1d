import aiohttp
import json
from fastapi import Depends, HTTPException, status
from typing import AsyncGenerator, Optional, Dict, Any
import logging

from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.crud.cookie_pool_manager import CookiePoolManager
from pywatcher.models.cookie_pool import MobileAccount, MobileCookiePool
from pywatcher.utils.file_io import read_json_file

logger = logging.getLogger(__name__)

# 全局共享的 aiohttp.ClientSession
# 将在应用启动和关闭时进行管理
client_session: Optional[aiohttp.ClientSession] = None

# --- Reusable Aiohttp Client Session ---
_CLIENT_SESSION: aiohttp.ClientSession | None = None

async def get_client_session() -> aiohttp.ClientSession:
    """
    提供一个可重用的 aiohttp.ClientSession 实例。
    在应用启动时创建，在应用关闭时销毁。
    """
    global _CLIENT_SESSION
    if _CLIENT_SESSION is None or _CLIENT_SESSION.closed:
        # 在实践中，这个 session 应该在应用生命周期事件中被管理 (startup/shutdown)
        # 这里为了简化，我们在第一次需要时创建它
        logger.info("Creating new aiohttp.ClientSession.")
        _CLIENT_SESSION = aiohttp.ClientSession()
    return _CLIENT_SESSION


# --- Data Migration Helper ---
def _migrate_account_data(account_data: Dict[str, Any]) -> Dict[str, Any]:
    """将单条账户数据从旧格式迁移到新格式以进行验证"""
    if "account" not in account_data:
        account_data["account"] = account_data.get("name", "")
    if "password" not in account_data:
        account_data["password"] = ""
    if "token_expire_time" not in account_data:
        account_data["token_expire_time"] = None
    if "status" not in account_data:
        account_data["status"] = 1
    if "remark" not in account_data or not account_data.get("remark"):
        account_data["remark"] = account_data.get("name", "")
    
    # 转换旧的account_type到新的枚举值
    if "account_type" in account_data:
        old_type = account_data["account_type"]
        if old_type == "primary":
            account_data["account_type"] = "query"  # 将主账号转为查询账号
        elif old_type == "order_dedicated":
            account_data["account_type"] = "order_primary"  # 将专用下单账号转为下单主账号
    
    return account_data

# --- Main Dependency ---
def get_cookie_pool_manager() -> CookiePoolManager:
    """
    依赖注入函数，用于加载、迁移并提供 CookiePoolManager 实例。
    这个函数是应用中获取账户数据的唯一入口点。
    """
    mobile_pool_path = "pywatcher/Mobile_cookie_pool.json"
    accounts_data = []
    try:
        data = read_json_file(mobile_pool_path)
        if data and "accounts" in data:
            accounts_data = [
                _migrate_account_data(acc) for acc in data["accounts"]
            ]
    except (FileNotFoundError, json.JSONDecodeError):
        # 如果文件不存在或无效，则使用空列表继续
        pass

    # 使用迁移后的数据创建 Pydantic 模型实例
    mobile_pool = MobileCookiePool(accounts=accounts_data)
    
    # CookiePoolManager 现在用一个有效的、内存中的对象初始化
    # 注意：我们在这里不使用 Singleton 模式，以确保每次请求都获得最新的数据
    # 如果需要跨请求共享状态，应考虑不同的实现方式
    return CookiePoolManager(pool=mobile_pool, file_path=mobile_pool_path)


async def get_steam_py_client(
    cookie_manager: CookiePoolManager = Depends(get_cookie_pool_manager),
    session: aiohttp.ClientSession = Depends(get_client_session)
) -> SteamPyClient:
    """
    依赖注入函数，用于创建和提供 SteamPyClient 实例。
    它会从 CookiePoolManager 获取查询账号类型的账号的 access_token。
    """
    logger.debug("Attempting to get query account for SteamPyClient.")
    try:
        # 查找查询账号
        query_account = None
        all_accounts = cookie_manager.get_all_mobile_accounts()
        
        # 优先选择查询账号类型
        for account in all_accounts:
            if account.account_type == "query":
                query_account = account
                break
        
        # 如果没有查询账号，尝试使用下单主账号
        if not query_account:
            for account in all_accounts:
                if account.account_type == "order_primary":
                    query_account = account
                    break
        
        # 如果还是没有，使用任何可用账号
        if not query_account and all_accounts:
            query_account = all_accounts[0]
        
        if not query_account:
            logger.error("No available accounts in the cookie pool.")
            raise HTTPException(
                status_code=401,
                detail="未配置任何账户，无法与 Steam API 交互。"
            )

        if not query_account.access_token:
            logger.error(f"Account '{query_account.name}' does not have an access token.")
            raise HTTPException(
                status_code=401,
                detail=f"账户 '{query_account.name}' 未登录 (缺少 access_token)。"
            )
        
        logger.info(f"Creating SteamPyClient for account: {query_account.name} (type: {query_account.account_type})")
        return SteamPyClient(session=session, access_token=query_account.access_token)

    except Exception as e:
        logger.error(f"Failed to create SteamPyClient: {e}", exc_info=True)
        # 重新抛出，让 FastAPI 的异常处理中间件捕获它
        raise


def get_steam_client_for_login() -> SteamPyClient:
    """为登录过程提供一个无需 access_token 的 SteamPyClient 实例。"""
    return SteamPyClient(access_token=None) 