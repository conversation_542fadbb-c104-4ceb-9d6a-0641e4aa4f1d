from fastapi import APIRouter, Depends, Query, HTTPException, Body
from typing import List, Dict, Any
import logging

from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.api.dependencies import get_steam_py_client
from pywatcher.crud import config_manager
from pywatcher.models.config import GameFavorite
from pywatcher.models.game import Game, GameFavoriteCreate, GameFavoriteReorder

router = APIRouter(
    prefix="/games",
    tags=["Games"]
)

@router.get("/search", response_model=List[Game], response_model_by_alias=False)
async def search_games(
    term: str = Query(..., min_length=1, description="要搜索的游戏名称", alias="query"),
    client: SteamPyClient = Depends(get_steam_py_client)
):
    """
    根据关键词在 steampy.com 搜索游戏。
    """
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据 (accesstoken)。")

    try:
        logging.info(f"正在搜索游戏: {term}")
        results = await client.search_game(game_name=term)
        
        if results and isinstance(results, dict) and results.get("code") == 200:
            result_data = results.get("result", {})
            return result_data.get("content", []) if isinstance(result_data, dict) else []
        
        # 处理API业务错误或意外格式
        error_msg = results.get("message", "游戏搜索失败") if isinstance(results, dict) else "上游API响应格式无效"
        status_code = results.get("code", 502) if isinstance(results, dict) else 502
        logging.warning(f"搜索游戏API响应异常: {results}")
        raise HTTPException(status_code=status_code, detail=error_msg)

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"搜索游戏时发生意外错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="搜索游戏时发生内部服务器错误。")


# --- Favorite Games Endpoints ---

@router.get("/favorites", response_model=List[GameFavorite])
async def list_favorites():
    """获取所有收藏的游戏"""
    return config_manager.get_favorite_games()


@router.post("/favorites", response_model=GameFavorite, status_code=201)
async def add_favorite(
    game_create: GameFavoriteCreate = Body(...)
):
    """将一个游戏添加到收藏列表"""
    # 将传入的 GameFavoriteCreate 转换为内部存储用的 GameFavorite
    game_to_save = GameFavorite.model_validate(game_create.model_dump())
    
    await config_manager.add_favorite_game(game_to_save)
    return game_to_save


@router.delete("/favorites/{game_id}", status_code=204)
async def remove_from_favorites(game_id: str):
    """从收藏列表中移除一个游戏"""
    success = await config_manager.remove_favorite_game(game_id)
    if not success:
        raise HTTPException(status_code=404, detail="收藏的游戏未找到")
    return None # 204 No Content 响应体应为空


@router.post("/favorites/reorder", status_code=200, response_model=Dict[str, str])
async def reorder_favorites(payload: GameFavoriteReorder = Body(...)):
    """更新收藏列表的游戏顺序"""
    await config_manager.update_favorite_games_order(payload.ordered_game_ids)
    return {"message": "收藏列表顺序已更新"}


# --- Seller Info Endpoint ---

@router.get("/{game_id}/sellers", response_model=List[Dict[str, Any]])
async def get_game_sellers(
    game_id: str,
    client: SteamPyClient = Depends(get_steam_py_client)
):
    """获取指定游戏的卖家列表"""
    if not client.access_token:
        logging.warning(f"尝试获取游戏 {game_id} 的卖家列表，但未提供有效的认证凭据。")
        raise HTTPException(status_code=401, detail="未登录，请先登录后再使用此功能。")

    logging.info(f"正在获取游戏 {game_id} 的卖家列表")
    try:
        response = await client.get_game_sellers(game_id=game_id)
        
        if response and response.get("code") == 200:
            content = response.get("result", {}).get("content", [])
            logging.info(f"成功获取游戏 {game_id} 的 {len(content)} 个卖家信息")
            return content
        elif response and response.get("code") == 401:
            logging.warning(f"获取卖家列表失败: 认证失败 (401). 错误信息: {response.get('message')}")
            raise HTTPException(status_code=401, detail=response.get("message", "认证失败或令牌无效。"))
        elif response and response.get("code") != 200:
            logging.warning(f"获取卖家列表失败，错误码 {response.get('code')}. 错误信息: {response.get('message')}")
            raise HTTPException(
                status_code=response.get("code", 400), 
                detail=response.get("message", f"获取游戏 {game_id} 的卖家列表失败。")
            )
        elif response and "error" in response:
            logging.error(f"获取卖家列表失败，客户端错误: {response.get('error')}. 详情: {response.get('details')}")
            raise HTTPException(status_code=502, detail={"message": "API请求失败", "error": response["error"], "details": response.get("details")})
        else:
            logging.error(f"获取游戏 {game_id} 卖家列表时，API客户端返回了意外的空或无效响应。")
            raise HTTPException(status_code=500, detail=f"获取游戏 {game_id} 卖家列表时发生意外错误。")
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"获取卖家列表时发生意外错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取卖家列表时发生内部错误: {str(e)}") 