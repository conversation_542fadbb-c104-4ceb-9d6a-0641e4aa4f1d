{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}账户管理 - PyWatcher{% endblock %}

{% block content %}
<header class="page-header">
    <h1>{{ icon('users') }} 账户管理</h1>
    <p class="page-description">管理您的Web端和移动端账户凭据，确保系统正常运行。</p>
</header>

<div class="accounts-container">
    <!-- Web账户池功能已禁用 -->
    <!-- 
    <section class="account-section" style="display: none;">
        Web账户相关功能已被移除
    </section>
    -->

    <!-- 移动端账户 -->
    <section class="account-section">
        <div class="section-header">
            <div class="title-group">
                <h2>{{ icon('smartphone') }} 移动端账户</h2>
                <span class="subtitle">SMS短信验证登录</span>
            </div>
            <div class="action-buttons">
                <button id="refresh-mobile-accounts-btn" class="btn btn-outline">
                    {{ icon('refresh-cw') }}
                </button>
                <button id="register-btn" class="btn btn-primary">
                    {{ icon('user-plus') }} 注册账户
                </button>
            </div>
        </div>
        
        <div class="login-form-card">
            <form id="sms-login-form" class="compact-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">手机号</label>
                        <input type="tel" id="phone" name="phone" placeholder="请输入手机号" required>
                    </div>
                    <div class="form-group">
                        <label for="code">验证码</label>
                        <input type="text" id="code" name="code" placeholder="请输入验证码" required>
                    </div>
                    <div class="form-group">
                        <button type="button" id="send-sms-btn" class="btn btn-secondary">发送验证码</button>
                        <button type="submit" class="btn btn-success">{{ icon('log-in') }} 登录</button>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="accounts-list">
            <div id="mobile-account-list-container">
                <div class="loading-placeholder">
                    <div class="spinner"></div>
                    <span>正在加载移动账户...</span>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- 注册模态框 -->
<div id="register-modal" class="modal" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ icon('user-plus') }} 注册新账户</h5>
                <button type="button" class="close" onclick="document.getElementById('register-modal').style.display='none'">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="register-form">
                    <div class="form-group">
                        <label for="register-phone">手机号</label>
                        <input type="tel" id="register-phone" class="form-control" placeholder="请输入手机号" required>
                    </div>
                    
                    <div class="form-group">
                        <label>图形验证码</label>
                        <button type="button" id="get-captcha-btn" class="btn btn-outline">获取验证码</button>
                    </div>
                    
                    <div id="captcha-container" style="display: none;">
                        <div class="form-group">
                            <img id="captcha-image" class="captcha-image">
                        </div>
                        <div class="form-group">
                            <label for="captcha-code">验证码</label>
                            <input type="text" id="captcha-code" class="form-control" placeholder="请输入图形验证码">
                        </div>
                        <div class="form-group">
                            <button type="button" id="send-register-sms-btn" class="btn btn-primary">发送注册短信</button>
                        </div>
                    </div>
                    
                    <div id="sms-code-container" style="display: none;">
                        <div class="form-group">
                            <label for="register-sms-code">短信验证码</label>
                            <input type="text" id="register-sms-code" class="form-control" placeholder="请输入短信验证码" required>
                        </div>
                        <div class="form-group">
                            <label for="register-remark">备注名称</label>
                            <input type="text" id="register-remark" class="form-control" placeholder="可选，默认为手机号后4位">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="document.getElementById('register-modal').style.display='none'">取消</button>
                <button type="submit" form="register-form" class="btn btn-primary">完成注册</button>
            </div>
        </div>
    </div>
</div>
<style>
/* 主容器样式 */
.accounts-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* 账户节区样式 */
.account-section {
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 节区头部样式 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-color-dark));
    color: white;
    border-bottom: 1px solid var(--border-color);
}

.title-group h2 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.subtitle {
    display: block;
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 0.25rem;
}

.action-buttons {
    display: flex;
    gap: 0.75rem;
}

.action-buttons .btn {
    border-color: rgba(255,255,255,0.3);
    color: white;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.action-buttons .btn:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

/* 登录表单卡片 */
.login-form-card {
    padding: 2rem;
    background: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
}

.compact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-group input {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 验证码特殊样式 */
.captcha-group {
    grid-column: 1 / -1;
}

.captcha-input-group {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.captcha-input-group input {
    flex: 1;
    min-width: 120px;
}

.captcha-image {
    max-width: 120px;
    height: auto;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: #f8f9fa;
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid transparent;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-outline {
    background: transparent;
    border-color: var(--border-color);
    color: var(--text-color);
}

.btn-outline:hover {
    background: var(--hover-bg);
    border-color: var(--primary-color);
}

/* 账户列表样式 */
.accounts-list {
    padding: 2rem;
    min-height: 200px;
}

.loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 3rem;
    color: var(--text-muted);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 账户卡片样式 */
.account-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 0.75rem;
    transition: all 0.2s;
}

.account-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.account-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.account-name {
    font-weight: 500;
    color: var(--text-color);
}

.account-meta {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.account-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background: var(--success-bg);
    color: var(--success-color);
}

.status-inactive {
    background: var(--warning-bg);
    color: var(--warning-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .accounts-container {
        gap: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
        padding: 1rem;
    }
    
    .login-form-card {
        padding: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .captcha-input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .accounts-list {
        padding: 1rem;
    }
}

/* 模态框样式优化 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: var(--primary-color);
    color: white;
    border-radius: 12px 12px 0 0;
}

.modal-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* CSS 变量定义 */
:root {
    --primary-color: #3b82f6;
    --primary-color-dark: #2563eb;
    --secondary-color: #6b7280;
    --success-color: #10b981;
    --success-bg: #d1fae5;
    --warning-color: #f59e0b;
    --warning-bg: #fef3c7;
    --card-bg: #ffffff;
    --bg-color: #f8fafc;
    --text-color: #1f2937;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --hover-bg: #f3f4f6;
}

[data-theme="dark"] {
    --primary-color: #60a5fa;
    --primary-color-dark: #3b82f6;
    --card-bg: #1f2937;
    --bg-color: #111827;
    --text-color: #f9fafb;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --hover-bg: #374151;
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/accounts.js') }}" defer></script>
{% endblock %} 
