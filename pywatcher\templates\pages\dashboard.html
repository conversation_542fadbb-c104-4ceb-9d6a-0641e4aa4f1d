{% from "macros/icons.html" import icon %}
{% extends "shared/base.html" %}

{% block title %}仪表盘 - PyWatcher{% endblock %}

{% block head %}
<style>
/* 特殊模式设置样式 */
.special-mode-config {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.special-setting-group {
    margin-top: 20px;
    padding: 15px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.special-setting-group h5 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.form-checkbox {
    margin-right: 8px;
}

.help-text {
    display: block;
    margin-top: 5px;
    color: #6c757d;
    font-size: 0.875rem;
}

/* 特殊模式状态指示器 */
.special-mode-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    z-index: 1000;
    font-size: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 增强设置组的视觉层次 */
.setting-group h4 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
    margin-bottom: 20px;
}

.setting-group h4 small {
    color: #7f8c8d;
    font-weight: normal;
}
</style>
{% endblock %}

{% block content %}
<header class="page-header fade-in">
    <h1>仪表盘</h1>
    <p class="page-description">欢迎来到 PyWatcher 控制台。</p>
</header>

<div class="layout-grid">
    <div class="main-content fade-in">
        <div class="card card-constrained">
            <header class="article-header">
                <h2>{{ icon('settings') }} 系统设置</h2>
            </header>
            <div>
                <div class="settings-section">
                    <h3>下单配置</h3>
                    
                    <!-- CDK下单配置 -->
                    <div class="setting-group">
                        <h4>CDK下单设置</h4>
                        
                        <div class="account-switch-container">
                            <label>CDK下单账号：</label>
                            <div class="account-switch">
                                <input type="checkbox" id="cdk-account-switch" class="account-switch-checkbox">
                                <label for="cdk-account-switch" class="account-switch-label">
                                    <span class="account-switch-inner"></span>
                                    <span class="account-switch-switch"></span>
                                </label>
                                <span id="cdk-account-switch-text">主账号</span>
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="cdk-pay-type">支付方式</label>
                                <select id="cdk-pay-type" class="form-control">
                                    <option value="AU">AU</option>
                                    <option value="AA">AA</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="cdk-use-balance">使用余额</label>
                                <select id="cdk-use-balance" class="form-control">
                                    <option value="">不使用</option>
                                    <option value="useBalance">使用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 肥皂盒下单配置 -->
                    <div class="setting-group">
                        <h4>肥皂盒下单设置</h4>
                        
                        <div class="account-switch-container">
                            <label>肥皂盒下单账号：</label>
                            <div class="account-switch">
                                <input type="checkbox" id="chest-account-switch" class="account-switch-checkbox">
                                <label for="chest-account-switch" class="account-switch-label">
                                    <span class="account-switch-inner"></span>
                                    <span class="account-switch-switch"></span>
                                </label>
                                <span id="chest-account-switch-text">主账号</span>
                            </div>
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="chest-pay-type">支付方式</label>
                                <select id="chest-pay-type" class="form-control">
                                    <option value="AA">AA</option>
                                    <option value="py">py</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="chest-use-balance">使用余额</label>
                                <select id="chest-use-balance" class="form-control">
                                    <option value="">不使用</option>
                                    <option value="useBalance">使用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 特殊模式设置 -->
                    <div class="setting-group">
                        <h4>特殊模式设置 <small>(全天候生效)</small></h4>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable-special-mode" class="form-checkbox">
                                启用特殊模式
                            </label>
                            <small class="help-text">启用后，saleid不带k的商品将使用特殊模式配置下单，带k的商品仍使用上方正常配置</small>
                        </div>
                        
                        <div id="special-mode-settings" class="special-mode-config" style="display: none;">
                            
                            <!-- 特殊模式CDK下单配置 -->
                            <div class="special-setting-group">
                                <h5>特殊模式CDK下单设置</h5>
                                
                                <div class="account-switch-container">
                                    <label>特殊模式CDK下单账号：</label>
                                    <div class="account-switch">
                                        <input type="checkbox" id="special-cdk-account-switch" class="account-switch-checkbox">
                                        <label for="special-cdk-account-switch" class="account-switch-label">
                                            <span class="account-switch-inner"></span>
                                            <span class="account-switch-switch"></span>
                                        </label>
                                        <span id="special-cdk-account-switch-text">主账号</span>
                                    </div>
                                </div>
                                
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="special-cdk-pay-type">特殊模式支付方式</label>
                                        <select id="special-cdk-pay-type" class="form-control">
                                            <option value="AU">AU</option>
                                            <option value="AA">AA</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="special-cdk-use-balance">特殊模式使用余额</label>
                                        <select id="special-cdk-use-balance" class="form-control">
                                            <option value="">不使用</option>
                                            <option value="useBalance">使用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 特殊模式肥皂盒下单配置 -->
                            <div class="special-setting-group">
                                <h5>特殊模式肥皂盒下单设置</h5>
                                
                                <div class="account-switch-container">
                                    <label>特殊模式肥皂盒下单账号：</label>
                                    <div class="account-switch">
                                        <input type="checkbox" id="special-chest-account-switch" class="account-switch-checkbox">
                                        <label for="special-chest-account-switch" class="account-switch-label">
                                            <span class="account-switch-inner"></span>
                                            <span class="account-switch-switch"></span>
                                        </label>
                                        <span id="special-chest-account-switch-text">主账号</span>
                                    </div>
                                </div>
                                
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="special-chest-pay-type">特殊模式支付方式</label>
                                        <select id="special-chest-pay-type" class="form-control">
                                            <option value="AA">AA</option>
                                            <option value="py">py</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="special-chest-use-balance">特殊模式使用余额</label>
                                        <select id="special-chest-use-balance" class="form-control">
                                            <option value="">不使用</option>
                                            <option value="useBalance">使用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button id="save-order-settings" class="btn btn-primary">保存设置</button>
                    </div>
                </div>
                
                <div class="shortcuts">
                    <a href="/favorites" class="favorites-link">
                        {{ icon('star') }} 查看我的游戏收藏
                    </a>
                    <a href="/favorite-chests" class="favorites-link">
                        {{ icon('package') }} 查看我的肥皂盒收藏
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 添加订单记录卡片 -->
        <div class="card card-constrained">
            <header class="article-header">
                <h2>{{ icon('shopping-cart') }} 下单记录</h2>
            </header>
            <div>
                <div class="orders-list-wrapper">
                    <div id="orders-loading" class="loading-indicator">
                        <div class="spin"></div><span>正在加载订单记录...</span>
                    </div>
                    <div id="orders-error" class="error-indicator" style="display:none;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg><span>加载订单记录失败</span>
                    </div>
                    <div id="orders-empty" class="empty-notice" style="display:none;">
                        <p>暂无订单记录</p>
                    </div>
                    <table id="orders-table" class="orders-table" style="display:none;">
                        <thead>
                            <tr>
                                <th>游戏</th>
                                <th>价格</th>
                                <th>下单账号</th>
                                <th>状态</th>
                                <th>下单时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="orders-list">
                            <!-- 订单列表将通过JS动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 从本地存储加载设置
    // CDK下单设置
    const cdkPayType = localStorage.getItem('cdkPayType') || 'AU';
    const cdkUseBalance = localStorage.getItem('cdkUseBalance');
    const isCdkUsingMain = localStorage.getItem('cdkUseMainAccount') !== 'false'; // 默认使用主账号
    
    // 肥皂盒下单设置
    const chestPayType = localStorage.getItem('chestPayType') || 'py';
    const chestUseBalance = localStorage.getItem('chestUseBalance');
    const isChestUsingMain = localStorage.getItem('chestUseMainAccount') !== 'false'; // 默认使用主账号
    
    // 设置CDK下单表单值
    if (document.getElementById('cdk-pay-type')) {
        document.getElementById('cdk-pay-type').value = cdkPayType;
    }
    if (document.getElementById('cdk-use-balance')) {
        // 确保即使是空字符串也能正确设置
        document.getElementById('cdk-use-balance').value = cdkUseBalance !== null ? cdkUseBalance : '';
    }
    
    // 设置肥皂盒下单表单值
    if (document.getElementById('chest-pay-type')) {
        document.getElementById('chest-pay-type').value = chestPayType;
    }
    if (document.getElementById('chest-use-balance')) {
        // 确保即使是空字符串也能正确设置
        document.getElementById('chest-use-balance').value = chestUseBalance !== null ? chestUseBalance : 'useBalance';
    }
    
    // 设置CDK账号选择开关
    const cdkAccountSwitch = document.getElementById('cdk-account-switch');
    const cdkAccountSwitchText = document.getElementById('cdk-account-switch-text');
    
    if (cdkAccountSwitch && cdkAccountSwitchText) {
        // 设置开关的初始状态
        cdkAccountSwitch.checked = !isCdkUsingMain;
        cdkAccountSwitchText.textContent = isCdkUsingMain ? '主账号' : '副账号';
        
        // 添加开关事件监听
        cdkAccountSwitch.addEventListener('change', function() {
            const isUsingMainAccount = !this.checked;
            cdkAccountSwitchText.textContent = isUsingMainAccount ? '主账号' : '副账号';
            localStorage.setItem('cdkUseMainAccount', isUsingMainAccount);
        });
    }
    
    // 设置肥皂盒账号选择开关
    const chestAccountSwitch = document.getElementById('chest-account-switch');
    const chestAccountSwitchText = document.getElementById('chest-account-switch-text');
    
    if (chestAccountSwitch && chestAccountSwitchText) {
        // 设置开关的初始状态
        chestAccountSwitch.checked = !isChestUsingMain;
        chestAccountSwitchText.textContent = isChestUsingMain ? '主账号' : '副账号';
        
        // 添加开关事件监听
        chestAccountSwitch.addEventListener('change', function() {
            const isUsingMainAccount = !this.checked;
            chestAccountSwitchText.textContent = isUsingMainAccount ? '主账号' : '副账号';
            localStorage.setItem('chestUseMainAccount', isUsingMainAccount);
        });
    }
    
    // 初始化特殊模式设置
    initSpecialModeSettings();
    
    // 保存设置
    document.getElementById('save-order-settings').addEventListener('click', function() {
        // 获取CDK下单设置
        const cdkPayType = document.getElementById('cdk-pay-type').value;
        const cdkUseBalance = document.getElementById('cdk-use-balance').value;
        const isCdkUsingMain = !document.getElementById('cdk-account-switch').checked;
        
        // 获取肥皂盒下单设置
        const chestPayType = document.getElementById('chest-pay-type').value;
        const chestUseBalance = document.getElementById('chest-use-balance').value;
        const isChestUsingMain = !document.getElementById('chest-account-switch').checked;
        
        // 保存CDK设置到本地存储
        localStorage.setItem('cdkPayType', cdkPayType);
        localStorage.setItem('cdkUseBalance', cdkUseBalance);
        localStorage.setItem('cdkUseMainAccount', isCdkUsingMain);
        
        // 保存肥皂盒设置到本地存储
        localStorage.setItem('chestPayType', chestPayType);
        localStorage.setItem('chestUseBalance', chestUseBalance);
        localStorage.setItem('chestUseMainAccount', isChestUsingMain);
        
        // 保存特殊模式设置
        saveSpecialModeSettings();
        
        // 显示保存成功消息
        alert('设置已保存');
    });
    
    // 加载订单记录
    loadOrdersList();
});

/**
 * 加载订单记录列表
 */
async function loadOrdersList() {
    const ordersLoading = document.getElementById('orders-loading');
    const ordersError = document.getElementById('orders-error');
    const ordersEmpty = document.getElementById('orders-empty');
    const ordersTable = document.getElementById('orders-table');
    const ordersList = document.getElementById('orders-list');
    
    try {
        // 显示加载中
        ordersLoading.style.display = 'flex';
        ordersError.style.display = 'none';
        ordersEmpty.style.display = 'none';
        ordersTable.style.display = 'none';
        
        // 获取订单数据
        const response = await fetch('/api/orders');
        
        if (!response.ok) {
            throw new Error(`获取订单记录失败: ${response.status} ${response.statusText}`);
        }
        
        const orders = await response.json();
        
        // 隐藏加载中
        ordersLoading.style.display = 'none';
        
        // 如果没有订单，显示空提示
        if (!orders || orders.length === 0) {
            ordersEmpty.style.display = 'block';
            return;
        }
        
        // 显示订单表格
        ordersTable.style.display = 'table';
        
        // 清空列表
        ordersList.innerHTML = '';
        
        // 添加订单数据
        orders.forEach(order => {
            const row = document.createElement('tr');
            
            // 格式化时间
            const orderTime = new Date(order.order_time);
            const formattedTime = orderTime.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            // 设置行内容
            const expireTime = order.expire_time ? new Date(order.expire_time) : null;
            
            // 检查订单是否已过期
            const now = new Date();
            let orderStatus = order.status;
            let statusClass = order.status === '已支付' ? 'paid' : 'pending';
            
            // 如果过期时间存在且已过期，则显示为已过期
            if (expireTime && now > expireTime) {
                orderStatus = '已过期';
                statusClass = 'expired';
            }
            
            row.innerHTML = `
                <td>${order.game_name}</td>
                <td>¥${order.price.toFixed(2)}</td>
                <td>${order.account_name}</td>
                <td><span class="order-status ${statusClass}">${orderStatus}</span></td>
                <td>${formattedTime}</td>
                <td>
                    ${orderStatus === '已支付' ? 
                        `<button class="redeem-btn" data-order-id="${order.id}">兑换</button>` :
                        `<button class="redeem-btn disabled" disabled>待支付</button>`
                    }
                    <button class="delete-btn" data-order-id="${order.id}">删除</button>
                </td>
            `;
            
            // 如果是已支付订单，添加兑换按钮的点击事件
            if (orderStatus === '已支付') {
                const redeemBtn = row.querySelector('.redeem-btn');
                redeemBtn.addEventListener('click', async () => {
                    await redeemOrder(order.id);
                });
            }
            
            // 添加删除按钮的点击事件
            const deleteBtn = row.querySelector('.delete-btn');
            deleteBtn.addEventListener('click', async () => {
                await deleteOrder(order.id);
            });
            
            ordersList.appendChild(row);
        });
        
    } catch (error) {
        console.error('加载订单记录失败:', error);
        ordersLoading.style.display = 'none';
        ordersError.style.display = 'flex';
        ordersError.querySelector('span').textContent = `加载订单记录失败: ${error.message}`;
    }
}

/**
 * 兑换订单激活码
 * @param {string} orderId - 订单ID
 */
async function redeemOrder(orderId) {
    try {
        // 显示加载中
        const redeemBtn = document.querySelector(`.redeem-btn[data-order-id="${orderId}"]`);
        const originalText = redeemBtn.textContent;
        redeemBtn.textContent = '兑换中...';
        redeemBtn.disabled = true;
        
        // 发送兑换请求
        const response = await fetch(`/api/orders/${orderId}/redeem`);
        
        // 获取响应数据
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.detail || `兑换失败: ${response.status} ${response.statusText}`);
        }
        
        // 处理兑换结果
        if (data.success && data.records && data.records.length > 0) {
            // 构建激活码显示内容
            let cdkHtml = '<h3>兑换成功! 游戏激活码:</h3><ul style="list-style: none; padding: 1rem; background-color: #f8f9fa; border-radius: 5px;">';
            
            data.records.forEach(record => {
                cdkHtml += `
                <li style="margin-bottom: 0.5rem; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; background-color: white;">
                    <div><strong>游戏:</strong> ${record.game_name}</div>
                    <div><strong>激活码:</strong> <code style="background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px;">${record.cdk}</code></div>
                    <div><strong>生成时间:</strong> ${record.create_time}</div>
                    <div><strong>状态:</strong> ${record.is_used ? '已使用' : '未使用'}</div>
                </li>`;
            });
            
            cdkHtml += '</ul><p style="color:#666;">提示: 请保存好您的激活码，页面刷新后将不再显示完整的激活码!</p>';
            
            // 显示弹窗
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
            `;
            
            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background-color: white;
                padding: 1.5rem;
                border-radius: 8px;
                max-width: 80%;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            `;
            
            modalContent.innerHTML = cdkHtml;
            
            // 添加关闭按钮
            const closeBtn = document.createElement('button');
            closeBtn.textContent = '关闭';
            closeBtn.style.cssText = `
                padding: 0.5rem 1rem;
                margin-top: 1rem;
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            `;
            
            closeBtn.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
            
            modalContent.appendChild(closeBtn);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);
            
            // 点击模态框背景也可以关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    document.body.removeChild(modal);
                }
            });
            
        } else {
            alert('兑换成功，但未找到激活码记录。');
        }
        
    } catch (error) {
        console.error('兑换订单失败:', error);
        alert(`兑换失败: ${error.message}`);
    } finally {
        // 恢复按钮状态
        const redeemBtn = document.querySelector(`.redeem-btn[data-order-id="${orderId}"]`);
        if (redeemBtn) {
            redeemBtn.textContent = '兑换';
            redeemBtn.disabled = false;
        }
    }
}

/**
 * 删除订单
 * @param {string} orderId - 订单ID
 */
async function deleteOrder(orderId) {
    if (!confirm('确定要删除这个订单吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        // 发送删除请求
        const response = await fetch(`/api/orders/${orderId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            const data = await response.json();
            throw new Error(data.detail || `删除失败: ${response.status} ${response.statusText}`);
        }
        
        // 刷新订单列表
        loadOrdersList();
        
    } catch (error) {
        console.error('删除订单失败:', error);
        alert(`删除订单失败: ${error.message}`);
    }
}

/**
 * 初始化特殊模式设置
 */
function initSpecialModeSettings() {
    const enableSpecialMode = document.getElementById('enable-special-mode');
    const specialModeSettings = document.getElementById('special-mode-settings');
    
    // 从localStorage加载特殊模式设置
    const isSpecialModeEnabled = localStorage.getItem('enableSpecialMode') === 'true';
    
    // 设置开关状态
    if (enableSpecialMode) {
        enableSpecialMode.checked = isSpecialModeEnabled;
        specialModeSettings.style.display = isSpecialModeEnabled ? 'block' : 'none';
    }
    
    // 初始化特殊模式CDK设置
    initSpecialCdkSettings();
    
    // 初始化特殊模式肥皂盒设置
    initSpecialChestSettings();
    
    // 添加特殊模式开关事件监听
    if (enableSpecialMode) {
        enableSpecialMode.addEventListener('change', function() {
            specialModeSettings.style.display = this.checked ? 'block' : 'none';
            localStorage.setItem('enableSpecialMode', this.checked);
        });
    }
    
    // 显示特殊模式状态提示
    checkAndDisplaySpecialModeStatus();
}

/**
 * 初始化特殊模式CDK设置
 */
function initSpecialCdkSettings() {
    // 加载特殊模式CDK设置
    const specialCdkPayType = localStorage.getItem('specialCdkPayType') || 'AU';
    const specialCdkUseBalance = localStorage.getItem('specialCdkUseBalance') || '';
    const isSpecialCdkUsingMain = localStorage.getItem('specialCdkUseMainAccount') !== 'false';
    
    // 设置表单值
    const specialCdkPayTypeSelect = document.getElementById('special-cdk-pay-type');
    const specialCdkUseBalanceSelect = document.getElementById('special-cdk-use-balance');
    if (specialCdkPayTypeSelect) specialCdkPayTypeSelect.value = specialCdkPayType;
    if (specialCdkUseBalanceSelect) specialCdkUseBalanceSelect.value = specialCdkUseBalance;
    
    // 设置特殊模式CDK账号选择开关
    const specialCdkAccountSwitch = document.getElementById('special-cdk-account-switch');
    const specialCdkAccountSwitchText = document.getElementById('special-cdk-account-switch-text');
    
    if (specialCdkAccountSwitch && specialCdkAccountSwitchText) {
        specialCdkAccountSwitch.checked = !isSpecialCdkUsingMain;
        specialCdkAccountSwitchText.textContent = isSpecialCdkUsingMain ? '主账号' : '副账号';
        
        specialCdkAccountSwitch.addEventListener('change', function() {
            const isUsingMainAccount = !this.checked;
            specialCdkAccountSwitchText.textContent = isUsingMainAccount ? '主账号' : '副账号';
            localStorage.setItem('specialCdkUseMainAccount', isUsingMainAccount);
        });
    }
}

/**
 * 初始化特殊模式肥皂盒设置
 */
function initSpecialChestSettings() {
    // 加载特殊模式肥皂盒设置
    const specialChestPayType = localStorage.getItem('specialChestPayType') || 'AA';
    const specialChestUseBalance = localStorage.getItem('specialChestUseBalance') || '';
    const isSpecialChestUsingMain = localStorage.getItem('specialChestUseMainAccount') !== 'false';
    
    // 设置表单值
    const specialChestPayTypeSelect = document.getElementById('special-chest-pay-type');
    const specialChestUseBalanceSelect = document.getElementById('special-chest-use-balance');
    if (specialChestPayTypeSelect) specialChestPayTypeSelect.value = specialChestPayType;
    if (specialChestUseBalanceSelect) specialChestUseBalanceSelect.value = specialChestUseBalance;
    
    // 设置特殊模式肥皂盒账号选择开关
    const specialChestAccountSwitch = document.getElementById('special-chest-account-switch');
    const specialChestAccountSwitchText = document.getElementById('special-chest-account-switch-text');
    
    if (specialChestAccountSwitch && specialChestAccountSwitchText) {
        specialChestAccountSwitch.checked = !isSpecialChestUsingMain;
        specialChestAccountSwitchText.textContent = isSpecialChestUsingMain ? '主账号' : '副账号';
        
        specialChestAccountSwitch.addEventListener('change', function() {
            const isUsingMainAccount = !this.checked;
            specialChestAccountSwitchText.textContent = isUsingMainAccount ? '主账号' : '副账号';
            localStorage.setItem('specialChestUseMainAccount', isUsingMainAccount);
        });
    }
}

/**
 * 保存特殊模式设置
 */
function saveSpecialModeSettings() {
    const enableSpecialMode = document.getElementById('enable-special-mode');
    
    // 保存基本设置
    if (enableSpecialMode) {
        localStorage.setItem('enableSpecialMode', enableSpecialMode.checked);
    }
    
    // 保存特殊模式CDK设置
    const specialCdkPayType = document.getElementById('special-cdk-pay-type');
    const specialCdkUseBalance = document.getElementById('special-cdk-use-balance');
    const isSpecialCdkUsingMain = !document.getElementById('special-cdk-account-switch').checked;
    
    if (specialCdkPayType) {
        localStorage.setItem('specialCdkPayType', specialCdkPayType.value);
    }
    if (specialCdkUseBalance) {
        localStorage.setItem('specialCdkUseBalance', specialCdkUseBalance.value);
    }
    localStorage.setItem('specialCdkUseMainAccount', isSpecialCdkUsingMain);
    
    // 保存特殊模式肥皂盒设置
    const specialChestPayType = document.getElementById('special-chest-pay-type');
    const specialChestUseBalance = document.getElementById('special-chest-use-balance');
    const isSpecialChestUsingMain = !document.getElementById('special-chest-account-switch').checked;
    
    if (specialChestPayType) {
        localStorage.setItem('specialChestPayType', specialChestPayType.value);
    }
    if (specialChestUseBalance) {
        localStorage.setItem('specialChestUseBalance', specialChestUseBalance.value);
    }
    localStorage.setItem('specialChestUseMainAccount', isSpecialChestUsingMain);
}

/**
 * 检查是否启用特殊模式
 */
function isSpecialModeEnabled() {
    return localStorage.getItem('enableSpecialMode') === 'true';
}

/**
 * 获取当前有效的下单配置（根据saleid判断是否使用特殊模式）
 * @param {string} saleId - 商品ID，用于判断是否使用特殊模式配置
 */
function getCurrentOrderSettings(saleId) {
    const useSpecialMode = isSpecialModeEnabled() && saleId && !saleId.toLowerCase().includes('k');
    
    const settings = {
        cdk: {
            payType: localStorage.getItem(useSpecialMode ? 'specialCdkPayType' : 'cdkPayType') || 'AU',
            useBalance: localStorage.getItem(useSpecialMode ? 'specialCdkUseBalance' : 'cdkUseBalance') || '',
            useMainAccount: localStorage.getItem(useSpecialMode ? 'specialCdkUseMainAccount' : 'cdkUseMainAccount') !== 'false'
        },
        chest: {
            payType: localStorage.getItem(useSpecialMode ? 'specialChestPayType' : 'chestPayType') || 'AA',
            useBalance: localStorage.getItem(useSpecialMode ? 'specialChestUseBalance' : 'chestUseBalance') || '',
            useMainAccount: localStorage.getItem(useSpecialMode ? 'specialChestUseMainAccount' : 'chestUseMainAccount') !== 'false'
        },
        isSpecialMode: useSpecialMode
    };
    
    return settings;
}

/**
 * 检查并显示特殊模式状态
 */
function checkAndDisplaySpecialModeStatus() {
    const isSpecial = isSpecialModeEnabled();
    console.log(`特殊模式状态: ${isSpecial ? '已启用' : '已禁用'}`);
    
    // 在页面标题显示特殊模式状态
    updatePageTitleWithSpecialMode(isSpecial);
}

/**
 * 更新页面标题以显示特殊模式状态
 */
function updatePageTitleWithSpecialMode(isSpecialMode) {
    const titleElement = document.querySelector('h1');
    if (titleElement) {
        const baseTitle = '仪表盘';
        titleElement.textContent = isSpecialMode ? `⚡ ${baseTitle} (特殊模式)` : baseTitle;
    }
}

// 将getCurrentOrderSettings函数暴露到全局，供其他模块使用
window.getCurrentOrderSettings = getCurrentOrderSettings;
</script>
{% endblock %} 