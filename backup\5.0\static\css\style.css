/* ---
Root & Theming
--- */
:root {
  /* 主色调 */
  --primary: #4f46e5;
  --primary-hover: #4338ca;
  --primary-focus: rgba(79, 70, 229, 0.125);
  --primary-inverse: #fff;
  
  /* 辅助颜色 */
  --secondary: #0ea5e9;
  --success: #22c55e;
  --warning: #f59e0b;
  --danger: #ef4444;
  
  /* 明亮主题 */
  --background-color: #f8fafc;
  --card-background-color: #ffffff;
  --card-border-color: #e2e8f0;
  --card-box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --text-color: #0f172a;
  --text-muted-color: #64748b;
  --log-bg-color: #f1f5f9;
  --log-text-color: #334155;
  
  /* 布局变量 */
  --header-height: 60px;
  --bottom-nav-height: 60px;
  --container-padding: 1.5rem;
  --section-spacing: 2rem;
  --card-radius: 0.75rem;
  
  /* 动画 */
  --transition-speed: 0.2s;
  --loader-color: rgba(66, 99, 235, 0.8);
  --loader-size: 40px;

  /* 新添加的状态相关变量 */
  --primary-color: #4a6cf7;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --body-bg: #f5f7fb;
  --body-color: #212529;
  --header-bg: #ffffff;
  --card-bg: #ffffff;
  --border-color: #e9ecef;
  --shadow-color: rgba(0, 0, 0, 0.05);
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

[data-theme="dark"] {
  /* 暗色主题 */
  --primary: #818cf8;
  --primary-hover: #a78bfa;
  --primary-focus: rgba(129, 140, 248, 0.25);
  
  --background-color: #020617;
  --card-background-color: #0f172a;
  --card-border-color: #1e293b;
  --card-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --text-color: #e2e8f0;
  --text-muted-color: #94a3b8;
  --log-bg-color: #1e293b;
  --log-text-color: #cbd5e1;
  --loader-color: rgba(116, 143, 252, 0.8);

  /* 新添加的状态相关变量 */
  --primary-color: #4e73df;
  --secondary-color: #858796;
  --success-color: #1cc88a;
  --danger-color: #e74a3b;
  --warning-color: #f6c23e;
  --info-color: #36b9cc;
  --light-color: #f8f9fc;
  --dark-color: #5a5c69;
  --body-bg: #1a1c23;
  --body-color: #e0e0e0;
  --header-bg: #252830;
  --card-bg: #252830;
  --border-color: #2e3035;
  --shadow-color: rgba(0, 0, 0, 0.15);
}

/* ---
基础样式
--- */
body {
  font-family: var(--font-family);
  background-color: var(--body-bg);
  color: var(--body-color);
  /* padding-top is now applied to main content */
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-speed) ease;
  pointer-events: auto;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 顶部加载条 */
#top-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--secondary), var(--primary));
  background-size: 200% 200%;
  z-index: 9999;
  animation: top-loader-progress 1.5s linear infinite, move-bg 1.5s linear infinite;
  display: none; /* 默认隐藏 */
}

@keyframes top-loader-progress {
  0% { width: 0%; }
  50% { width: 100%; }
  100% { width: 0%; opacity: 0; }
}

@keyframes move-bg {
    0%{background-position:0% 50%}
    50%{background-position:100% 50%}
    100%{background-position:0% 50%}
}

/* Unified container styles */
.container {
  padding: var(--container-padding);
  max-width: 1200px;
  margin: 0 auto;
}

/* Add top padding specifically to the main container to avoid the header */
main.container {
  padding-top: calc(var(--header-height) + var(--section-spacing));
}

/* 排版样式 */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-color);
}

h1 { font-size: 1.875rem; }
h2 { font-size: 1.25rem; }
h3 { font-size: 1.25rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem;
  color: var(--text-muted-color);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition-speed) ease;
}

a:hover {
  color: var(--primary-hover);
}

/* 卡片样式 - 重构为 .card 类 */
.card {
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  box-shadow: var(--card-box-shadow);
  border-radius: var(--card-radius);
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  transition: all 0.2s ease;
}

/* 用于需要限制内容宽度的卡片，例如仪表盘上的状态卡片 */
.card-constrained {
  max-width: 800px; 
  margin-left: auto;
  margin-right: auto;
}

/* article header 样式现在由 .article-header 直接处理，不再需要 article 前缀 */
/*
article header {
  margin-bottom: 1rem;
}
*/

/* 节段间距 */
section {
  margin-bottom: var(--section-spacing);
}

/* ---
导航和布局
--- */
/* 顶部导航栏 */
.app-header {
  background-color: var(--header-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-bottom: 1px solid var(--card-border-color);
  
  /* Restore fixed position */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  z-index: 100;
  display: none; /* Hidden on mobile by default */
  box-shadow: 0 2px 10px var(--shadow-color);
}

[data-theme="dark"] .app-header {
    background-color: var(--header-bg);
}

.app-header .container {
    height: 100%;
}

.app-header nav {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.brand-logo {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  color: var(--primary-color);
}

.brand-logo i {
  margin-right: 0.5rem;
  color: var(--primary);
}

.brand-logo strong {
    font-weight: 700;
}

.nav-links {
  display: flex;
  gap: 1rem;
}

.app-header .nav-link {
  color: var(--text-muted-color);
  padding: 0.5rem 1rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.app-header .nav-link:hover {
  color: var(--primary);
  background-color: transparent;
}

.app-header .nav-link.is-active {
  color: var(--primary);
  background-color: transparent;
  position: relative;
}

/* 激活状态下划线 */
.app-header .nav-link.is-active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary);
}

.mode-toggle {
  display: flex;
  align-items: center;
  margin-left: 1rem; /* Add some space */
}

/* 底部导航栏（移动端） */
.bottom-nav {
  background-color: var(--header-bg);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-top: 1px solid var(--card-border-color);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--bottom-nav-height);
  z-index: 100;
  padding: 0.5rem 0;
  box-shadow: 0 -2px 10px var(--shadow-color);
  display: none;
}

[data-theme="dark"] .bottom-nav {
    background-color: var(--header-bg);
}

.bottom-nav .nav-link {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-muted-color);
  text-decoration: none;
  font-size: 0.75rem;
  transition: color var(--transition-speed) ease;
}

.bottom-nav .nav-link i {
  font-size: 1.25rem;
  margin-bottom: 0rem;
  transition: transform var(--transition-speed) ease;
}

.bottom-nav .nav-link.is-active {
  color: var(--primary);
}

.bottom-nav .nav-link.is-active i {
  transform: translateY(-2px);
}

/* 响应式布局调整 */
@media (min-width: 768px) {
  .app-header {
    display: block;
  }
  .bottom-nav {
    display: none;
  }
  body {
    padding-bottom: 0; /* 重置底部内边距 */
  }
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}

@media (max-width: 767px) {
  body {
    padding-bottom: var(--bottom-nav-height);
  }
  
  main.container {
    padding-top: var(--container-padding); /* Reset for mobile as header is hidden */
  }
  
  /* This rule is being removed as it's now part of the base .container style */
}

/* 布局网格 */
.layout-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 992px) {
  .layout-grid {
    /* Make grid more adaptive */
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }
}

/* ---
组件样式
--- */
/* 图标 */
i.icon {
  display: inline-block;
  font-style: normal;
  vertical-align: middle;
  font-size: 1.25rem;
  line-height: 1;
  margin-right: 0.25rem;
}

h1 i, h2 i, header strong i {
  margin-right: 0.75rem;
  color: var(--primary);
}

/* 按钮 */
button, [role="button"] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

button:hover, [role="button"]:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
}

button:active, [role="button"]:active {
    transform: translateY(0);
}

button.icon-btn, a.icon-btn {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted-color);
  transition: all var(--transition-speed) ease;
}

button.icon-btn svg, a.icon-btn svg {
  width: 22px;
  height: 22px;
}

button.outline, a.outline {
  background-color: transparent;
  border-color: var(--card-border-color);
  color: var(--text-color);
}

button.outline:hover, a.outline:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--text-muted-color);
}

a.icon-btn:hover, button.icon-btn:hover {
  color: var(--primary);
}

button.secondary {
  --background-color: var(--secondary);
  --border-color: var(--secondary);
  --color: white;
}

/* 日志框 */
.log-box {
  background-color: var(--body-bg);
  color: var(--body-color);
  height: 400px;
  overflow-y: auto;
  padding: 1rem;
  border-radius: 8px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.9em;
  border: 1px solid var(--border-color);
}

.log-box p {
  margin-bottom: 0.5em;
  word-break: break-all;
  color: var(--body-color) !important;
}

.log-success { color: var(--success) !important; }
.log-warn { color: var(--warning) !important; }
.log-error { color: var(--danger) !important; }

/* 文章头部 */
.article-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

/* 表单样式 */
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  align-items: end;
}

input, select, textarea {
  width: 100%;
  border-radius: 0.5rem;
  padding: 0.75rem;
  border: 1px solid var(--card-border-color);
  background-color: var(--background-color);
  color: var(--text-color);
  transition: all var(--transition-speed) ease;
}

input:focus, select:focus, textarea:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-focus);
}

label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.form-group {
  margin-bottom: 1rem;
}

@media (min-width: 576px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
  .form-group {
    grid-column: span 1;
  }
  .form-actions {
    grid-column: span 2;
  }
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 0.5rem;
}

/* 列表样式 */
#mobile-account-list-container ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

#chest-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

#mobile-account-list-container li {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  margin-bottom: 0.75rem;
  transition: all var(--transition-speed) ease;
  position: relative;
}

#chest-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  margin-bottom: 0.5rem;
  transition: all var(--transition-speed) ease;
}

#mobile-account-list-container li:hover {
  box-shadow: var(--card-box-shadow);
}

#chest-list li:hover {
  box-shadow: var(--card-box-shadow);
  transform: translateY(-1px);
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
}

.account-badges {
  display: flex;
  gap: 0.5rem;
  margin: 0.25rem 0;
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
}

.bg-primary { background-color: #4a6cf7; }
.bg-success { background-color: #28a745; }
.bg-info { background-color: #17a2b8; }
.bg-warning { background-color: #ffc107; color: #212529; }
.bg-secondary { background-color: #6c757d; }

.account-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.account-type-menu {
  position: absolute;
  top: 40px;
  right: 0;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  padding: 0.75rem;
  z-index: 100;
  width: 180px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform-origin: top right;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.menu-header {
  font-weight: 600;
  padding: 0.5rem;
  border-bottom: 1px solid var(--card-border-color);
  margin-bottom: 0.75rem;
  text-align: center;
}

.menu-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.menu-options button {
  background: none;
  border: none;
  text-align: left;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.menu-options button:hover {
  background-color: var(--primary-focus);
  color: var(--primary);
}

/* 全局加载器 */
#global-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

#global-loader svg {
  width: 40px;
  height: 40px;
  color: white;
}

/* 状态标记 */
mark {
  padding: 0.25em 0.5em;
  border-radius: 4px;
  background-color: var(--primary-focus);
  color: var(--primary);
}

mark.success {
  background-color: rgba(64, 192, 87, 0.15);
  color: var(--success);
}

mark.warning {
  background-color: rgba(250, 176, 5, 0.15);
  color: var(--warning);
}

mark.danger {
  background-color: rgba(250, 82, 82, 0.15);
  color: var(--danger);
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn var(--transition-speed) ease;
}

/* 加载指示器样式 */
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  color: var(--text-muted-color);
}

.error-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
  color: var(--danger);
}

.spin {
  animation: spin 1.5s linear infinite;
}

/* 状态样式 */
.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.status-label {
  width: 8rem;
  color: var(--text-muted-color);
}

.text-muted {
  color: var(--text-muted-color);
}

p.page-description {
    font-size: 1.125rem;
    color: var(--text-muted-color);
}

/* 游戏搜索结果列表样式 */
#game-results-container ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 0.75rem;
}

#game-results-container li {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background-color: var(--log-bg-color);
  border: 1px solid var(--card-border-color);
  border-radius: 8px;
  transition: all var(--transition-speed) ease;
}

#game-results-container li:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--card-box-shadow);
}

.game-image {
  width: 120px;
  height: 45px;
  margin-right: 1rem;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;
  background-color: var(--background-color);
}

.game-info {
    display: flex;
    flex-direction: column;
}

.game-prices {
    display: flex;
    align-items: center;
    gap: 0.75rem; /* Space between price groups */
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.price-label {
    color: var(--text-muted-color);
}

.price-value {
    font-weight: 600;
}

.price-current {
    color: var(--success);
}

.price-average {
    color: var(--warning);
}

/* ---
新功能: 游戏收藏 & 卖家模态框  -> 重构为仅搜索
--- */

/* 页面头部 */
.page-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--card-border-color);
}
.page-header h1 {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* 搜索结果区域 */
.results-area {
    margin-top: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

/* 新的结果卡片样式 */
.result-card {
  position: relative;
  display: flex;
  align-items: stretch;
}

.result-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--card-box-shadow);
}

.result-card-image {
  width: 120px;
  height: 68px; /* 根据常见横幅图比例调整，保持一致性 */
  object-fit: cover; /* 切换回 cover 以填充容器，避免留白 */
  border-radius: 0.375rem;
  background-color: #f1f5f9;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
}

.result-card-info {
  flex-grow: 1;
  min-width: 0; /* 防止文本溢出 */
}

.result-card-name {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.result-card-id {
  font-size: 0.875rem;
}

.sellers-btn {
  margin-left: auto; /* 按钮推到最右侧 */
  flex-shrink: 0;
}

/* 查看卖家按钮 */
.sellers-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
    white-space: nowrap;
    margin-top: 8px;
}

.sellers-btn:hover {
    background-color: #45a049;
}

/* 收藏按钮样式 */
.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.favorite-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted-color);
  padding: 0;
  line-height: 1;
  transition: color 0.2s, transform 0.2s;
}

.favorite-btn:hover {
  transform: scale(1.2);
  color: var(--warning-color);
}

.favorite-btn.active {
  color: var(--warning-color);
  transform: scale(1.1);
}

/* 卡片右侧按钮组 */
.card-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.75rem;
  padding-left: 1rem;
}

/* 卖家按钮 */
.sellers-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 14px;
  white-space: nowrap;
}

.sellers-btn:hover {
  background-color: #45a049;
}

/* 模态框标题栏操作按钮 */
.modal-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refresh-btn {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted-color);
  transition: color 0.2s, transform 0.2s;
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
}

.refresh-btn:hover {
  color: var(--primary-color);
  background-color: rgba(0, 0, 0, 0.05);
}

.refresh-btn svg {
  width: 18px;
  height: 18px;
}

/* 卖家模态框样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.7);
}

.modal-content {
  background-color: var(--card-background-color);
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--card-border-color);
  padding-bottom: 10px;
  margin-bottom: 15px;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25em;
  color: var(--text-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-muted-color);
}

.close-btn:hover {
  color: var(--text-color);
}

.modal-body {
    padding: 10px 0;
}

#modal-loading {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: #666;
}

.sellers-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.seller-item {
    padding: 12px;
    border-bottom: 1px solid #eee;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.seller-item:last-child {
    border-bottom: none;
}

.seller-info-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
}

.seller-name {
    font-weight: 500;
    color: var(--text-color);
}

.seller-price {
    font-weight: bold;
    color: #e53935;
}

.seller-discount {
    color: #ff6d00;
    font-weight: 500;
    background-color: rgba(255, 109, 0, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.seller-quantity {
    color: #2196f3;
}

.seller-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.seller-id {
    font-size: 0.8rem;
    color: var(--text-muted-color);
    opacity: 0.7;
}

.error {
    color: #e53935;
    font-weight: bold;
}

/* 新添加的状态相关样式 */
.status-display {
  background-color: var(--body-bg);
  border-radius: 0.25rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  margin-bottom: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-weight: 500;
}

mark {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.status-running {
  background-color: rgba(40, 167, 69, 0.2);
  color: var(--success-color);
}

.status-stopped {
  background-color: rgba(220, 53, 69, 0.2);
  color: var(--danger-color);
}

/* 工具提示 */
[data-tooltip] {
  position: relative;
}

[data-tooltip]::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 0.25rem 0.5rem;
  background-color: var(--dark-color);
  color: white;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
}

[data-tooltip]:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Toast Notifications */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--dark-color);
    color: var(--light-color);
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1050;
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s, transform 0.3s;
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast.error {
    background-color: var(--danger-color);
}

/* ---
游戏搜索页面 - Game Search Page
--- */

/* 为结果容器设置最小高度，防止搜索时页面布局跳动 */
#game-results-container {
  min-height: 300px;
  transition: all var(--transition-speed) ease;
}

/* 游戏结果卡片 */
.result-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: var(--card-radius);
  margin-bottom: 1rem;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  border-color: var(--primary);
}

/* 卡片中的游戏图片 */
.result-card-image {
  width: 120px;
  height: 68px; /* 根据常见横幅图比例调整，保持一致性 */
  object-fit: cover; /* 切换回 cover 以填充容器，避免留白 */
  border-radius: 0.375rem;
  background-color: #f1f5f9;
  flex-shrink: 0;
  border: 1px solid var(--border-color);
}

/* 游戏信息容器 */
.result-card-info {
  flex-grow: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  gap: 0.25rem; /* 信息间的垂直间距 */
}

/* 游戏名称 */
.result-card-name {
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--body-color);
}

/* 游戏ID */
.result-card-id {
  font-size: 0.8rem;
  color: var(--text-muted-color);
}

/* 价格信息容器 */
.result-card-prices {
  display: flex;
  gap: 1rem; /* 价格标签间的间距 */
  margin-top: 0.5rem;
}

/* 价格标签 */
.price-tag {
  font-size: 0.85rem;
  color: var(--text-muted-color);
}

.price-tag .key-price {
  color: var(--success-color); /* 绿色，表示当前价格 */
  font-weight: 600;
}

.price-tag .key-ave-amt {
  color: var(--info-color); /* 蓝色，表示平均参考价 */
  font-weight: 600;
}

/* 查看卖家按钮 */
.sellers-btn {
  margin-left: auto; /* 将按钮推到最右边 */
  flex-shrink: 0;
}

/* 移除之前的通用结果区域样式，或确保它们不冲突 */
.results-area {
  /* .results-area 的旧样式可以被移除或注释掉 */
  /* background: var(--log-bg-color); */
  /* padding: 1rem; */
  /* border-radius: var(--card-radius); */
}

/* 为搜索卡片设置特定样式，使其与结果区对齐 */
#search-card {
  width: 100%; /* 强制撑满父容器宽度 */
  margin-bottom: var(--section-spacing); /* 增加与下方结果的间距 */
}

/* 收藏按钮样式 */
.result-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.favorite-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-muted-color);
  padding: 0;
  line-height: 1;
  transition: color 0.2s, transform 0.2s;
}

.favorite-btn:hover {
  transform: scale(1.2);
  color: var(--warning-color);
}

.favorite-btn.active {
  color: var(--warning-color);
  transform: scale(1.1);
}

/* 收藏列表样式 */
.favorites-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.favorites-list .result-card {
  margin-bottom: 1rem;
}

.empty-favorites {
  text-align: center;
  padding: 2rem;
  color: var(--text-muted-color);
}

/* 收藏入口按钮 */
.favorites-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s;
}

.favorites-link:hover {
  opacity: 0.8;
}

/* 收藏功能提示和刷新提示 */
.refresh-notice {
  background-color: rgba(72, 187, 120, 0.1);
  color: var(--success-color);
  padding: 0.5rem;
  border-radius: 4px;
  text-align: center;
  margin-bottom: 1rem;
  font-weight: 500;
  transition: opacity 0.5s ease;
}

/* 结果卡片调整，让按钮和内容对齐 */
.result-card {
  position: relative;
  display: flex;
  align-items: stretch;
}

.card-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 0.75rem;
  padding-left: 1rem;
}

/* 使按钮垂直居中 */
.favorite-btn {
  margin-bottom: 0.5rem;
}

/* 游戏卡片图片容器 */
.image-container {
  position: relative;
  width: 120px;
  flex-shrink: 0;
}

/* 收藏按钮 - 悬浮在图片上 */
.favorite-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 255, 255, 0.7);
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.25rem;
  color: #ccc;
  padding: 0;
  line-height: 1;
  transition: all 0.2s ease;
  z-index: 2;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.favorite-btn:hover {
  transform: scale(1.1);
  color: var(--warning-color);
  background-color: rgba(255, 255, 255, 0.9);
}

.favorite-btn.active {
  color: var(--warning-color);
}

[data-theme="dark"] .favorite-btn {
  background-color: rgba(0, 0, 0, 0.5);
  color: #aaa;
}

[data-theme="dark"] .favorite-btn:hover,
[data-theme="dark"] .favorite-btn.active {
  color: var(--warning-color);
  background-color: rgba(0, 0, 0, 0.7);
}

/* 刷新按钮 */
.refresh-btn {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-muted-color);
  transition: color 0.2s, transform 0.2s;
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
  margin-right: 5px;
}

.refresh-btn:hover {
  color: var(--primary-color);
  background-color: rgba(0, 0, 0, 0.05);
}

.refresh-btn svg {
  width: 18px;
  height: 18px;
}

/* 修改结果卡片布局 */
.result-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: var(--card-radius);
  margin-bottom: 1rem;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.result-card-info {
  flex-grow: 1;
  min-width: 0;
}

/* 卖家按钮放在卡片最右侧 */
.sellers-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 14px;
  white-space: nowrap;
  margin-left: auto;
  flex-shrink: 0;
}

.sellers-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
}

/* 肥皂盒页面样式 */
.sort-card {
  padding: 1rem;
}

.sort-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  justify-content: space-between;
}

/* 排序按钮组样式 */
.sort-btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.sort-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  background-color: #f0f0f0;
  border: none;
  color: #333;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

[data-theme="dark"] .sort-btn {
  background-color: #333;
  color: #f0f0f0;
}

.sort-btn:hover {
  background-color: #4a6cf7;
  color: white;
}

.sort-btn.active {
  background-color: #4a6cf7;
  color: white;
}

/* 页码控制 */
.page-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

.page-input {
  width: 60px;
  text-align: center;
  padding: 0.5rem;
  border: 1px solid var(--card-border-color);
  border-radius: 0.25rem;
  background-color: var(--card-background-color);
  color: var(--text-color);
}

/* 更新肥皂盒卡片样式 */
.chest-item {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: var(--card-radius);
  margin-bottom: 1rem;
  transition: all 0.2s ease-in-out;
  position: relative;
}

.chest-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--primary);
}

/* 肥皂盒标题区域 */
.chest-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--card-border-color);
}

.chest-title {
  display: flex;
  align-items: baseline;
  gap: 0.75rem;
  flex: 1;
}

.chest-name {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

/* 概率徽章 */
.chest-probability-badge {
  background-color: var(--warning-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 抽取进度 */
.chest-progress {
  display: flex;
  align-items: center;
  gap: 0;
  font-weight: 600;
}

.chest-remain {
  color: var(--primary-color);
}

.chest-total {
  color: var(--text-muted-color);
}

.chest-id-label {
  font-size: 0.8rem;
  color: var(--text-muted-color);
  font-weight: normal;
}

.chest-badge {
  align-self: flex-start;
  padding: 0.25rem 0.75rem;
  color: white;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}

/* 肥皂盒详情信息 */
.chest-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  width: 100%;
}

.chest-detail {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 0.85rem;
  color: var(--text-muted-color);
  margin-bottom: 0;
  flex-shrink: 0;
}

.chest-id, .chest-price, .chest-seller {
  font-weight: 600;
  font-size: 1rem;
}

.chest-price {
  color: var(--success-color);
}

.chest-seller {
  color: var(--primary-color);
}

/* 分页控制样式优化 */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 2rem;
}

.pagination-btn {
  padding: 0.5rem 1rem;
  background-color: var(--card-background-color);
  border: 1px solid var(--card-border-color);
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 90px;
  font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

#page-indicator {
  font-weight: 500;
  padding: 0.5rem 1rem;
  background-color: rgba(74, 108, 247, 0.1);
  border-radius: 0.25rem;
  min-width: 120px;
  text-align: center;
}

/* 收藏列表排序功能 */
.favorites-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.favorites-item {
  cursor: grab;
  margin-bottom: 1rem;
}

.favorites-item:active {
  cursor: grabbing;
}

.favorites-list .result-card {
  margin-bottom: 0;
  transition: background-color 0.2s ease;
}

.favorites-list .result-card.sortable-ghost {
  opacity: 0.5;
  background-color: var(--primary-focus);
}

.favorites-list .result-card.sortable-drag {
  opacity: 0.8;
} 