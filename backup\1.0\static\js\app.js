// This script is intended for the games page.
document.addEventListener('DOMContentLoaded', () => {
    // DOM Element References
    const searchInput = document.getElementById('game-search-input');
    const searchForm = document.getElementById('game-search-form');
    const resultsContainer = document.getElementById('game-results-container');
    const gameCardTemplate = document.getElementById('game-card-template');

    // Modal elements
    const modal = document.getElementById('sellers-modal');
    const closeModalButton = modal?.querySelector('.close-btn');
    const modalGameTitle = document.getElementById('modal-game-title');
    const modalSellersList = document.getElementById('modal-sellers-list');
    const modalLoading = document.getElementById('modal-loading');

    /**
     * Create a game card element from game data
     * @param {object} game - Game data
     * @returns {HTMLElement}
     */
    function createGameCard(game) {
        const card = gameCardTemplate.content.cloneNode(true).firstElementChild;
        card.querySelector('.result-card-image').src = game.picUrl;
        card.querySelector('.result-card-name').textContent = game.gameName;
        card.querySelector('.result-card-id').textContent = `ID: ${game.id}`;

        // Populate prices
        const keyPriceEl = card.querySelector('.key-price');
        if (game.keyPrice !== null && game.keyPrice !== undefined) {
            keyPriceEl.textContent = `¥${game.keyPrice.toFixed(2)}`;
        }

        const keyAveAmtEl = card.querySelector('.key-ave-amt');
        if (game.keyAveAmt !== null && game.keyAveAmt !== undefined) {
            keyAveAmtEl.textContent = `¥${game.keyAveAmt.toFixed(2)}`;
        }
        
        const sellersButton = card.querySelector('.sellers-btn');
        sellersButton.addEventListener('click', () => openSellersModal(game));

        return card;
    }

    /**
     * Handle the game search
     */
    async function handleGameSearch(event) {
        // Ensure to prevent the default form submission behavior
        event.preventDefault();
        event.stopPropagation();

        const query = searchInput.value.trim();
        if (!query) {
            resultsContainer.innerHTML = '<p class="text-muted">请输入关键词进行搜索。</p>';
            return;
        }

        resultsContainer.innerHTML = '<div class="loading-indicator"><div class="spin"></div><span>正在搜索...</span></div>';
        
        try {
            const searchUrl = `/api/games/search?query=${encodeURIComponent(query)}`;
            const response = await fetch(searchUrl);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: '无法解析错误响应' }));
                throw new Error(errorData.detail || '搜索请求失败');
            }
            const games = await response.json();
            
            resultsContainer.innerHTML = '';
            if (!Array.isArray(games) || games.length === 0) {
                resultsContainer.innerHTML = '<p>没有找到相关游戏。</p>';
                return;
            }
            
            games.forEach(game => {
                const card = createGameCard(game);
                resultsContainer.appendChild(card);
            });

        } catch (error) {
            resultsContainer.innerHTML = `<p class="error-indicator">搜索失败: ${error.message}</p>`;
        }
    }

    /**
     * Open the sellers modal
     * @param {object} game
     */
    async function openSellersModal(game) {
        modal.style.display = 'block';
        modalGameTitle.textContent = `卖家列表 - ${game.gameName}`;
        modalSellersList.innerHTML = '';
        modalLoading.style.display = 'block';

        try {
            const response = await fetch(`/api/games/${game.id}/sellers`);
            if (!response.ok) throw new Error('获取卖家信息失败');
            const sellers = await response.json();
            
            modalLoading.style.display = 'none';
            if (sellers.length === 0) {
                modalSellersList.innerHTML = '<li>暂无卖家信息。</li>';
                return;
            }

            const list = document.createElement('ul');
            sellers.forEach(seller => {
                const item = document.createElement('li');
                item.textContent = `卖家: ${seller.seller_name || 'N/A'}, 价格: ${seller.price}, 数量: ${seller.quantity}`;
                list.appendChild(item);
            });
            modalSellersList.appendChild(list);

        } catch (error) {
            console.error('Error fetching sellers:', error);
            modalLoading.style.display = 'none';
            modalSellersList.innerHTML = '<li>加载卖家信息失败。</li>';
        }
    }

    // Event Listeners
    if (searchForm) {
        searchForm.addEventListener('submit', handleGameSearch);
    }

    // Modal event listeners
    if (closeModalButton) {
        closeModalButton.addEventListener('click', () => {
            modal.style.display = 'none';
        });
    }

    if (modal) {
        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
});