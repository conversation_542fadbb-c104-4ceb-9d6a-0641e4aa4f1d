/**
 * 游戏比价页面功能
 */

class PriceComparison {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadStats();
    }

    bindEvents() {
        // 搜索表单
        const searchForm = document.getElementById('price-search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        }

        // 显示全部按钮
        const showAllBtn = document.getElementById('show-all-btn');
        if (showAllBtn) {
            showAllBtn.addEventListener('click', () => this.showAllGames());
        }

        // 更新fhyx数据按钮
        const updateFhyxBtn = document.getElementById('update-fhyx-btn');
        if (updateFhyxBtn) {
            updateFhyxBtn.addEventListener('click', () => this.updateFhyxData());
        }

        // 迁移py数据按钮
        const migratePyBtn = document.getElementById('migrate-py-btn');
        if (migratePyBtn) {
            migratePyBtn.addEventListener('click', () => this.migratePyData());
        }
    }

    async loadStats() {
        try {
            const response = await fetch('/api/price-comparison/stats');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const stats = await response.json();
            this.updateStatsDisplay(stats);
        } catch (error) {
            console.error('加载统计信息失败:', error);
            this.showStatus('加载统计信息失败', 'error');
        }
    }

    updateStatsDisplay(stats) {
        const totalElement = document.getElementById('total-games');
        const fhyxElement = document.getElementById('fhyx-count');
        const pyElement = document.getElementById('py-count');
        const lastUpdateElement = document.getElementById('last-update');

        if (totalElement) totalElement.textContent = stats.total || 0;
        if (fhyxElement) fhyxElement.textContent = stats.by_source?.fhyx || 0;
        if (pyElement) pyElement.textContent = stats.by_source?.py || 0;
        
        if (lastUpdateElement && stats.last_update) {
            const date = new Date(stats.last_update);
            lastUpdateElement.textContent = date.toLocaleString('zh-CN');
        }
    }

    async handleSearch(event) {
        event.preventDefault();
        
        const searchInput = document.getElementById('price-search-input');
        const searchTerm = searchInput.value.trim();
        
        if (!searchTerm) {
            this.showStatus('请输入搜索关键词', 'error');
            return;
        }

        this.showLoading();

        try {
            const response = await fetch(`/api/price-comparison/search?q=${encodeURIComponent(searchTerm)}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const results = await response.json();
            this.displayResults(results);
        } catch (error) {
            console.error('搜索失败:', error);
            this.showStatus('搜索失败，请稍后重试', 'error');
            this.hideLoading();
        }
    }

    async showAllGames() {
        this.showLoading();

        try {
            const response = await fetch('/api/price-comparison/games?limit=200');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const results = await response.json();
            this.displayResults(results);
        } catch (error) {
            console.error('获取游戏列表失败:', error);
            this.showStatus('获取游戏列表失败，请稍后重试', 'error');
            this.hideLoading();
        }
    }

    displayResults(results) {
        this.hideLoading();
        
        const container = document.getElementById('price-results-container');
        if (!container) return;

        if (!results || results.length === 0) {
            container.innerHTML = '<p class="text-muted">未找到相关游戏价格信息。</p>';
            return;
        }

        // 按游戏名称分组
        const gameGroups = this.groupByGameName(results);
        
        container.innerHTML = '';
        
        Object.keys(gameGroups).forEach(gameName => {
            const prices = gameGroups[gameName];
            const gameCard = this.createGameCard(gameName, prices);
            container.appendChild(gameCard);
        });
    }

    groupByGameName(results) {
        const groups = {};
        
        results.forEach(result => {
            if (!groups[result.name]) {
                groups[result.name] = [];
            }
            groups[result.name].push(result);
        });
        
        return groups;
    }

    createGameCard(gameName, prices) {
        const template = document.getElementById('price-result-template');
        const card = template.content.cloneNode(true);
        
        const gameNameElement = card.querySelector('.game-name');
        const priceSourcesContainer = card.querySelector('.price-sources');
        
        gameNameElement.textContent = gameName;
        
        // 按价格排序（从低到高）
        prices.sort((a, b) => a.price - b.price);
        
        prices.forEach(price => {
            const sourceItem = this.createPriceSourceItem(price);
            priceSourcesContainer.appendChild(sourceItem);
        });
        
        return card;
    }

    createPriceSourceItem(price) {
        const template = document.getElementById('price-source-template');
        const item = template.content.cloneNode(true);
        
        const sourceNameElement = item.querySelector('.source-name');
        const sourcePriceElement = item.querySelector('.source-price');
        const sourceUpdatedElement = item.querySelector('.source-updated');
        
        sourceNameElement.textContent = price.source;
        sourceNameElement.classList.add(price.source);
        
        sourcePriceElement.textContent = `¥${price.price.toFixed(2)}`;
        
        if (price.updated_at) {
            const date = new Date(price.updated_at);
            sourceUpdatedElement.textContent = `更新于 ${date.toLocaleString('zh-CN')}`;
        } else {
            sourceUpdatedElement.textContent = '更新时间未知';
        }
        
        return item;
    }

    async updateFhyxData() {
        const updateBtn = document.getElementById('update-fhyx-btn');
        if (!updateBtn) return;

        // 禁用按钮并显示加载状态
        updateBtn.disabled = true;
        updateBtn.innerHTML = '<svg class="icon spin" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="23 4 23 10 17 10"></polyline><polyline points="1 20 1 14 7 14"></polyline><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path></svg> 正在更新...';

        try {
            const response = await fetch('/api/price-comparison/update-fhyx', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                this.showStatus(result.message, 'success');
                
                // 延迟刷新统计信息
                setTimeout(() => {
                    this.loadStats();
                }, 2000);
            } else {
                this.showStatus(result.message || '更新失败', 'error');
            }
        } catch (error) {
            console.error('更新fhyx数据失败:', error);
            this.showStatus('更新fhyx数据失败，请稍后重试', 'error');
        } finally {
            // 恢复按钮状态
            updateBtn.disabled = false;
            updateBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><polyline points="23 4 23 10 17 10"></polyline><polyline points="1 20 1 14 7 14"></polyline><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path></svg> 更新fhyx数据';
        }
    }

    async migratePyData() {
        const migrateBtn = document.getElementById('migrate-py-btn');
        if (!migrateBtn) return;

        // 禁用按钮并显示加载状态
        migrateBtn.disabled = true;
        migrateBtn.innerHTML = '<svg class="icon spin" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="9" cy="9" r="2"></circle><path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path></svg> 正在迁移...';

        try {
            const response = await fetch('/api/price-comparison/migrate-py-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                this.showStatus(result.message, 'success');
                this.loadStats();
            } else {
                this.showStatus(result.message || '迁移失败', 'error');
            }
        } catch (error) {
            console.error('迁移py数据失败:', error);
            this.showStatus('迁移py数据失败，请稍后重试', 'error');
        } finally {
            // 恢复按钮状态
            migrateBtn.disabled = false;
            migrateBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="9" cy="9" r="2"></circle><path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path></svg> 迁移py数据';
        }
    }

    showLoading() {
        const container = document.getElementById('price-results-container');
        if (container) {
            container.innerHTML = '<div class="loading-spinner">正在搜索...</div>';
        }
    }

    hideLoading() {
        // Loading状态会被结果替换，无需特别处理
    }

    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('update-status');
        if (!statusElement) return;

        statusElement.className = `status-message ${type}`;
        statusElement.textContent = message;
        statusElement.style.display = 'block';

        // 3秒后隐藏状态消息
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 3000);
    }
}

// 添加旋转动画的CSS
const priceComparisonStyle = document.createElement('style');
priceComparisonStyle.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    .loading-spinner {
        text-align: center;
        padding: 2rem;
        color: var(--text-secondary);
    }
`;
document.head.appendChild(priceComparisonStyle);

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PriceComparison();
});

