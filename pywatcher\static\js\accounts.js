document.addEventListener('DOMContentLoaded', () => {
    // 移动端元素
    const phoneInput = document.getElementById('phone');
    const codeInput = document.getElementById('code');
    const sendSmsBtn = document.getElementById('send-sms-btn');
    const loginForm = document.getElementById('sms-login-form');
    const mobileAccountListContainer = document.getElementById('mobile-account-list-container');
    const refreshMobileBtn = document.getElementById('refresh-mobile-accounts-btn');

    // Web端功能已禁用
    // const webUsernameInput = document.getElementById('web-username');
    // const webPasswordInput = document.getElementById('web-password');
    // const webCaptchaInput = document.getElementById('web-captcha');
    // const webCaptchaImage = document.getElementById('web-captcha-image');
    // const getWebCaptchaBtn = document.getElementById('get-web-captcha-btn');
    // const webLoginForm = document.getElementById('web-login-form');
    // const webAccountListContainer = document.getElementById('web-account-list-container');
    // const refreshWebBtn = document.getElementById('refresh-web-accounts-btn');

    // Web验证码相关变量
    // let webCaptchaId = null;

    // 账号类型对照表 - 显示名称和CSS类
    const ACCOUNT_TYPES = {
        'query': { name: '查询账户', color: 'bg-info' },
        'order_primary': { name: '下单主账号', color: 'bg-success' },
        'order_secondary': { name: '下单副账号', color: 'bg-warning' },
        'cdk_query': { name: 'CDK专用查询', color: 'bg-primary' },
        'web_cdk_query': { name: 'Web CDK查询', color: 'bg-purple' },
        'backup': { name: '备用账号', color: 'bg-secondary' }
    };

    // Define SVGs for dynamic injection
    const loaderSVG = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-loader spin"><line x1="12" y1="2" x2="12" y2="6"></line><line x1="12" y1="18" x2="12" y2="22"></line><line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line><line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line><line x1="2" y1="12" x2="6" y2="12"></line><line x1="18" y1="12" x2="22" y2="12"></line><line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line><line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line></svg>`;
    const alertCircleSVG = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-alert-circle"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>`;
    const trash2SVG = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>`;
    const settingsSVG = `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-settings"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>`;

    // --- 添加在页面上全局可访问的方法 ---
    // 使用window对象使这些函数在HTML中可调用
    window.deleteMobileAccount = deleteMobileAccount;
    window.setAccountType = setAccountType;
    window.showAccountTypeMenu = showAccountTypeMenu;

    // --- Notifications ---
    function showToast(message, isError = false) {
        const toast = document.createElement('div');
        toast.className = `toast ${isError ? 'error' : ''}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        // Trigger the animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Remove the toast after 3 seconds
        setTimeout(() => {
            toast.classList.remove('show');
            toast.addEventListener('transitionend', () => toast.remove());
        }, 3000);
    }

    // 显示加载器
    function showLoader() {
        const loader = document.createElement('div');
        loader.id = 'global-loader';
        loader.innerHTML = loaderSVG;
        document.body.appendChild(loader);
    }

    // 隐藏加载器
    function hideLoader() {
        const loader = document.getElementById('global-loader');
        if (loader) loader.remove();
    }

    // --- API Calls ---
    async function fetchAPI(url, options = {}) {
        try {
            const response = await fetch(url, options);
            const data = await response.json();
            if (!response.ok) {
                throw new Error(data.detail || `HTTP error! status: ${response.status}`);
            }
            return data;
        } catch (error) {
            console.error('API Error:', error);
            showToast(error.message, true);
            throw error;
        }
    }

    async function fetchAndRenderMobileAccounts() {
        if (!mobileAccountListContainer) return;
        mobileAccountListContainer.innerHTML = `<div class="loading-indicator">${loaderSVG} 正在加载账户...</div>`;
        
        try {
            const response = await fetch('/api/accounts');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const accounts = await response.json();

            if (accounts.length === 0) {
                mobileAccountListContainer.innerHTML = '<p class="text-muted">尚未添加任何移动账户。</p>';
                return;
            }

            const ul = document.createElement('ul');
            accounts.forEach(account => {
                const li = document.createElement('li');
                const accountType = ACCOUNT_TYPES[account.account_type] || ACCOUNT_TYPES.backup;
                
                li.innerHTML = `
                    <div class="account-info">
                        <strong>${account.name || '未知账户'}</strong>
                        <div class="account-badges">
                            <span class="badge ${accountType.color}">${accountType.name}</span>
                        </div>
                        <span class="text-muted">ID: ${account.id || 'N/A'}</span>
                    </div>
                    <div class="account-actions">
                        <button class="outline icon-btn" data-id="${account.id}" onclick="showAccountTypeMenu('${account.id}')" title="设置账户类型">
                            ${settingsSVG}
                        </button>
                        <button class="outline icon-btn" data-id="${account.id}" onclick="deleteMobileAccount('${account.id}')" title="删除">
                            ${trash2SVG}
                        </button>
                    </div>
                    <div id="type-menu-${account.id}" class="account-type-menu" style="display:none;">
                        <div class="menu-header">选择账户类型</div>
                        <div class="menu-options">
                            <button onclick="setAccountType('${account.id}', 'query')">查询账户</button>
                            <button onclick="setAccountType('${account.id}', 'order_primary')">下单主账号</button>
                            <button onclick="setAccountType('${account.id}', 'order_secondary')">下单副账号</button>
                            <button onclick="setAccountType('${account.id}', 'cdk_query')">CDK专用查询</button>
                            <button onclick="setAccountType('${account.id}', 'backup')">备用账号</button>
                        </div>
                    </div>
                `;
                ul.appendChild(li);
            });
            mobileAccountListContainer.innerHTML = '';
            mobileAccountListContainer.appendChild(ul);
        } catch (error) {
            console.error('无法加载移动账户:', error);
            mobileAccountListContainer.innerHTML = `<div class="error-indicator">${alertCircleSVG} 加载失败，请重试。</div>`;
        }
    }

    async function deleteMobileAccount(accountId) {
        if (!confirm('确定要删除这个账户吗？')) {
            return;
        }
        showLoader();
        try {
            const response = await fetch(`/api/accounts/${accountId}`, {
                method: 'DELETE',
            });
            
            if (response.ok) {
                showToast('账户删除成功');
                fetchAndRenderMobileAccounts(); // 刷新列表
            } else {
                const result = await response.json().catch(() => ({}));
                throw new Error(result.detail || '删除失败');
            }
        } catch (error) {
            showToast(`删除失败: ${error.message}`, true);
        } finally {
            hideLoader();
        }
    }
    
    // 设置账户类型
    async function setAccountType(accountId, accountType) {
        showLoader();
        try {
            const result = await fetchAPI(`/api/accounts/${accountId}/set-type`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ account_type: accountType })
            });
            showToast(result.message || '账户类型已更新');
            
            // 隐藏菜单
            const menu = document.getElementById(`type-menu-${accountId}`);
            if (menu) menu.style.display = 'none';
            
            fetchAndRenderMobileAccounts(); // 刷新列表
        } catch (error) {
            // Error toast is already shown by fetchAPI
        } finally {
            hideLoader();
        }
    }
    
    // 显示账户类型选择菜单
    function showAccountTypeMenu(accountId) {
        // 获取目标菜单和触发按钮
        const menu = document.getElementById(`type-menu-${accountId}`);
        const button = document.querySelector(`button[data-id="${accountId}"][onclick*="showAccountTypeMenu"]`);
        if (!menu || !button) return;
        
        // 切换菜单显示状态
        const isVisible = menu.style.display === 'block';
        
        // 先关闭所有打开的菜单
        document.querySelectorAll('.account-type-menu').forEach(m => {
            m.style.display = 'none';
            // 重置位置样式
            m.style.top = '';
            m.style.left = '';
            m.style.right = '';
            m.style.bottom = '';
        });
        
        // 如果菜单原来是隐藏的，则显示它
        if (!isVisible) {
            // 首先显示菜单（但在屏幕外）来获取其尺寸
            menu.style.display = 'block';
            menu.style.visibility = 'hidden';
            
            // 获取按钮位置
            const buttonRect = button.getBoundingClientRect();
            const menuRect = menu.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;
            
            // 计算菜单位置
            let top = buttonRect.bottom + 5; // 按钮下方5px
            let right = viewportWidth - buttonRect.right; // 与按钮右对齐
            
            // 检查是否超出底部边界
            if (top + menuRect.height > viewportHeight - 10) {
                // 如果超出底部，显示在按钮上方
                top = buttonRect.top - menuRect.height - 5;
            }
            
            // 检查是否超出右边界
            if (buttonRect.right - menuRect.width < 10) {
                // 如果超出右边，调整到左对齐
                right = viewportWidth - buttonRect.left - menuRect.width;
            }
            
            // 确保不超出顶部
            if (top < 10) {
                top = 10;
            }
            
            // 应用计算出的位置
            menu.style.top = `${top}px`;
            menu.style.right = `${right}px`;
            menu.style.visibility = 'visible';
            
            // 立即添加一个点击监听器到document上
            setTimeout(() => {
                const closeMenuListener = function(e) {
                    // 如果点击的不是菜单本身或触发按钮
                    if (!menu.contains(e.target) && !e.target.matches(`button[data-id="${accountId}"]`)) {
                        menu.style.display = 'none';
                        document.removeEventListener('click', closeMenuListener);
                    }
                };
                
                document.addEventListener('click', closeMenuListener);
            }, 0);
        }
    }

    // --- Event Handlers ---
    sendSmsBtn.addEventListener('click', async () => {
        const phone = phoneInput.value;
        if (!phone) {
            showToast('请输入手机号码。', true);
            return;
        }

        sendSmsBtn.disabled = true;
        sendSmsBtn.textContent = '发送中...';

        try {
            const result = await fetchAPI('/api/login/sms', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ phone }),
            });
            showToast(result.message || '验证码已发送。');
        } finally {
            sendSmsBtn.disabled = false;
            sendSmsBtn.textContent = '发送验证码';
        }
    });

    loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const phone = phoneInput.value;
        const code = codeInput.value;

        if (!phone || !code) {
            showToast('手机号和验证码不能为空。', true);
            return;
        }

        try {
            await fetchAPI('/api/accounts/login', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ phone, code, remark: `用户-${phone.slice(-4)}` }),
            });
            showToast('账户添加成功！');
            phoneInput.value = '';
            codeInput.value = '';
            fetchAndRenderMobileAccounts(); // Refresh the list
        } catch (error) {
            // Error toast is already shown by fetchAPI
        }
    });
    
    if (refreshMobileBtn) {
        refreshMobileBtn.addEventListener('click', fetchAndRenderMobileAccounts);
    }

    // --- Initial Load ---
    fetchAndRenderMobileAccounts();

    // 添加注册相关的DOM元素和事件处理
    const registerBtn = document.getElementById('register-btn');
    const registerModal = document.getElementById('register-modal');
    const getCaptchaBtn = document.getElementById('get-captcha-btn');
    const sendRegisterSmsBtn = document.getElementById('send-register-sms-btn');
    const registerForm = document.getElementById('register-form');

    let currentCaptchaId = null;

    // 显示注册模态框
    registerBtn?.addEventListener('click', () => {
        registerModal.style.display = 'block';
        // 重置表单状态
        document.getElementById('captcha-container').style.display = 'none';
        document.getElementById('sms-code-container').style.display = 'none';
        currentCaptchaId = null;
    });

    // 点击模态框外部关闭
    registerModal?.addEventListener('click', (e) => {
        if (e.target === registerModal) {
            registerModal.style.display = 'none';
        }
    });

    // 获取图形验证码
    getCaptchaBtn?.addEventListener('click', async () => {
        try {
            const response = await fetchAPI('/api/accounts/register/captcha');
            currentCaptchaId = response.captcha_id;
            document.getElementById('captcha-image').src = response.captcha_image;
            document.getElementById('captcha-container').style.display = 'block';
        } catch (error) {
            showToast('获取验证码失败', true);
        }
    });

    // 发送注册短信
    sendRegisterSmsBtn?.addEventListener('click', async () => {
        const phone = document.getElementById('register-phone').value;
        const captchaCode = document.getElementById('captcha-code').value;
        
        if (!phone || !captchaCode || !currentCaptchaId) {
            showToast('请填写完整信息', true);
            return;
        }
        
        try {
            await fetchAPI('/api/accounts/register/sms', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    phone,
                    captcha_id: currentCaptchaId,
                    captcha_code: captchaCode
                })
            });
            showToast('注册短信发送成功');
            document.getElementById('sms-code-container').style.display = 'block';
        } catch (error) {
            // Error already handled by fetchAPI
        }
    });

    // 注册表单提交
    registerForm?.addEventListener('submit', async (e) => {
        e.preventDefault();
        const phone = document.getElementById('register-phone').value;
        const smsCode = document.getElementById('register-sms-code').value;
        const remark = document.getElementById('register-remark').value;
        
        if (!phone || !smsCode) {
            showToast('手机号和短信验证码不能为空', true);
            return;
        }
        
        try {
            await fetchAPI('/api/accounts/register', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ phone, sms_code: smsCode, remark })
            });
            showToast('账户注册成功！');
            registerModal.style.display = 'none';
            fetchAndRenderMobileAccounts();
        } catch (error) {
            // Error already handled by fetchAPI
        }
    });

    // =========================
    // Web账户池功能已禁用
    // =========================

    // 初始化页面
    fetchAndRenderMobileAccounts();
}); 



